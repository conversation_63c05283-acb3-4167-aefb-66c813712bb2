<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组织架构管理 - 主数据平台 - 数字工厂一体化平台</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">组织架构管理</h1>
            <p class="text-gray-600">统一管理部门结构、岗位设置、汇报关系、权限分配等组织架构信息</p>
        </div>

        <!-- 组织架构统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-indigo-600">15</div>
                        <div class="text-sm text-gray-600">部门数量</div>
                        <div class="text-xs text-gray-500">组织单元</div>
                    </div>
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-sitemap text-indigo-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-blue-600">45</div>
                        <div class="text-sm text-gray-600">岗位设置</div>
                        <div class="text-xs text-gray-500">职位配置</div>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-tie text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-green-600">245</div>
                        <div class="text-sm text-gray-600">员工总数</div>
                        <div class="text-xs text-gray-500">在职人员</div>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-purple-600">5</div>
                        <div class="text-sm text-gray-600">管理层级</div>
                        <div class="text-xs text-gray-500">组织深度</div>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-layer-group text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 组织架构管理功能选项卡 -->
        <div class="mb-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8">
                    <button onclick="showTab('structure')" class="tab-button border-b-2 border-indigo-500 text-indigo-600 py-2 px-1 text-sm font-medium" id="structure-tab">
                        组织架构图
                    </button>
                    <button onclick="showTab('departments')" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 text-sm font-medium" id="departments-tab">
                        部门管理
                    </button>
                    <button onclick="showTab('positions')" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 text-sm font-medium" id="positions-tab">
                        岗位管理
                    </button>
                    <button onclick="showTab('permissions')" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 text-sm font-medium" id="permissions-tab">
                        权限分配
                    </button>
                </nav>
            </div>
        </div>

        <!-- 组织架构图 -->
        <div id="structure-content" class="tab-content">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-800">组织架构图</h3>
                    <div class="flex space-x-2">
                        <button class="bg-primary text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700" onclick="editStructure()">
                            <i class="fas fa-edit mr-2"></i>编辑架构
                        </button>
                        <button class="bg-secondary text-white px-4 py-2 rounded-md text-sm hover:bg-gray-700" onclick="exportStructure()">
                            <i class="fas fa-download mr-2"></i>导出图表
                        </button>
                    </div>
                </div>
                
                <!-- 组织架构树形图 -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <div class="text-center mb-6">
                        <div class="inline-block bg-indigo-100 rounded-lg p-4">
                            <div class="flex items-center">
                                <i class="fas fa-building text-indigo-600 text-2xl mr-3"></i>
                                <div>
                                    <div class="text-lg font-bold text-indigo-800">数字工厂一体化平台</div>
                                    <div class="text-sm text-gray-600">总经理: 张总</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 第二层 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <div class="text-center">
                            <div class="bg-blue-100 rounded-lg p-4">
                                <div class="flex items-center justify-center">
                                    <i class="fas fa-cogs text-blue-600 text-xl mr-2"></i>
                                    <div>
                                        <div class="font-semibold text-blue-800">生产副总</div>
                                        <div class="text-sm text-gray-600">李副总</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="bg-green-100 rounded-lg p-4">
                                <div class="flex items-center justify-center">
                                    <i class="fas fa-chart-line text-green-600 text-xl mr-2"></i>
                                    <div>
                                        <div class="font-semibold text-green-800">技术副总</div>
                                        <div class="text-sm text-gray-600">王副总</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="bg-purple-100 rounded-lg p-4">
                                <div class="flex items-center justify-center">
                                    <i class="fas fa-users text-purple-600 text-xl mr-2"></i>
                                    <div>
                                        <div class="font-semibold text-purple-800">管理副总</div>
                                        <div class="text-sm text-gray-600">赵副总</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 第三层 - 部门 -->
                    <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
                        <div class="bg-white rounded-lg p-3 border border-blue-200">
                            <div class="text-center">
                                <i class="fas fa-industry text-blue-600 mb-2"></i>
                                <div class="font-medium text-blue-800">生产部</div>
                                <div class="text-xs text-gray-600">85人</div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg p-3 border border-red-200">
                            <div class="text-center">
                                <i class="fas fa-check-circle text-red-600 mb-2"></i>
                                <div class="font-medium text-red-800">质量部</div>
                                <div class="text-xs text-gray-600">32人</div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg p-3 border border-orange-200">
                            <div class="text-center">
                                <i class="fas fa-truck text-orange-600 mb-2"></i>
                                <div class="font-medium text-orange-800">物流部</div>
                                <div class="text-xs text-gray-600">45人</div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg p-3 border border-green-200">
                            <div class="text-center">
                                <i class="fas fa-laptop-code text-green-600 mb-2"></i>
                                <div class="font-medium text-green-800">技术部</div>
                                <div class="text-xs text-gray-600">28人</div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg p-3 border border-purple-200">
                            <div class="text-center">
                                <i class="fas fa-briefcase text-purple-600 mb-2"></i>
                                <div class="font-medium text-purple-800">管理部</div>
                                <div class="text-xs text-gray-600">15人</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 部门管理 -->
        <div id="departments-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-800">部门管理</h3>
                        <button class="bg-primary text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700" onclick="addDepartment()">
                            <i class="fas fa-plus mr-2"></i>新增部门
                        </button>
                    </div>
                </div>

                <!-- 部门数据表格 -->
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">部门信息</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">上级部门</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">部门负责人</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">人员数量</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成立时间</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-industry text-blue-600"></i>
                                        </div>
                                        <div class="ml-3">
                                            <div class="text-sm font-medium text-gray-900">生产部</div>
                                            <div class="text-xs text-gray-500">编号: DEPT001</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm text-gray-900">生产副总</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">张主管</div>
                                    <div class="text-xs text-gray-500">13800138001</div>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm text-gray-900">85人</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm text-gray-900">2020-01-15</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        正常
                                    </span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-600 hover:text-blue-900">编辑</button>
                                        <button class="text-green-600 hover:text-green-900">查看</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-check-circle text-red-600"></i>
                                        </div>
                                        <div class="ml-3">
                                            <div class="text-sm font-medium text-gray-900">质量部</div>
                                            <div class="text-xs text-gray-500">编号: DEPT002</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm text-gray-900">技术副总</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">李主管</div>
                                    <div class="text-xs text-gray-500">13800138002</div>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm text-gray-900">32人</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm text-gray-900">2020-01-15</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        正常
                                    </span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-600 hover:text-blue-900">编辑</button>
                                        <button class="text-green-600 hover:text-green-900">查看</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-truck text-orange-600"></i>
                                        </div>
                                        <div class="ml-3">
                                            <div class="text-sm font-medium text-gray-900">物流部</div>
                                            <div class="text-xs text-gray-500">编号: DEPT003</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm text-gray-900">生产副总</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">王主管</div>
                                    <div class="text-xs text-gray-500">13800138003</div>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm text-gray-900">45人</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm text-gray-900">2020-01-15</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        正常
                                    </span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-600 hover:text-blue-900">编辑</button>
                                        <button class="text-green-600 hover:text-green-900">查看</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 岗位管理 -->
        <div id="positions-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-800">岗位管理</h3>
                    <button class="bg-primary text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700" onclick="addPosition()">
                        <i class="fas fa-plus mr-2"></i>新增岗位
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- 生产主管 -->
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-gray-800">生产主管</h4>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">管理</span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">所属部门:</span>
                                <span class="text-gray-900">生产部</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">在职人数:</span>
                                <span class="text-gray-900">5人</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">薪资等级:</span>
                                <span class="text-gray-900">M3</span>
                            </div>
                        </div>
                        <div class="mt-3 flex space-x-2">
                            <button class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded hover:bg-blue-200">查看详情</button>
                            <button class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded hover:bg-gray-200">编辑</button>
                        </div>
                    </div>

                    <!-- 质量工程师 -->
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-gray-800">质量工程师</h4>
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">技术</span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">所属部门:</span>
                                <span class="text-gray-900">质量部</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">在职人数:</span>
                                <span class="text-gray-900">12人</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">薪资等级:</span>
                                <span class="text-gray-900">T2</span>
                            </div>
                        </div>
                        <div class="mt-3 flex space-x-2">
                            <button class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded hover:bg-green-200">查看详情</button>
                            <button class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded hover:bg-gray-200">编辑</button>
                        </div>
                    </div>

                    <!-- 操作员 -->
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-gray-800">操作员</h4>
                            <span class="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full">操作</span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">所属部门:</span>
                                <span class="text-gray-900">生产部</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">在职人数:</span>
                                <span class="text-gray-900">68人</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">薪资等级:</span>
                                <span class="text-gray-900">O1</span>
                            </div>
                        </div>
                        <div class="mt-3 flex space-x-2">
                            <button class="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded hover:bg-orange-200">查看详情</button>
                            <button class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded hover:bg-gray-200">编辑</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 权限分配 -->
        <div id="permissions-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">权限分配管理</h3>
                <div class="space-y-4">
                    <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-blue-800">生产管理权限</div>
                                <div class="text-xs text-gray-600">包含工单管理、生产调度、设备操作等权限</div>
                                <div class="text-xs text-gray-500">分配给: 生产主管、生产经理</div>
                            </div>
                            <div class="text-right">
                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">已分配</span>
                                <div class="text-xs text-gray-500 mt-1">8个岗位</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-green-50 p-4 rounded-lg border-l-4 border-green-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-green-800">质量管理权限</div>
                                <div class="text-xs text-gray-600">包含质量检验、不合格处理、质量报告等权限</div>
                                <div class="text-xs text-gray-500">分配给: 质量工程师、质量主管</div>
                            </div>
                            <div class="text-right">
                                <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">已分配</span>
                                <div class="text-xs text-gray-500 mt-1">5个岗位</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-orange-50 p-4 rounded-lg border-l-4 border-orange-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-orange-800">物流管理权限</div>
                                <div class="text-xs text-gray-600">包含库存管理、配送调度、物料跟踪等权限</div>
                                <div class="text-xs text-gray-500">分配给: 物流主管、仓库管理员</div>
                            </div>
                            <div class="text-right">
                                <span class="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">已分配</span>
                                <div class="text-xs text-gray-500 mt-1">6个岗位</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-purple-50 p-4 rounded-lg border-l-4 border-purple-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-purple-800">系统管理权限</div>
                                <div class="text-xs text-gray-600">包含用户管理、系统配置、数据维护等权限</div>
                                <div class="text-xs text-gray-500">分配给: 系统管理员、IT主管</div>
                            </div>
                            <div class="text-right">
                                <span class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">已分配</span>
                                <div class="text-xs text-gray-500 mt-1">3个岗位</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示选项卡
        function showTab(tabName) {
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // 重置所有选项卡样式
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('border-indigo-500', 'text-indigo-600');
                button.classList.add('border-transparent', 'text-gray-500');
            });
            
            // 显示选中的内容
            document.getElementById(tabName + '-content').classList.remove('hidden');
            
            // 设置选中的选项卡样式
            const activeTab = document.getElementById(tabName + '-tab');
            activeTab.classList.remove('border-transparent', 'text-gray-500');
            activeTab.classList.add('border-indigo-500', 'text-indigo-600');
        }

        // 操作函数
        function editStructure() {
            alert('编辑组织架构功能');
        }

        function exportStructure() {
            alert('导出组织架构图功能');
        }

        function addDepartment() {
            alert('新增部门功能');
        }

        function addPosition() {
            alert('新增岗位功能');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            showTab('structure');
        });
    </script>
</body>
</html>
