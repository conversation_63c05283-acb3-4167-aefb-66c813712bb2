<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>库存预警管理 - 数字工厂平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../../assets/css/custom.css">
</head>
<body class="bg-gray-50">
    <div class="p-6 space-y-6">
        <!-- 页面操作栏 -->
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-xl font-bold text-gray-800">库存预警管理</h1>
                <p class="text-gray-600">监控备件库存水平，设置预警规则和自动补货提醒</p>
            </div>
            <div class="flex space-x-3">
                <button class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                    <i class="fas fa-exclamation-triangle mr-2"></i>紧急补货
                </button>
                <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-cog mr-2"></i>预警设置
                </button>
                <button class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                    <i class="fas fa-chart-line mr-2"></i>库存分析
                </button>
            </div>
        </div>
        
        <!-- 预警概览统计 -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div class="card text-center">
                <div class="text-2xl font-bold text-red-600 mb-1">18</div>
                <div class="text-sm text-gray-600">紧急预警</div>
                <div class="text-xs text-red-600 mt-1">
                    <i class="fas fa-exclamation-triangle mr-1"></i>需立即处理
                </div>
            </div>
            <div class="card text-center">
                <div class="text-2xl font-bold text-yellow-600 mb-1">35</div>
                <div class="text-sm text-gray-600">低库存预警</div>
                <div class="text-xs text-yellow-600 mt-1">建议补货</div>
            </div>
            <div class="card text-center">
                <div class="text-2xl font-bold text-blue-600 mb-1">8</div>
                <div class="text-sm text-gray-600">超储预警</div>
                <div class="text-xs text-blue-600 mt-1">库存过多</div>
            </div>
            <div class="card text-center">
                <div class="text-2xl font-bold text-green-600 mb-1">1,195</div>
                <div class="text-sm text-gray-600">正常库存</div>
                <div class="text-xs text-green-600 mt-1">
                    <i class="fas fa-check mr-1"></i>库存健康
                </div>
            </div>
            <div class="card text-center">
                <div class="text-2xl font-bold text-purple-600 mb-1">95.2%</div>
                <div class="text-sm text-gray-600">预警准确率</div>
                <div class="text-xs text-green-600 mt-1">
                    <i class="fas fa-arrow-up mr-1"></i>+2.1% 较上月
                </div>
            </div>
        </div>
        
        <!-- 筛选条件区域 -->
        <div class="card">
            <div class="p-4">
                <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                    <div class="flex-1">
                        <input type="text" placeholder="搜索备件编码、名称..."
                               class="w-full border border-gray-300 rounded-lg px-4 py-2">
                    </div>
                    <select class="border border-gray-300 rounded-lg px-3 py-2">
                        <option>全部预警</option>
                        <option>紧急预警</option>
                        <option>低库存</option>
                        <option>超储预警</option>
                        <option>正常库存</option>
                    </select>
                    <select class="border border-gray-300 rounded-lg px-3 py-2">
                        <option>全部分类</option>
                        <option>电气类</option>
                        <option>机械类</option>
                        <option>液压类</option>
                        <option>易耗品</option>
                    </select>
                    <select class="border border-gray-300 rounded-lg px-3 py-2">
                        <option>全部供应商</option>
                        <option>供应商A</option>
                        <option>供应商B</option>
                        <option>供应商C</option>
                    </select>
                    <select class="border border-gray-300 rounded-lg px-3 py-2">
                        <option>全部仓库</option>
                        <option>主仓库</option>
                        <option>备件仓</option>
                        <option>临时仓</option>
                    </select>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 库存预警列表 -->
        <div class="card">
            <div class="card-header">
                <div class="flex justify-between items-center">
                    <h3 class="card-title">库存预警列表</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 bg-blue-600 text-white rounded text-sm">
                            <i class="fas fa-list"></i>
                        </button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">
                            <i class="fas fa-th"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>备件编码</th>
                            <th>备件名称</th>
                            <th>当前库存</th>
                            <th>安全库存</th>
                            <th>最大库存</th>
                            <th>预警级别</th>
                            <th>库存状态</th>
                            <th>建议补货量</th>
                            <th>最后更新</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="bg-red-50">
                            <td class="font-medium">SP-E001</td>
                            <td>温度传感器 TS-350</td>
                            <td class="font-semibold text-red-600">2</td>
                            <td>10</td>
                            <td>50</td>
                            <td><span class="status-indicator status-danger">紧急</span></td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="progress-bar w-16">
                                        <div class="progress-fill bg-red-500" style="width: 4%"></div>
                                    </div>
                                    <span class="text-sm text-red-600">4%</span>
                                </div>
                            </td>
                            <td class="font-semibold text-blue-600">48</td>
                            <td>2024-06-28 16:30</td>
                            <td>
                                <div class="flex space-x-1">
                                    <button class="text-red-600 hover:text-red-800" title="紧急补货">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </button>
                                    <button class="text-blue-600 hover:text-blue-800" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-800" title="申请采购">
                                        <i class="fas fa-shopping-cart"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>

                        <tr class="bg-red-50">
                            <td class="font-medium">SP-H002</td>
                            <td>液压油滤芯 HF-200</td>
                            <td class="font-semibold text-red-600">1</td>
                            <td>15</td>
                            <td>60</td>
                            <td><span class="status-indicator status-danger">紧急</span></td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="progress-bar w-16">
                                        <div class="progress-fill bg-red-500" style="width: 2%"></div>
                                    </div>
                                    <span class="text-sm text-red-600">2%</span>
                                </div>
                            </td>
                            <td class="font-semibold text-blue-600">59</td>
                            <td>2024-06-28 15:45</td>
                            <td>
                                <div class="flex space-x-1">
                                    <button class="text-red-600 hover:text-red-800" title="紧急补货">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </button>
                                    <button class="text-blue-600 hover:text-blue-800" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-800" title="申请采购">
                                        <i class="fas fa-shopping-cart"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>

                        <tr class="bg-yellow-50">
                            <td class="font-medium">SP-M003</td>
                            <td>轴承 6205-2RS</td>
                            <td class="font-semibold text-yellow-600">18</td>
                            <td>20</td>
                            <td>80</td>
                            <td><span class="status-indicator status-warning">低库存</span></td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="progress-bar w-16">
                                        <div class="progress-fill bg-yellow-500" style="width: 23%"></div>
                                    </div>
                                    <span class="text-sm text-yellow-600">23%</span>
                                </div>
                            </td>
                            <td class="font-semibold text-blue-600">62</td>
                            <td>2024-06-28 14:20</td>
                            <td>
                                <div class="flex space-x-1">
                                    <button class="text-yellow-600 hover:text-yellow-800" title="补货提醒">
                                        <i class="fas fa-bell"></i>
                                    </button>
                                    <button class="text-blue-600 hover:text-blue-800" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-800" title="申请采购">
                                        <i class="fas fa-shopping-cart"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>

                        <tr class="bg-yellow-50">
                            <td class="font-medium">SP-P004</td>
                            <td>气压阀门 PV-100</td>
                            <td class="font-semibold text-yellow-600">8</td>
                            <td>12</td>
                            <td>40</td>
                            <td><span class="status-indicator status-warning">低库存</span></td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="progress-bar w-16">
                                        <div class="progress-fill bg-yellow-500" style="width: 20%"></div>
                                    </div>
                                    <span class="text-sm text-yellow-600">20%</span>
                                </div>
                            </td>
                            <td class="font-semibold text-blue-600">32</td>
                            <td>2024-06-28 13:15</td>
                            <td>
                                <div class="flex space-x-1">
                                    <button class="text-yellow-600 hover:text-yellow-800" title="补货提醒">
                                        <i class="fas fa-bell"></i>
                                    </button>
                                    <button class="text-blue-600 hover:text-blue-800" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-800" title="申请采购">
                                        <i class="fas fa-shopping-cart"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>

                        <tr class="bg-blue-50">
                            <td class="font-medium">SP-C005</td>
                            <td>润滑油 LUB-46</td>
                            <td class="font-semibold text-blue-600">125</td>
                            <td>30</td>
                            <td>100</td>
                            <td><span class="status-indicator status-info">超储</span></td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="progress-bar w-16">
                                        <div class="progress-fill bg-blue-500" style="width: 100%"></div>
                                    </div>
                                    <span class="text-sm text-blue-600">125%</span>
                                </div>
                            </td>
                            <td class="font-semibold text-red-600">-25</td>
                            <td>2024-06-28 12:30</td>
                            <td>
                                <div class="flex space-x-1">
                                    <button class="text-blue-600 hover:text-blue-800" title="库存调配">
                                        <i class="fas fa-exchange-alt"></i>
                                    </button>
                                    <button class="text-blue-600 hover:text-blue-800" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-purple-600 hover:text-purple-800" title="调整库存">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="flex justify-between items-center mt-4 pt-4 border-t border-gray-200">
                <div class="text-sm text-gray-600">
                    显示 1-5 条，共 61 条预警记录
                </div>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">上一页</button>
                    <button class="px-3 py-1 bg-blue-600 text-white rounded text-sm">1</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">2</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">3</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">下一页</button>
                </div>
            </div>
        </div>
        
        <!-- 预警趋势和设置 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 预警趋势分析 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">预警趋势分析（近30天）</h3>
                </div>
                <div class="p-4">
                    <div class="grid grid-cols-6 gap-2 text-xs text-center mb-4">
                        <div>
                            <div class="h-16 bg-gray-100 rounded mb-2 flex items-end">
                                <div class="w-full bg-red-500 rounded" style="height: 45%;"></div>
                            </div>
                            <div class="text-gray-600">第1周</div>
                            <div class="text-red-600 font-semibold">22</div>
                        </div>
                        <div>
                            <div class="h-16 bg-gray-100 rounded mb-2 flex items-end">
                                <div class="w-full bg-red-500 rounded" style="height: 38%;"></div>
                            </div>
                            <div class="text-gray-600">第2周</div>
                            <div class="text-red-600 font-semibold">19</div>
                        </div>
                        <div>
                            <div class="h-16 bg-gray-100 rounded mb-2 flex items-end">
                                <div class="w-full bg-red-500 rounded" style="height: 42%;"></div>
                            </div>
                            <div class="text-gray-600">第3周</div>
                            <div class="text-red-600 font-semibold">21</div>
                        </div>
                        <div>
                            <div class="h-16 bg-gray-100 rounded mb-2 flex items-end">
                                <div class="w-full bg-red-500 rounded" style="height: 35%;"></div>
                            </div>
                            <div class="text-gray-600">第4周</div>
                            <div class="text-red-600 font-semibold">18</div>
                        </div>
                        <div>
                            <div class="h-16 bg-gray-100 rounded mb-2 flex items-end">
                                <div class="w-full bg-green-500 rounded" style="height: 30%;"></div>
                            </div>
                            <div class="text-gray-600">本周</div>
                            <div class="text-green-600 font-semibold">15</div>
                        </div>
                        <div>
                            <div class="h-16 bg-gray-100 rounded mb-2 flex items-end">
                                <div class="w-full bg-gray-300 rounded" style="height: 25%;"></div>
                            </div>
                            <div class="text-gray-600">预测</div>
                            <div class="text-gray-600 font-semibold">12</div>
                        </div>
                    </div>
                    
                    <div class="space-y-2 text-xs">
                        <div class="flex justify-between">
                            <span class="text-gray-600">平均预警数：</span>
                            <span class="font-semibold">19个/周</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">预警下降趋势：</span>
                            <span class="font-semibold text-green-600">-21%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">处理及时率：</span>
                            <span class="font-semibold text-green-600">95.2%</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 预警设置 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">预警规则设置</h3>
                </div>
                <div class="p-4 space-y-4">
                    <div class="p-3 bg-red-50 border border-red-200 rounded-lg">
                        <div class="font-medium text-red-700 mb-2">紧急预警规则</div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span>触发条件：</span>
                                <span>库存 ≤ 安全库存 × 20%</span>
                            </div>
                            <div class="flex justify-between">
                                <span>通知方式：</span>
                                <span>短信 + 邮件 + 系统推送</span>
                            </div>
                            <div class="flex justify-between">
                                <span>通知对象：</span>
                                <span>仓库主管 + 采购经理</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div class="font-medium text-yellow-700 mb-2">低库存预警规则</div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span>触发条件：</span>
                                <span>库存 ≤ 安全库存</span>
                            </div>
                            <div class="flex justify-between">
                                <span>通知方式：</span>
                                <span>邮件 + 系统推送</span>
                            </div>
                            <div class="flex justify-between">
                                <span>通知对象：</span>
                                <span>仓库管理员</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div class="font-medium text-blue-700 mb-2">超储预警规则</div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span>触发条件：</span>
                                <span>库存 ≥ 最大库存</span>
                            </div>
                            <div class="flex justify-between">
                                <span>通知方式：</span>
                                <span>系统推送</span>
                            </div>
                            <div class="flex justify-between">
                                <span>通知对象：</span>
                                <span>仓库主管</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                            <i class="fas fa-cog mr-2"></i>修改预警设置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 按预警级别筛选
        function filterByLevel(level) {
            console.log('筛选预警级别:', level);
            // 这里可以添加筛选逻辑
        }
    </script>
</body>
</html>
