<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资产盘点管理 - 慧新全智厂园一体平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-clipboard-list text-primary mr-3"></i>
                资产盘点管理
            </h1>
            <p class="text-gray-600 mt-2">系统化资产盘点，确保资产账实相符</p>
        </div>

        <!-- 盘点统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">盘点完成率</h3>
                        <p class="text-3xl font-bold text-green-600 mt-2">78%</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>已盘点:</span>
                        <span class="text-green-600 font-medium">974台</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>待盘点:</span>
                        <span class="text-orange-600 font-medium">274台</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">盘点差异</h3>
                        <p class="text-3xl font-bold text-red-600 mt-2">12</p>
                    </div>
                    <div class="bg-red-100 p-3 rounded-full">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>盘盈:</span>
                        <span class="text-green-600 font-medium">5台</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>盘亏:</span>
                        <span class="text-red-600 font-medium">7台</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">盘点人员</h3>
                        <p class="text-3xl font-bold text-blue-600 mt-2">15</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-users text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>在线:</span>
                        <span class="text-green-600 font-medium">12人</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>离线:</span>
                        <span class="text-gray-600 font-medium">3人</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">预计完成</h3>
                        <p class="text-3xl font-bold text-purple-600 mt-2">3天</p>
                    </div>
                    <div class="bg-purple-100 p-3 rounded-full">
                        <i class="fas fa-calendar-alt text-purple-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>计划:</span>
                        <span class="text-blue-600 font-medium">5天</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>提前:</span>
                        <span class="text-green-600 font-medium">2天</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 盘点任务管理 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-tasks text-blue-600 mr-2"></i>
                盘点任务管理
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-200">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-semibold text-gray-800">A区盘点</h4>
                        <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">已完成</span>
                    </div>
                    <div class="text-sm text-gray-600 space-y-2">
                        <div class="flex justify-between">
                            <span>负责人:</span>
                            <span class="font-medium text-green-600">张主管</span>
                        </div>
                        <div class="flex justify-between">
                            <span>资产数量:</span>
                            <span class="font-medium">356台</span>
                        </div>
                        <div class="flex justify-between">
                            <span>完成进度:</span>
                            <span class="font-medium text-green-600">100%</span>
                        </div>
                        <div class="flex justify-between">
                            <span>差异数量:</span>
                            <span class="font-medium text-red-600">2台</span>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: 100%"></div>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-semibold text-gray-800">B区盘点</h4>
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">进行中</span>
                    </div>
                    <div class="text-sm text-gray-600 space-y-2">
                        <div class="flex justify-between">
                            <span>负责人:</span>
                            <span class="font-medium text-blue-600">李经理</span>
                        </div>
                        <div class="flex justify-between">
                            <span>资产数量:</span>
                            <span class="font-medium">428台</span>
                        </div>
                        <div class="flex justify-between">
                            <span>完成进度:</span>
                            <span class="font-medium text-blue-600">65%</span>
                        </div>
                        <div class="flex justify-between">
                            <span>已盘点:</span>
                            <span class="font-medium text-green-600">278台</span>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: 65%"></div>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-4 border border-yellow-200">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-semibold text-gray-800">C区盘点</h4>
                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">待开始</span>
                    </div>
                    <div class="text-sm text-gray-600 space-y-2">
                        <div class="flex justify-between">
                            <span>负责人:</span>
                            <span class="font-medium text-yellow-600">王主任</span>
                        </div>
                        <div class="flex justify-between">
                            <span>资产数量:</span>
                            <span class="font-medium">464台</span>
                        </div>
                        <div class="flex justify-between">
                            <span>计划开始:</span>
                            <span class="font-medium text-orange-600">明日</span>
                        </div>
                        <div class="flex justify-between">
                            <span>预计用时:</span>
                            <span class="font-medium text-blue-600">3天</span>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-yellow-500 h-2 rounded-full" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 盘点进度跟踪 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-chart-line text-green-600 mr-2"></i>
                    盘点进度跟踪
                </h3>
                <div class="space-y-4">
                    <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">今日进度</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• 已盘点资产: 156台</div>
                            <div>• 发现差异: 3台</div>
                            <div>• 完成率: 78%</div>
                            <div class="text-green-600">• 进度良好</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">质量控制</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• 复核率: 100%</div>
                            <div>• 准确率: 98.5%</div>
                            <div>• 及时率: 95%</div>
                            <div class="text-blue-600">• 质量优秀</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-exclamation-circle text-red-600 mr-2"></i>
                    差异处理
                </h3>
                <div class="space-y-3">
                    <div class="bg-red-50 border border-red-200 rounded-lg p-3">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">盘亏设备</span>
                            <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">7台</span>
                        </div>
                        <div class="text-xs text-gray-600">
                            <div>• 生产设备: 3台</div>
                            <div>• 办公设备: 4台</div>
                        </div>
                    </div>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">盘盈设备</span>
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">5台</span>
                        </div>
                        <div class="text-xs text-gray-600">
                            <div>• 新购设备: 3台</div>
                            <div>• 调拨设备: 2台</div>
                        </div>
                    </div>
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">待处理</span>
                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">8台</span>
                        </div>
                        <div class="text-xs text-gray-600">
                            <div>• 需要调查: 5台</div>
                            <div>• 需要调账: 3台</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 盘点报告生成 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-file-alt text-purple-600 mr-2"></i>
                盘点报告生成
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
                    <h4 class="font-semibold text-gray-800 mb-3">盘点汇总报告</h4>
                    <div class="text-sm text-gray-600 space-y-2">
                        <div>• 盘点结果统计</div>
                        <div>• 差异分析说明</div>
                        <div>• 处理建议方案</div>
                        <div>• 责任人签字确认</div>
                    </div>
                    <button class="w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                        生成报告
                    </button>
                </div>

                <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-200">
                    <h4 class="font-semibold text-gray-800 mb-3">差异明细表</h4>
                    <div class="text-sm text-gray-600 space-y-2">
                        <div>• 盘盈设备清单</div>
                        <div>• 盘亏设备清单</div>
                        <div>• 差异原因分析</div>
                        <div>• 调账处理建议</div>
                    </div>
                    <button class="w-full mt-4 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                        导出明细
                    </button>
                </div>

                <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-200">
                    <h4 class="font-semibold text-gray-800 mb-3">资产台账</h4>
                    <div class="text-sm text-gray-600 space-y-2">
                        <div>• 更新后资产清单</div>
                        <div>• 资产分类统计</div>
                        <div>• 价值重新评估</div>
                        <div>• 台账数据同步</div>
                    </div>
                    <button class="w-full mt-4 px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors">
                        更新台账
                    </button>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-bolt text-blue-600 mr-2"></i>
                快速操作
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button class="flex flex-col items-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                    <i class="fas fa-plus text-green-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">新建盘点</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
                    <i class="fas fa-qrcode text-blue-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">扫码盘点</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors">
                    <i class="fas fa-chart-bar text-purple-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">进度分析</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors">
                    <i class="fas fa-download text-orange-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">导出数据</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 资产盘点管理功能
        function initAssetInventory() {
            console.log('初始化资产盘点管理功能');
            
            // 报告生成按钮事件
            const reportButtons = document.querySelectorAll('button');
            reportButtons.forEach(button => {
                const text = button.textContent.trim();
                if (text.includes('生成报告')) {
                    button.addEventListener('click', function() {
                        console.log('生成盘点汇总报告');
                        alert('正在生成盘点汇总报告...');
                    });
                } else if (text.includes('导出明细')) {
                    button.addEventListener('click', function() {
                        console.log('导出差异明细表');
                        alert('正在导出差异明细表...');
                    });
                } else if (text.includes('更新台账')) {
                    button.addEventListener('click', function() {
                        console.log('更新资产台账');
                        alert('正在更新资产台账...');
                    });
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initAssetInventory();
            console.log('资产盘点管理页面加载完成');
        });
    </script>
</body>
</html>
