<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>原料管理 - 数字工厂一体化平台</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">原料管理</h1>
            <p class="text-gray-600">管理生产原料的库存、采购和使用情况</p>
        </div>

        <!-- 操作按钮 -->
        <div class="flex flex-wrap gap-4 mb-6">
            <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                <i class="fas fa-plus mr-2"></i>
                新增原料
            </button>
            <button class="bg-success text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
                <i class="fas fa-upload mr-2"></i>
                批量导入
            </button>
            <button class="bg-warning text-white px-4 py-2 rounded-lg hover:bg-yellow-600 transition-colors flex items-center">
                <i class="fas fa-download mr-2"></i>
                导出数据
            </button>
            <button class="bg-secondary text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center">
                <i class="fas fa-filter mr-2"></i>
                高级筛选
            </button>
        </div>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-blue-600">156</div>
                        <div class="text-sm text-gray-600">原料种类</div>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-cubes text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-green-600">¥2.8M</div>
                        <div class="text-sm text-gray-600">库存总价值</div>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-red-600">12</div>
                        <div class="text-sm text-gray-600">库存预警</div>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-purple-600">85%</div>
                        <div class="text-sm text-gray-600">库存周转率</div>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-sync-alt text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 原料列表 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">原料库存列表</h3>
                <div class="flex flex-wrap gap-4">
                    <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option>全部分类</option>
                        <option>塑料原料</option>
                        <option>金属材料</option>
                        <option>电子元件</option>
                        <option>包装材料</option>
                    </select>
                    <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option>全部状态</option>
                        <option>库存充足</option>
                        <option>库存不足</option>
                        <option>缺货</option>
                    </select>
                    <input type="text" placeholder="搜索原料编号、名称..." class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1 min-w-64">
                </div>
            </div>

            <!-- 表格 -->
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">原料编号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">原料名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规格型号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前库存</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">安全库存</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单价</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">供应商</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">MAT-001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">ABS塑料颗粒</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">ABS-750A</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2,500 kg</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1,000 kg</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥12.50/kg</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">奇美实业</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">库存充足</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-900" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-purple-600 hover:text-purple-900" title="入库">
                                        <i class="fas fa-plus-circle"></i>
                                    </button>
                                    <button class="text-orange-600 hover:text-orange-900" title="出库">
                                        <i class="fas fa-minus-circle"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">MAT-002</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">铝合金板材</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">6061-T6</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">150 张</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">200 张</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥85.00/张</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">中铝集团</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">库存不足</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-900" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-purple-600 hover:text-purple-900" title="入库">
                                        <i class="fas fa-plus-circle"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-900" title="紧急采购">
                                        <i class="fas fa-shopping-cart"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">MAT-003</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">电阻器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1KΩ ±5%</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">0 个</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">5,000 个</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥0.05/个</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">风华高科</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">缺货</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-900" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-900" title="紧急采购">
                                        <i class="fas fa-shopping-cart"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    显示 1-3 条，共 156 条记录
                </div>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">上一页</button>
                    <button class="px-3 py-1 bg-primary text-white rounded text-sm">1</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">2</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">3</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">下一页</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
