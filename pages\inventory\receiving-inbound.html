<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收货入库管理 - 仓储管理 - 数字工厂一体化平台</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">收货入库管理</h1>
            <p class="text-gray-600">基于Process.md 2.2.1-2.2.2节，实现采购收货管理流程和退料入库流程</p>
        </div>

        <!-- 收货概览统计 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-blue-600">45</div>
                        <div class="text-sm text-gray-600">今日收货</div>
                        <div class="text-xs text-gray-500">收货单数</div>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-truck-loading text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-green-600">96%</div>
                        <div class="text-sm text-gray-600">及时收货率</div>
                        <div class="text-xs text-gray-500">本月平均</div>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-orange-600">8</div>
                        <div class="text-sm text-gray-600">待处理</div>
                        <div class="text-xs text-gray-500">收货任务</div>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-tasks text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-red-600">3</div>
                        <div class="text-sm text-gray-600">收货异常</div>
                        <div class="text-xs text-gray-500">需处理</div>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 收货任务列表 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800">收货任务列表</h3>
                    <div class="flex space-x-2">
                        <button class="bg-primary text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700" onclick="createReceivingTask()">
                            <i class="fas fa-plus mr-2"></i>新建收货
                        </button>
                        <button class="bg-secondary text-white px-4 py-2 rounded-md text-sm hover:bg-gray-700" onclick="batchProcess()">
                            <i class="fas fa-layer-group mr-2"></i>批量处理
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">收货信息</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">供应商</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">物料信息</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">收货时间</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-truck text-blue-600"></i>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">RCV-2025-001</div>
                                        <div class="text-xs text-gray-500">采购收货</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">深圳电子有限公司</div>
                                <div class="text-xs text-gray-500">SUP-001</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">电容器-100μF</div>
                                <div class="text-xs text-gray-500">CAP-100UF-001</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">1,000个</div>
                                <div class="text-xs text-gray-500">已收: 1,000个</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">2025-01-17 09:30</div>
                                <div class="text-xs text-gray-500">预计: 09:00</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    已完成
                                </span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900 mr-2">查看</button>
                                <button class="text-green-600 hover:text-green-900 mr-2">入库</button>
                                <button class="text-gray-600 hover:text-gray-900">打印</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-undo text-orange-600"></i>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">RET-2025-002</div>
                                        <div class="text-xs text-gray-500">退料入库</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">生产车间A</div>
                                <div class="text-xs text-gray-500">WORKSHOP-A</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">电阻器-10KΩ</div>
                                <div class="text-xs text-gray-500">RES-10K-001</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">50个</div>
                                <div class="text-xs text-gray-500">退料数量</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">2025-01-17 11:15</div>
                                <div class="text-xs text-gray-500">退料时间</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    处理中
                                </span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900 mr-2">处理</button>
                                <button class="text-green-600 hover:text-green-900 mr-2">确认</button>
                                <button class="text-gray-600 hover:text-gray-900">拒绝</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-exclamation-triangle text-red-600"></i>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">RCV-2025-003</div>
                                        <div class="text-xs text-gray-500">采购收货</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">上海元器件公司</div>
                                <div class="text-xs text-gray-500">SUP-002</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">IC芯片-STM32</div>
                                <div class="text-xs text-gray-500">IC-STM32-001</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">200个</div>
                                <div class="text-xs text-red-500">短缺: 20个</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">2025-01-17 14:00</div>
                                <div class="text-xs text-gray-500">预计: 13:30</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                    异常
                                </span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900 mr-2">处理</button>
                                <button class="text-orange-600 hover:text-orange-900 mr-2">联系</button>
                                <button class="text-red-600 hover:text-red-900">拒收</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 收货流程监控 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 收货进度 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">收货进度监控</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="bg-green-50 p-4 rounded-lg border-l-4 border-green-500">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-sm font-medium text-green-800">电容器收货</div>
                                    <div class="text-xs text-gray-600">RCV-2025-001</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-green-600">完成</div>
                                    <div class="text-xs text-gray-500">质检通过</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-sm font-medium text-blue-800">电阻器退料</div>
                                    <div class="text-xs text-gray-600">RET-2025-002</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-blue-600">处理中</div>
                                    <div class="text-xs text-gray-500">质量检查</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-red-50 p-4 rounded-lg border-l-4 border-red-500">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-sm font-medium text-red-800">IC芯片收货</div>
                                    <div class="text-xs text-gray-600">RCV-2025-003</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-red-600">异常</div>
                                    <div class="text-xs text-gray-500">数量短缺</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 收货统计 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">收货统计分析</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">采购收货</span>
                            <div class="flex items-center">
                                <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 85%"></div>
                                </div>
                                <span class="text-sm font-medium text-blue-600">85%</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">退料入库</span>
                            <div class="flex items-center">
                                <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                    <div class="bg-orange-600 h-2 rounded-full" style="width: 15%"></div>
                                </div>
                                <span class="text-sm font-medium text-orange-600">15%</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">及时收货率</span>
                            <div class="flex items-center">
                                <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: 96%"></div>
                                </div>
                                <span class="text-sm font-medium text-green-600">96%</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">质量合格率</span>
                            <div class="flex items-center">
                                <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                    <div class="bg-purple-600 h-2 rounded-full" style="width: 98%"></div>
                                </div>
                                <span class="text-sm font-medium text-purple-600">98%</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-2 gap-4 text-center">
                            <div>
                                <div class="text-lg font-bold text-blue-600">45</div>
                                <div class="text-xs text-gray-600">今日收货单</div>
                            </div>
                            <div>
                                <div class="text-lg font-bold text-green-600">42</div>
                                <div class="text-xs text-gray-600">已完成</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 操作函数
        function createReceivingTask() {
            alert('新建收货任务功能');
        }

        function batchProcess() {
            alert('批量处理功能');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('收货入库管理页面已加载');
        });
    </script>
</body>
</html>
