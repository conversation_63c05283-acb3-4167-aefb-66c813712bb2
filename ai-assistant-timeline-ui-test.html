<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手时间线UI优化测试 - 数字工厂一体化平台</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- AI助手样式 -->
    <link rel="stylesheet" href="assets/css/ai-assistant.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">AI助手时间线UI优化测试</h1>
            <p class="text-gray-600">测试时间线连接线的连续性和中心对齐效果</p>
        </div>

        <!-- 优化说明 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">🎨 UI优化内容</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">优化前问题</h4>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li>❌ 时间线连接线显示不连续</li>
                            <li>❌ 在各个时间节点之间出现断开</li>
                            <li>❌ 连接线与圆形状态图标未正确对齐</li>
                            <li>❌ 偏离了图标的中心位置</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">优化后效果</h4>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li>✅ 连接线在所有相邻节点之间保持连续</li>
                            <li>✅ 连接线精确通过每个圆形图标的中心点</li>
                            <li>✅ 实现完美的垂直对齐</li>
                            <li>✅ 保持颜色、粗细和样式的一致性</li>
                            <li>✅ 响应式适配不同屏幕尺寸</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技术实现 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">⚙️ 技术实现</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">HTML结构优化</h4>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li>✅ 重新设计时间线HTML结构</li>
                            <li>✅ 分离图标区域和内容区域</li>
                            <li>✅ 使用flexbox布局确保对齐</li>
                            <li>✅ 添加专用的CSS类名</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">CSS样式优化</h4>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li>✅ 使用绝对定位精确控制连接线</li>
                            <li>✅ top和bottom属性确保连续性</li>
                            <li>✅ transform居中对齐</li>
                            <li>✅ 渐变背景增强视觉效果</li>
                            <li>✅ 响应式媒体查询适配</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试步骤 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">📋 测试步骤</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                        <div>
                            <h4 class="font-medium text-gray-800">打开平台主页</h4>
                            <p class="text-sm text-gray-600">访问 <a href="http://localhost:8081/" target="_blank" class="text-blue-600 hover:underline">http://localhost:8081/</a></p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                        <div>
                            <h4 class="font-medium text-gray-800">启动AI助手</h4>
                            <p class="text-sm text-gray-600">点击右下角的蓝色圆形AI助手按钮</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                        <div>
                            <h4 class="font-medium text-gray-800">搜索业务单据</h4>
                            <p class="text-sm text-gray-600">输入 <code class="bg-gray-100 px-2 py-1 rounded">DM202501001</code> 并搜索</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">4</div>
                        <div>
                            <h4 class="font-medium text-gray-800">检查时间线UI</h4>
                            <p class="text-sm text-gray-600">仔细观察9个时间节点之间的连接线是否连续且与圆形图标中心对齐</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 验证要点 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">🔍 验证要点</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">连接线连续性</h4>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li>□ 第1个节点到第2个节点连接线连续</li>
                            <li>□ 第2个节点到第3个节点连接线连续</li>
                            <li>□ 所有中间节点连接线无断开</li>
                            <li>□ 最后一个节点无多余连接线</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">中心对齐效果</h4>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li>□ 连接线精确通过圆形图标中心</li>
                            <li>□ 垂直对齐完美无偏移</li>
                            <li>□ 不同状态图标对齐一致</li>
                            <li>□ 悬停效果不影响对齐</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 响应式测试 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">📱 响应式测试</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-blue-800 mb-2">桌面端 (>1024px)</h4>
                        <div class="text-sm text-blue-600 space-y-1">
                            <div>• 图标尺寸: 32px × 32px</div>
                            <div>• 连接线宽度: 2px</div>
                            <div>• 内容区域间距: 16px</div>
                        </div>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-green-800 mb-2">平板端 (768px-1024px)</h4>
                        <div class="text-sm text-green-600 space-y-1">
                            <div>• 图标尺寸: 32px × 32px</div>
                            <div>• 连接线宽度: 2px</div>
                            <div>• 内容区域自适应</div>
                        </div>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-purple-800 mb-2">移动端 (<768px)</h4>
                        <div class="text-sm text-purple-600 space-y-1">
                            <div>• 图标尺寸: 28px × 28px</div>
                            <div>• 连接线宽度: 2px</div>
                            <div>• 内容区域间距: 12px</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速测试按钮 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">🚀 快速测试</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="testTimelineUI('DM202501001')" 
                            class="p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-timeline mb-2"></i>
                        <div class="font-medium">测试完整时间线</div>
                        <div class="text-xs opacity-80">DM202501001 (9个节点)</div>
                    </button>
                    <button onclick="testTimelineUI('WO202501001')" 
                            class="p-4 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                        <i class="fas fa-cogs mb-2"></i>
                        <div class="font-medium">测试工单时间线</div>
                        <div class="text-xs opacity-80">WO202501001 (4个节点)</div>
                    </button>
                    <button onclick="testTimelineUI('SO202501001')" 
                            class="p-4 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                        <i class="fas fa-shopping-cart mb-2"></i>
                        <div class="font-medium">测试销售时间线</div>
                        <div class="text-xs opacity-80">SO202501001 (4个节点)</div>
                    </button>
                </div>
            </div>
        </div>

        <!-- 优化总结 -->
        <div class="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6">
            <div class="flex items-start">
                <i class="fas fa-check-circle text-green-600 text-2xl mr-4 mt-1"></i>
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">时间线UI优化完成</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h4 class="font-medium text-gray-800 mb-2">核心改进</h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>✅ 连接线完全连续，无断开</li>
                                <li>✅ 精确的中心对齐</li>
                                <li>✅ 一致的视觉样式</li>
                                <li>✅ 完美的响应式适配</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-800 mb-2">技术特点</h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>✅ CSS绝对定位精确控制</li>
                                <li>✅ Flexbox布局确保对齐</li>
                                <li>✅ 渐变背景增强视觉</li>
                                <li>✅ 媒体查询响应式适配</li>
                            </ul>
                        </div>
                    </div>
                    <div class="mt-4 p-3 bg-white rounded-lg border border-green-200">
                        <p class="text-sm text-green-800">
                            <i class="fas fa-lightbulb mr-2"></i>
                            <strong>测试提示</strong>：在AI助手中搜索DM202501001，观察9个时间节点的连接线是否完美连续且居中对齐。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 快速测试函数
        function testTimelineUI(docNumber) {
            // 等待AI助手加载完成
            setTimeout(() => {
                if (window.digitalFactoryAI) {
                    // 打开AI助手对话框
                    digitalFactoryAI.openDialog();
                    
                    // 填入测试单据编号
                    setTimeout(() => {
                        const input = document.getElementById('ai-input');
                        if (input) {
                            input.value = docNumber;
                            input.focus();
                            
                            // 自动搜索
                            setTimeout(() => {
                                digitalFactoryAI.searchDocument();
                            }, 500);
                        }
                    }, 200);
                } else {
                    alert('AI助手正在加载中，请稍后再试...');
                }
            }, 100);
        }

        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AI助手时间线UI优化测试页面已加载');
            console.log('请测试时间线连接线的连续性和对齐效果');
        });
    </script>
    
    <!-- AI助手脚本 -->
    <script src="assets/js/ai-assistant.js"></script>
</body>
</html>
