<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>厂内物流执行系统(LES) - 数字工厂一体化平台</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">厂内物流执行系统(LES)</h1>
            <p class="text-gray-600">基于process-les.md需求，提供全面的厂内物流管理和执行功能</p>
        </div>

        <!-- 物流管理概览统计 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-blue-600">156</div>
                        <div class="text-sm text-gray-600">配送任务</div>
                        <div class="text-xs text-gray-500">今日执行</div>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-truck text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-green-600">98.5%</div>
                        <div class="text-sm text-gray-600">送达率</div>
                        <div class="text-xs text-gray-500">按时送达</div>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-orange-600">12</div>
                        <div class="text-sm text-gray-600">AGV运行</div>
                        <div class="text-xs text-gray-500">设备在线</div>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-robot text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-purple-600">3</div>
                        <div class="text-sm text-gray-600">异常报警</div>
                        <div class="text-xs text-gray-500">待处理</div>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 物流管理功能模块 -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-6">物流管理功能</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- 基础数据管理 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-pointer" onclick="navigateToModule('basic-data')">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-database text-blue-600 text-xl"></i>
                            </div>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">基础</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">基础数据管理</h3>
                        <p class="text-sm text-gray-600 mb-4">物料工单信息、配送权限、料车料箱、配送规则、条码管理</p>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500">物料种类: 1,256</span>
                            <span class="text-green-600 font-medium">数据完整</span>
                        </div>
                    </div>
                </div>

                <!-- 任务优化配置 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-pointer" onclick="navigateToModule('task-optimization')">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-cogs text-green-600 text-xl"></i>
                            </div>
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">优化</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">任务优化配置</h3>
                        <p class="text-sm text-gray-600 mb-4">就近调配、任务分配、异常处理、故障规避</p>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500">优化算法: 4种</span>
                            <span class="text-green-600 font-medium">运行正常</span>
                        </div>
                    </div>
                </div>

                <!-- 存放区管理 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-pointer" onclick="navigateToModule('storage-area')">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-warehouse text-purple-600 text-xl"></i>
                            </div>
                            <span class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">存储</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">存放区管理</h3>
                        <p class="text-sm text-gray-600 mb-4">发料区管理、存放区管理、待运位置管理</p>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500">存储区域: 15个</span>
                            <span class="text-green-600 font-medium">利用率: 85%</span>
                        </div>
                    </div>
                </div>

                <!-- 物流追溯 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-pointer" onclick="navigateToModule('logistics-tracking')">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-route text-orange-600 text-xl"></i>
                            </div>
                            <span class="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full">追溯</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">物流追溯</h3>
                        <p class="text-sm text-gray-600 mb-4">AGV运行追溯、物料配送追溯、物料呼叫追溯、料箱料车追溯</p>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500">追溯记录: 5,689</span>
                            <span class="text-green-600 font-medium">可追溯率: 100%</span>
                        </div>
                    </div>
                </div>

                <!-- 动态监控 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-pointer" onclick="navigateToModule('dynamic-monitoring')">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-tv text-red-600 text-xl"></i>
                            </div>
                            <span class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">监控</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">动态监控</h3>
                        <p class="text-sm text-gray-600 mb-4">大屏显示、物流实时监控、物流设备监控</p>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500">监控设备: 25台</span>
                            <span class="text-green-600 font-medium">在线率: 96%</span>
                        </div>
                    </div>
                </div>

                <!-- 人员管理 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-pointer" onclick="navigateToModule('personnel-management')">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-users text-indigo-600 text-xl"></i>
                            </div>
                            <span class="text-xs bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full">人员</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">人员管理</h3>
                        <p class="text-sm text-gray-600 mb-4">人员管理、登录管理、权限管理</p>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500">物流人员: 45人</span>
                            <span class="text-green-600 font-medium">在岗率: 92%</span>
                        </div>
                    </div>
                </div>

                <!-- 信息查询 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-pointer" onclick="navigateToModule('information-query')">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-search text-teal-600 text-xl"></i>
                            </div>
                            <span class="text-xs bg-teal-100 text-teal-800 px-2 py-1 rounded-full">查询</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">信息查询</h3>
                        <p class="text-sm text-gray-600 mb-4">配送记录查询、计划物料查询、异常报警查询</p>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500">查询记录: 2,345</span>
                            <span class="text-green-600 font-medium">响应时间: <1s</span>
                        </div>
                    </div>
                </div>

                <!-- 分析统计 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-pointer" onclick="navigateToModule('analysis-statistics')">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-chart-bar text-yellow-600 text-xl"></i>
                            </div>
                            <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">统计</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">分析统计</h3>
                        <p class="text-sm text-gray-600 mb-4">配送统计、利用率分析、送达率统计、报警统计</p>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500">统计报表: 12种</span>
                            <span class="text-green-600 font-medium">数据准确</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 物流流程概览 -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-6">物流执行流程</h2>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-8">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">物料呼叫</div>
                                <div class="text-xs text-gray-500">工位发起需求</div>
                            </div>
                        </div>
                        <div class="flex-1 h-1 bg-gray-200 mx-4">
                            <div class="h-1 bg-blue-600" style="width: 100%"></div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-medium">2</div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">任务分配</div>
                                <div class="text-xs text-gray-500">智能调度优化</div>
                            </div>
                        </div>
                        <div class="flex-1 h-1 bg-gray-200 mx-4">
                            <div class="h-1 bg-green-600" style="width: 85%"></div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-medium">3</div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">物料配送</div>
                                <div class="text-xs text-gray-500">AGV自动运输</div>
                            </div>
                        </div>
                        <div class="flex-1 h-1 bg-gray-200 mx-4">
                            <div class="h-1 bg-purple-600" style="width: 70%"></div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-orange-600 text-white rounded-full flex items-center justify-center text-sm font-medium">4</div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">送达确认</div>
                                <div class="text-xs text-gray-500">完成追溯记录</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时监控面板 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 物流设备状态 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">物流设备状态</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">AGV自动导引车</span>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                <div class="bg-green-600 h-2 rounded-full" style="width: 92%"></div>
                            </div>
                            <span class="text-sm font-medium text-green-600">12/13在线</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">叉车设备</span>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: 100%"></div>
                            </div>
                            <span class="text-sm font-medium text-blue-600">8/8在线</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">输送带系统</span>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                <div class="bg-purple-600 h-2 rounded-full" style="width: 95%"></div>
                            </div>
                            <span class="text-sm font-medium text-purple-600">19/20运行</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">料车料箱</span>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                <div class="bg-orange-600 h-2 rounded-full" style="width: 88%"></div>
                            </div>
                            <span class="text-sm font-medium text-orange-600">156/178可用</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 异常报警 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">异常报警</h3>
                <div class="space-y-3">
                    <div class="bg-red-50 p-3 rounded-lg border-l-4 border-red-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-red-800">AGV-003运行异常</div>
                                <div class="text-xs text-gray-600">导航系统故障，已停止运行</div>
                            </div>
                            <span class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">紧急</span>
                        </div>
                    </div>
                    <div class="bg-orange-50 p-3 rounded-lg border-l-4 border-orange-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-orange-800">料箱库存不足</div>
                                <div class="text-xs text-gray-600">A区料箱数量低于安全库存</div>
                            </div>
                            <span class="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">重要</span>
                        </div>
                    </div>
                    <div class="bg-yellow-50 p-3 rounded-lg border-l-4 border-yellow-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-yellow-800">配送任务延迟</div>
                                <div class="text-xs text-gray-600">工位W-05物料配送超时</div>
                            </div>
                            <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">一般</span>
                        </div>
                    </div>
                </div>
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <button class="text-sm text-blue-600 hover:text-blue-800 font-medium">查看全部报警 →</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 导航到具体模块
        function navigateToModule(module) {
            const moduleUrls = {
                'basic-data': './basic-data.html',
                'task-optimization': './task-optimization.html',
                'storage-area': './storage-area.html',
                'logistics-tracking': './logistics-tracking.html',
                'dynamic-monitoring': './dynamic-monitoring.html',
                'personnel-management': './personnel-management.html',
                'information-query': './information-query.html',
                'analysis-statistics': './analysis-statistics.html'
            };
            
            if (moduleUrls[module]) {
                window.location.href = moduleUrls[module];
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('厂内物流执行系统(LES)已加载');
        });
    </script>
</body>
</html>
