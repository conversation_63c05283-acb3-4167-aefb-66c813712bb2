<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>物料主数据管理 - 主数据平台 - 数字工厂一体化平台</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">物料主数据管理</h1>
            <p class="text-gray-600">统一管理物料编码、规格、属性、BOM结构等核心物料信息</p>
        </div>

        <!-- 物料统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-blue-600">1,256</div>
                        <div class="text-sm text-gray-600">物料总数</div>
                        <div class="text-xs text-gray-500">已标准化</div>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-cube text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-green-600">15</div>
                        <div class="text-sm text-gray-600">物料类别</div>
                        <div class="text-xs text-gray-500">分类管理</div>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-tags text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-orange-600">98%</div>
                        <div class="text-sm text-gray-600">标准化率</div>
                        <div class="text-xs text-gray-500">编码规范</div>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-purple-600">156</div>
                        <div class="text-sm text-gray-600">BOM结构</div>
                        <div class="text-xs text-gray-500">产品结构</div>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-sitemap text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 物料管理功能选项卡 -->
        <div class="mb-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8">
                    <button onclick="showTab('materials')" class="tab-button border-b-2 border-blue-500 text-blue-600 py-2 px-1 text-sm font-medium" id="materials-tab">
                        物料信息管理
                    </button>
                    <button onclick="showTab('categories')" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 text-sm font-medium" id="categories-tab">
                        物料分类管理
                    </button>
                    <button onclick="showTab('bom')" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 text-sm font-medium" id="bom-tab">
                        BOM结构管理
                    </button>
                    <button onclick="showTab('attributes')" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 text-sm font-medium" id="attributes-tab">
                        物料属性管理
                    </button>
                </nav>
            </div>
        </div>

        <!-- 物料信息管理 -->
        <div id="materials-content" class="tab-content">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-800">物料信息管理</h3>
                        <div class="flex space-x-2">
                            <button class="bg-primary text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700" onclick="addMaterial()">
                                <i class="fas fa-plus mr-2"></i>新增物料
                            </button>
                            <button class="bg-success text-white px-4 py-2 rounded-md text-sm hover:bg-green-700" onclick="importMaterials()">
                                <i class="fas fa-upload mr-2"></i>批量导入
                            </button>
                            <button class="bg-secondary text-white px-4 py-2 rounded-md text-sm hover:bg-gray-700" onclick="exportMaterials()">
                                <i class="fas fa-download mr-2"></i>导出数据
                            </button>
                        </div>
                    </div>
                    
                    <!-- 搜索和筛选 -->
                    <div class="flex flex-wrap gap-4 mt-4">
                        <select class="border border-gray-300 rounded-md px-3 py-2 text-sm" id="categoryFilter">
                            <option>全部类别</option>
                            <option>电子元器件</option>
                            <option>PCB板</option>
                            <option>包装材料</option>
                            <option>辅助材料</option>
                        </select>
                        <select class="border border-gray-300 rounded-md px-3 py-2 text-sm" id="statusFilter">
                            <option>全部状态</option>
                            <option>启用</option>
                            <option>禁用</option>
                            <option>待审核</option>
                        </select>
                        <input type="text" placeholder="搜索物料编号、名称..." class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1 min-w-64" id="searchInput">
                        <button class="bg-primary text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700" onclick="searchMaterials()">
                            <i class="fas fa-search mr-1"></i>搜索
                        </button>
                    </div>
                </div>

                <!-- 物料数据表格 -->
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">物料编号</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">物料名称</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">物料类别</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规格型号</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">供应商</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200" id="materialsTableBody">
                            <!-- 物料数据将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
                    <div class="text-sm text-gray-700" id="materialsRecordInfo">
                        显示记录信息
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-primary text-white rounded text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 物料分类管理 -->
        <div id="categories-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">物料分类管理</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- 电子元器件 -->
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-gray-800">电子元器件</h4>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">核心</span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">物料数量:</span>
                                <span class="text-gray-900">856种</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">子分类:</span>
                                <span class="text-gray-900">电阻、电容、IC等</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">标准化率:</span>
                                <span class="text-green-600">98%</span>
                            </div>
                        </div>
                        <div class="mt-3 flex space-x-2">
                            <button class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded hover:bg-blue-200">查看详情</button>
                            <button class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded hover:bg-gray-200">编辑</button>
                        </div>
                    </div>

                    <!-- PCB板 -->
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-gray-800">PCB板</h4>
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">重要</span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">物料数量:</span>
                                <span class="text-gray-900">156种</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">子分类:</span>
                                <span class="text-gray-900">主控板、功率板等</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">标准化率:</span>
                                <span class="text-green-600">95%</span>
                            </div>
                        </div>
                        <div class="mt-3 flex space-x-2">
                            <button class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded hover:bg-green-200">查看详情</button>
                            <button class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded hover:bg-gray-200">编辑</button>
                        </div>
                    </div>

                    <!-- 包装材料 -->
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-gray-800">包装材料</h4>
                            <span class="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full">辅助</span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">物料数量:</span>
                                <span class="text-gray-900">89种</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">子分类:</span>
                                <span class="text-gray-900">纸箱、泡沫、标签等</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">标准化率:</span>
                                <span class="text-green-600">92%</span>
                            </div>
                        </div>
                        <div class="mt-3 flex space-x-2">
                            <button class="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded hover:bg-orange-200">查看详情</button>
                            <button class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded hover:bg-gray-200">编辑</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- BOM结构管理 -->
        <div id="bom-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">BOM结构管理</h3>
                <div class="mb-4">
                    <select class="border border-gray-300 rounded-md px-3 py-2 text-sm mr-4">
                        <option>选择产品</option>
                        <option>5KW逆变器</option>
                        <option>10KW逆变器</option>
                        <option>PACK主控模块</option>
                    </select>
                    <button class="bg-primary text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700">
                        <i class="fas fa-eye mr-2"></i>查看BOM
                    </button>
                </div>
                
                <!-- BOM树形结构示例 -->
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="text-sm font-medium text-gray-800 mb-3">5KW逆变器 BOM结构</div>
                    <div class="space-y-2 text-sm">
                        <div class="flex items-center">
                            <i class="fas fa-folder text-blue-600 mr-2"></i>
                            <span class="font-medium">5KW逆变器 (1台)</span>
                        </div>
                        <div class="ml-6 space-y-1">
                            <div class="flex items-center">
                                <i class="fas fa-file text-green-600 mr-2"></i>
                                <span>主控PCB板 (1片) - PCB-MAIN-V2.1</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-file text-green-600 mr-2"></i>
                                <span>功率模块 (6个) - IGBT-600V-50A</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-file text-green-600 mr-2"></i>
                                <span>电解电容 (12个) - CAP-100uF-25V</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-file text-green-600 mr-2"></i>
                                <span>散热器 (1个) - HEAT-SINK-5KW</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-file text-green-600 mr-2"></i>
                                <span>外壳 (1套) - CASE-5KW-IP65</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 物料属性管理 -->
        <div id="attributes-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">物料属性管理</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 标准属性 -->
                    <div>
                        <h4 class="font-semibold text-gray-800 mb-3">标准属性</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">物料编号</span>
                                <span class="text-gray-900">必填，唯一标识</span>
                            </div>
                            <div class="flex justify-between p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">物料名称</span>
                                <span class="text-gray-900">必填，中文描述</span>
                            </div>
                            <div class="flex justify-between p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">规格型号</span>
                                <span class="text-gray-900">必填，技术规格</span>
                            </div>
                            <div class="flex justify-between p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">计量单位</span>
                                <span class="text-gray-900">必填，PCS/KG/M等</span>
                            </div>
                        </div>
                    </div>

                    <!-- 扩展属性 -->
                    <div>
                        <h4 class="font-semibold text-gray-800 mb-3">扩展属性</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">供应商</span>
                                <span class="text-gray-900">可选，主要供应商</span>
                            </div>
                            <div class="flex justify-between p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">采购周期</span>
                                <span class="text-gray-900">可选，天数</span>
                            </div>
                            <div class="flex justify-between p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">安全库存</span>
                                <span class="text-gray-900">可选，最低库存量</span>
                            </div>
                            <div class="flex justify-between p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">存储条件</span>
                                <span class="text-gray-900">可选，温湿度要求</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 物料数据模型
        const materialsData = [
            {
                id: 'MAT001',
                code: 'CAP-100uF-25V',
                name: '电解电容器',
                category: '电子元器件',
                specification: '100μF/25V',
                unit: 'PCS',
                supplier: '华星电子',
                status: 'active',
                statusName: '启用'
            },
            {
                id: 'MAT002',
                code: 'PCB-MAIN-V2.1',
                name: '主控PCB板',
                category: 'PCB板',
                specification: '100×80mm',
                unit: 'PCS',
                supplier: '精密电路',
                status: 'active',
                statusName: '启用'
            },
            {
                id: 'MAT003',
                code: 'IGBT-600V-50A',
                name: 'IGBT功率模块',
                category: '电子元器件',
                specification: '600V/50A',
                unit: 'PCS',
                supplier: '英飞凌',
                status: 'active',
                statusName: '启用'
            },
            {
                id: 'MAT004',
                code: 'CASE-5KW-IP65',
                name: '逆变器外壳',
                category: '结构件',
                specification: 'IP65防护等级',
                unit: 'SET',
                supplier: '金属制品',
                status: 'active',
                statusName: '启用'
            }
        ];

        // 显示选项卡
        function showTab(tabName) {
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // 重置所有选项卡样式
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('border-blue-500', 'text-blue-600');
                button.classList.add('border-transparent', 'text-gray-500');
            });
            
            // 显示选中的内容
            document.getElementById(tabName + '-content').classList.remove('hidden');
            
            // 设置选中的选项卡样式
            const activeTab = document.getElementById(tabName + '-tab');
            activeTab.classList.remove('border-transparent', 'text-gray-500');
            activeTab.classList.add('border-blue-500', 'text-blue-600');

            // 根据选项卡类型渲染相应数据
            if (tabName === 'materials') {
                renderMaterialsTable();
            }
        }

        // 渲染物料数据表格
        function renderMaterialsTable() {
            const tbody = document.getElementById('materialsTableBody');
            tbody.innerHTML = '';

            materialsData.forEach(material => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 transition-colors';
                
                row.innerHTML = `
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-blue-600">${material.code}</div>
                        <div class="text-xs text-gray-500">ID: ${material.id}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${material.name}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <span class="text-sm text-gray-900">${material.category}</span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <span class="text-sm text-gray-900">${material.specification}</span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <span class="text-sm text-gray-900">${material.unit}</span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <span class="text-sm text-gray-900">${material.supplier}</span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            ${material.statusName}
                        </span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-2">
                            <button onclick="editMaterial('${material.id}')" class="text-blue-600 hover:text-blue-900">编辑</button>
                            <button onclick="viewMaterial('${material.id}')" class="text-green-600 hover:text-green-900">查看</button>
                            <button onclick="deleteMaterial('${material.id}')" class="text-red-600 hover:text-red-900">删除</button>
                        </div>
                    </td>
                `;
                
                tbody.appendChild(row);
            });

            // 更新记录信息
            document.getElementById('materialsRecordInfo').textContent = `显示 1-${materialsData.length} 条，共 ${materialsData.length} 条记录`;
        }

        // 操作函数
        function addMaterial() {
            alert('新增物料功能');
        }

        function importMaterials() {
            alert('批量导入物料功能');
        }

        function exportMaterials() {
            alert('导出物料数据功能');
        }

        function searchMaterials() {
            alert('搜索物料功能');
        }

        function editMaterial(id) {
            alert(`编辑物料: ${id}`);
        }

        function viewMaterial(id) {
            alert(`查看物料详情: ${id}`);
        }

        function deleteMaterial(id) {
            if (confirm(`确认删除物料: ${id}？`)) {
                alert(`已删除物料: ${id}`);
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            showTab('materials');
        });
    </script>
</body>
</html>
