<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户主数据管理 - 主数据平台 - 数字工厂一体化平台</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">客户主数据管理</h1>
            <p class="text-gray-600">统一管理客户档案、信用评级、订单历史、服务记录等核心客户信息</p>
        </div>

        <!-- 客户统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-red-600">156</div>
                        <div class="text-sm text-gray-600">客户总数</div>
                        <div class="text-xs text-gray-500">合作客户</div>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-handshake text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-blue-600">8</div>
                        <div class="text-sm text-gray-600">客户类型</div>
                        <div class="text-xs text-gray-500">分类管理</div>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-layer-group text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-green-600">98%</div>
                        <div class="text-sm text-gray-600">满意度</div>
                        <div class="text-xs text-gray-500">服务评价</div>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-star text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-purple-600">2,456</div>
                        <div class="text-sm text-gray-600">订单总数</div>
                        <div class="text-xs text-gray-500">历史订单</div>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-shopping-cart text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 客户管理表格 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800">客户信息管理</h3>
                    <div class="flex space-x-2">
                        <button class="bg-primary text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700" onclick="addCustomer()">
                            <i class="fas fa-plus mr-2"></i>新增客户
                        </button>
                        <button class="bg-success text-white px-4 py-2 rounded-md text-sm hover:bg-green-700" onclick="importCustomers()">
                            <i class="fas fa-upload mr-2"></i>批量导入
                        </button>
                        <button class="bg-secondary text-white px-4 py-2 rounded-md text-sm hover:bg-gray-700" onclick="exportCustomers()">
                            <i class="fas fa-download mr-2"></i>导出数据
                        </button>
                    </div>
                </div>
                
                <!-- 搜索和筛选 -->
                <div class="flex flex-wrap gap-4 mt-4">
                    <select class="border border-gray-300 rounded-md px-3 py-2 text-sm" id="typeFilter">
                        <option>全部类型</option>
                        <option>大型企业</option>
                        <option>中型企业</option>
                        <option>小型企业</option>
                        <option>个人客户</option>
                    </select>
                    <select class="border border-gray-300 rounded-md px-3 py-2 text-sm" id="levelFilter">
                        <option>全部等级</option>
                        <option>VIP客户</option>
                        <option>重要客户</option>
                        <option>普通客户</option>
                    </select>
                    <select class="border border-gray-300 rounded-md px-3 py-2 text-sm" id="statusFilter">
                        <option>全部状态</option>
                        <option>活跃</option>
                        <option>休眠</option>
                        <option>流失</option>
                    </select>
                    <input type="text" placeholder="搜索客户名称、编号..." class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1 min-w-64" id="searchInput">
                    <button class="bg-primary text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700" onclick="searchCustomers()">
                        <i class="fas fa-search mr-1"></i>搜索
                    </button>
                </div>
            </div>

            <!-- 客户数据表格 -->
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户信息</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户类型</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户等级</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">信用评级</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">合作状态</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50 transition-colors">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-building text-red-600"></i>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">正泰电器集团</div>
                                        <div class="text-xs text-gray-500">编号: CUS001</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    大型企业
                                </span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">0571-12345678</div>
                                <div class="text-xs text-gray-500"><EMAIL></div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                                    VIP客户
                                </span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex text-yellow-400">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <span class="ml-2 text-sm text-gray-600">AAA</span>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    活跃
                                </span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900">编辑</button>
                                    <button class="text-green-600 hover:text-green-900">查看</button>
                                    <button class="text-purple-600 hover:text-purple-900">订单</button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 transition-colors">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-building text-blue-600"></i>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">德力西电气</div>
                                        <div class="text-xs text-gray-500">编号: CUS002</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    中型企业
                                </span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">021-87654321</div>
                                <div class="text-xs text-gray-500"><EMAIL></div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    重要客户
                                </span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex text-yellow-400">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="far fa-star"></i>
                                    </div>
                                    <span class="ml-2 text-sm text-gray-600">AA</span>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    活跃
                                </span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900">编辑</button>
                                    <button class="text-green-600 hover:text-green-900">查看</button>
                                    <button class="text-purple-600 hover:text-purple-900">订单</button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 transition-colors">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-building text-orange-600"></i>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">施耐德电气</div>
                                        <div class="text-xs text-gray-500">编号: CUS003</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                                    大型企业
                                </span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">010-98765432</div>
                                <div class="text-xs text-gray-500"><EMAIL></div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">
                                    普通客户
                                </span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex text-yellow-400">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="far fa-star"></i>
                                        <i class="far fa-star"></i>
                                    </div>
                                    <span class="ml-2 text-sm text-gray-600">A</span>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">
                                    休眠
                                </span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900">编辑</button>
                                    <button class="text-green-600 hover:text-green-900">查看</button>
                                    <button class="text-purple-600 hover:text-purple-900">订单</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    显示 1-3 条，共 156 条记录
                </div>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">上一页</button>
                    <button class="px-3 py-1 bg-primary text-white rounded text-sm">1</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">2</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">3</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">下一页</button>
                </div>
            </div>
        </div>

        <!-- 客户分析 -->
        <div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 客户类型分布 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">客户类型分布</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <div>
                            <div class="text-sm font-medium text-blue-800">大型企业</div>
                            <div class="text-xs text-gray-600">主要客户群体</div>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-blue-600">45</div>
                            <div class="text-xs text-gray-500">家客户</div>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                        <div>
                            <div class="text-sm font-medium text-green-800">中型企业</div>
                            <div class="text-xs text-gray-600">稳定合作伙伴</div>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-green-600">68</div>
                            <div class="text-xs text-gray-500">家客户</div>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                        <div>
                            <div class="text-sm font-medium text-orange-800">小型企业</div>
                            <div class="text-xs text-gray-600">成长型客户</div>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-orange-600">35</div>
                            <div class="text-xs text-gray-500">家客户</div>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                        <div>
                            <div class="text-sm font-medium text-purple-800">个人客户</div>
                            <div class="text-xs text-gray-600">零售客户</div>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-purple-600">8</div>
                            <div class="text-xs text-gray-500">位客户</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 客户等级分析 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">客户等级分析</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">VIP客户</span>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                <div class="bg-purple-600 h-2 rounded-full" style="width: 15%"></div>
                            </div>
                            <span class="text-sm font-medium text-purple-600">23家 (15%)</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">重要客户</span>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: 35%"></div>
                            </div>
                            <span class="text-sm font-medium text-blue-600">55家 (35%)</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">普通客户</span>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                <div class="bg-green-600 h-2 rounded-full" style="width: 50%"></div>
                            </div>
                            <span class="text-sm font-medium text-green-600">78家 (50%)</span>
                        </div>
                    </div>
                </div>
                <div class="mt-6 pt-4 border-t border-gray-200">
                    <div class="text-center">
                        <div class="text-lg font-bold text-red-600">98%</div>
                        <div class="text-xs text-gray-600">客户满意度</div>
                        <div class="text-xs text-green-600 mt-1">较上月提升 +1.5%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 操作函数
        function addCustomer() {
            alert('新增客户功能');
        }

        function importCustomers() {
            alert('批量导入客户功能');
        }

        function exportCustomers() {
            alert('导出客户数据功能');
        }

        function searchCustomers() {
            alert('搜索客户功能');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('客户主数据管理页面已加载');
        });
    </script>
</body>
</html>
