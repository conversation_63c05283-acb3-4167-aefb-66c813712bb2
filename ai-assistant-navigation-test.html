<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手导航功能测试 - 数字工厂一体化平台</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- AI助手样式 -->
    <link rel="stylesheet" href="assets/css/ai-assistant.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">AI助手导航功能测试</h1>
            <p class="text-gray-600">测试AI助手的iframe内导航功能，验证无缝单据切换体验</p>
        </div>

        <!-- 功能说明 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">🚀 导航功能优化</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">优化前</h4>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li>❌ 点击单据编号在新标签页打开</li>
                            <li>❌ 打断用户工作流程</li>
                            <li>❌ 需要在多个标签页间切换</li>
                            <li>❌ AI助手对话框关闭</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">优化后</h4>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li>✅ 在当前系统iframe中加载页面</li>
                            <li>✅ 保持平台整体布局结构</li>
                            <li>✅ AI助手对话框保持打开</li>
                            <li>✅ 无缝的单据切换体验</li>
                            <li>✅ 支持快速查看上下游业务单据</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试说明 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">📋 测试步骤</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                        <div>
                            <h4 class="font-medium text-gray-800">打开平台主页</h4>
                            <p class="text-sm text-gray-600">访问 <a href="http://localhost:8081/" target="_blank" class="text-blue-600 hover:underline">http://localhost:8081/</a></p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                        <div>
                            <h4 class="font-medium text-gray-800">点击AI助手按钮</h4>
                            <p class="text-sm text-gray-600">点击右下角的蓝色圆形AI助手按钮</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                        <div>
                            <h4 class="font-medium text-gray-800">搜索业务单据</h4>
                            <p class="text-sm text-gray-600">输入 <code class="bg-gray-100 px-2 py-1 rounded">DM202501001</code> 并搜索</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">4</div>
                        <div>
                            <h4 class="font-medium text-gray-800">点击时间线中的单据编号</h4>
                            <p class="text-sm text-gray-600">点击任意时间线节点中的单据编号（如DM202501001、MPS202501001、WO202501001等）</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">5</div>
                        <div>
                            <h4 class="font-medium text-gray-800">验证导航效果</h4>
                            <p class="text-sm text-gray-600">确认页面在主内容区域加载，AI助手保持打开，单据正确高亮显示</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试用例 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">🧪 测试用例</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-blue-800 mb-2">需求计划流程</h4>
                        <div class="text-sm text-blue-600 space-y-1">
                            <div>• DM202501001 → 需求管理页面</div>
                            <div>• MPS202501001 → MPS管理页面</div>
                            <div>• WO202501001 → 工单管理页面</div>
                        </div>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-green-800 mb-2">生产执行流程</h4>
                        <div class="text-sm text-green-600 space-y-1">
                            <div>• WO202501001 → 工单管理页面</div>
                            <div>• MR202501001 → MRP管理页面</div>
                            <div>• PO202501001 → 收货入库页面</div>
                        </div>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-purple-800 mb-2">仓储物流流程</h4>
                        <div class="text-sm text-purple-600 space-y-1">
                            <div>• IN202501001 → 收货入库页面</div>
                            <div>• FIN202501001 → 成品入库页面</div>
                            <div>• OUT202501001 → 成品出库页面</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技术实现 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">⚙️ 技术实现</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">核心功能</h4>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li>✅ 修改AI助手的navigateToDocument函数</li>
                            <li>✅ 使用iframe导航而非新标签页</li>
                            <li>✅ 调用主页面的iframe切换函数</li>
                            <li>✅ 保持AI助手对话框打开状态</li>
                            <li>✅ 保持URL参数传递功能</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">跨域通信</h4>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li>✅ 检测iframe运行环境</li>
                            <li>✅ 调用顶级窗口的导航函数</li>
                            <li>✅ 支持父窗口导航</li>
                            <li>✅ 备用方案：新标签页打开</li>
                            <li>✅ 错误处理和日志记录</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 验证清单 -->
        <div class="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6">
            <div class="flex items-start">
                <i class="fas fa-clipboard-check text-green-600 text-2xl mr-4 mt-1"></i>
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">验证清单</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h4 class="font-medium text-gray-800 mb-2">基础功能验证</h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>□ AI助手按钮正常显示</li>
                                <li>□ 对话框正常打开和关闭</li>
                                <li>□ 单据搜索功能正常</li>
                                <li>□ 时间线正常显示</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-800 mb-2">导航功能验证</h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>□ 点击单据编号在iframe中加载页面</li>
                                <li>□ AI助手对话框保持打开</li>
                                <li>□ 单据在目标页面正确高亮</li>
                                <li>□ 支持多个单据类型导航</li>
                            </ul>
                        </div>
                    </div>
                    <div class="mt-4 p-3 bg-white rounded-lg border border-green-200">
                        <p class="text-sm text-green-800">
                            <i class="fas fa-lightbulb mr-2"></i>
                            <strong>测试提示</strong>：在主页面中测试AI助手导航功能，确保所有单据编号都能正确跳转到对应页面，并且AI助手保持打开状态。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AI助手导航功能测试页面已加载');
            console.log('请在主页面中测试AI助手的导航功能');
            
            // 检查是否在iframe中运行
            if (window.self !== window.top) {
                console.log('当前页面在iframe中运行');
            } else {
                console.log('当前页面在顶级窗口中运行');
            }
        });
    </script>
    
    <!-- AI助手脚本 -->
    <script src="assets/js/ai-assistant.js"></script>
</body>
</html>
