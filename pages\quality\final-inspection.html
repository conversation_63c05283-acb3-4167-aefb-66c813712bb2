<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成品检验管理 - 质量管理 - 数字工厂一体化平台</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">成品检验管理</h1>
            <p class="text-gray-600">基于Process.md 2.5.3流程：成品检验→结果判定→合格放行→不合格处理，确保出货产品质量符合要求</p>
        </div>

        <!-- 成品检验流程状态指示器 -->
        <div class="mb-6">
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">成品检验流程</h3>
                    <span class="text-sm text-gray-600">FQC/OQC质量控制</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                            <span class="ml-2 text-sm font-medium text-gray-900">成品检验</span>
                        </div>
                        <div class="flex-1 h-1 bg-gray-200 mx-2">
                            <div class="h-1 bg-primary" style="width: 100%"></div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium">2</div>
                            <span class="ml-2 text-sm font-medium text-gray-900">结果判定</span>
                        </div>
                        <div class="flex-1 h-1 bg-gray-200 mx-2">
                            <div class="h-1 bg-primary" style="width: 85%"></div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">3</div>
                            <span class="ml-2 text-sm text-gray-900">合格放行</span>
                        </div>
                        <div class="flex-1 h-1 bg-gray-200 mx-2">
                            <div class="h-1 bg-blue-500" style="width: 70%"></div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">4</div>
                            <span class="ml-2 text-sm text-gray-600">不合格处理</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 顶部操作区域 -->
        <div class="flex flex-wrap gap-4 mb-6">
            <button id="finalInspectionBtn" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                <i class="fas fa-clipboard-check mr-2"></i>
                成品检验
            </button>
            <button id="resultJudgmentBtn" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors flex items-center">
                <i class="fas fa-gavel mr-2"></i>
                结果判定
            </button>
            <button id="qualifiedReleaseBtn" class="bg-success text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
                <i class="fas fa-check mr-2"></i>
                合格放行
            </button>
            <button id="nonconformityBtn" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                不合格处理
            </button>
            <button id="qualityAnalysisBtn" class="bg-warning text-white px-4 py-2 rounded-lg hover:bg-yellow-600 transition-colors flex items-center">
                <i class="fas fa-chart-line mr-2"></i>
                质量分析
            </button>
            <button class="bg-secondary text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center">
                <i class="fas fa-download mr-2"></i>
                导出报告
            </button>
        </div>

        <!-- 成品检验统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-blue-600">245</div>
                        <div class="text-sm text-gray-600">检验批次</div>
                        <div class="text-xs text-gray-500">本月累计</div>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clipboard-check text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-green-600">97.8%</div>
                        <div class="text-sm text-gray-600">合格率</div>
                        <div class="text-xs text-gray-500">出货质量</div>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-orange-600">18</div>
                        <div class="text-sm text-gray-600">待检验</div>
                        <div class="text-xs text-gray-500">排队中</div>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-purple-600">5</div>
                        <div class="text-sm text-gray-600">不合格批次</div>
                        <div class="text-xs text-gray-500">需要处理</div>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-times-circle text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-indigo-600">3.5小时</div>
                        <div class="text-sm text-gray-600">平均周期</div>
                        <div class="text-xs text-gray-500">检验时间</div>
                    </div>
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-stopwatch text-indigo-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-red-600">0</div>
                        <div class="text-sm text-gray-600">客户投诉</div>
                        <div class="text-xs text-gray-500">本月零投诉</div>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-shield text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 检验进度和质量监控面板 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- 检验进度跟踪 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">检验进度跟踪</h3>
                <div class="space-y-4">
                    <div class="border rounded-lg p-4 hover:bg-blue-50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-clipboard-check text-blue-600 mr-3"></i>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">成品检验</div>
                                    <div class="text-xs text-gray-500">外观、功能、性能检验</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-semibold text-blue-600">100%</div>
                                <div class="text-xs text-gray-500">已完成</div>
                            </div>
                        </div>
                        <div class="mt-2 w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: 100%"></div>
                        </div>
                    </div>
                    <div class="border rounded-lg p-4 hover:bg-green-50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-gavel text-green-600 mr-3"></i>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">结果判定</div>
                                    <div class="text-xs text-gray-500">合格性判定、数据分析</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-semibold text-green-600">85%</div>
                                <div class="text-xs text-gray-500">进行中</div>
                            </div>
                        </div>
                        <div class="mt-2 w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-600 h-2 rounded-full" style="width: 85%"></div>
                        </div>
                    </div>
                    <div class="border rounded-lg p-4 hover:bg-purple-50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-check text-purple-600 mr-3"></i>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">合格放行</div>
                                    <div class="text-xs text-gray-500">放行确认、入库准备</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-semibold text-purple-600">30%</div>
                                <div class="text-xs text-gray-500">准备中</div>
                            </div>
                        </div>
                        <div class="mt-2 w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-purple-600 h-2 rounded-full" style="width: 30%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 质量监控面板 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">质量监控</h3>
                <div class="space-y-4">
                    <div class="bg-red-50 p-3 rounded-lg border-l-4 border-red-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-red-800">逆变器功率异常</div>
                                <div class="text-xs text-gray-600">批次INV2025001功率不达标</div>
                                <div class="text-xs text-gray-500">检验员: 李检验员</div>
                            </div>
                            <button onclick="handleFinalAlert('ALERT001')" class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded hover:bg-red-200">
                                立即处理
                            </button>
                        </div>
                    </div>
                    <div class="bg-orange-50 p-3 rounded-lg border-l-4 border-orange-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-orange-800">PACK外观缺陷</div>
                                <div class="text-xs text-gray-600">批次PACK2025002外观划伤</div>
                                <div class="text-xs text-gray-500">检验员: 王检验员</div>
                            </div>
                            <button onclick="handleFinalAlert('ALERT002')" class="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded hover:bg-orange-200">
                                分析中
                            </button>
                        </div>
                    </div>
                    <div class="bg-yellow-50 p-3 rounded-lg border-l-4 border-yellow-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-yellow-800">包装标识错误</div>
                                <div class="text-xs text-gray-600">产品标签信息不匹配</div>
                                <div class="text-xs text-gray-500">检验员: 赵检验员</div>
                            </div>
                            <button onclick="handleFinalAlert('ALERT003')" class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded hover:bg-yellow-200">
                                待处理
                            </button>
                        </div>
                    </div>
                </div>
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">质量趋势</span>
                        <span class="font-medium text-green-600">本月提升: +1.5%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 成品检验记录表格 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">成品检验记录</h3>
                <div class="flex flex-wrap gap-4">
                    <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option>全部产品类型</option>
                        <option>PACK产品</option>
                        <option>PCBA产品</option>
                        <option>逆变器产品</option>
                        <option>包装产品</option>
                    </select>
                    <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option>全部检验状态</option>
                        <option>待检验</option>
                        <option>检验中</option>
                        <option>已合格</option>
                        <option>不合格</option>
                        <option>已放行</option>
                    </select>
                    <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option>全部检验类型</option>
                        <option>FQC检验</option>
                        <option>OQC检验</option>
                        <option>抽样检验</option>
                        <option>全检</option>
                    </select>
                    <input type="date" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                    <input type="text" placeholder="搜索批次号、产品编号..." class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1 min-w-64">
                    <button class="bg-primary text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700">
                        <i class="fas fa-search mr-1"></i>搜索
                    </button>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检验信息</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品信息</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">生产信息</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检验项目</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检验人员</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间进度</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检验状态</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检验结果</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="finalInspectionTableBody">
                        <!-- 成品检验数据将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
                <div class="text-sm text-gray-700" id="recordInfo">
                    显示记录信息
                </div>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">上一页</button>
                    <button class="px-3 py-1 bg-primary text-white rounded text-sm">1</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">2</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">3</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 基于Process.md 2.5.3的成品检验数据模型
        const finalInspectionData = [
            {
                id: 'FQC202501001',
                inspectionCode: 'FQC-INV-001',
                batchNumber: 'INV2025001',
                productCode: 'INV-POWER-5KW',
                productName: '5KW逆变器',
                productType: '逆变器产品',
                quantity: 50,
                unit: 'PCS',
                workOrderCode: 'WO202501001',
                productionLine: '逆变器产线',
                productionDate: '2025-01-15',
                inspectionType: 'FQC',
                inspectionTypeName: 'FQC检验',
                inspectionDate: '2025-01-16',
                inspectionTime: '14:30:00',
                inspector: '李检验员',
                inspectorId: 'FQC001',
                status: 'completed',
                statusName: '已完成',
                inspectionItems: [
                    { item: '外观检查', standard: '无损伤、污染', result: 'pass', actualValue: '合格', notes: '外观良好' },
                    { item: '尺寸测量', standard: '±2mm', result: 'pass', actualValue: '合格', notes: '尺寸符合要求' },
                    { item: '功率测试', standard: '5000±50W', result: 'fail', actualValue: '4920W', notes: '功率不达标' },
                    { item: '效率测试', standard: '>95%', result: 'pass', actualValue: '96.2%', notes: '效率合格' },
                    { item: '绝缘测试', standard: '>1MΩ', result: 'pass', actualValue: '2.5MΩ', notes: '绝缘良好' },
                    { item: '老化测试', standard: '24小时无故障', result: 'pass', actualValue: '正常', notes: '老化合格' }
                ],
                samplingPlan: {
                    method: 'AQL',
                    aqlLevel: '1.0',
                    sampleSize: 8,
                    acceptNumber: 0,
                    rejectNumber: 1
                },
                inspectionResult: {
                    totalSamples: 8,
                    defectSamples: 2,
                    defectRate: 25.0,
                    judgment: 'reject',
                    judgmentName: '不合格'
                },
                releaseStatus: 'rejected',
                releaseStatusName: '不合格',
                releaseDate: '2025-01-16',
                storageLocation: '不合格品区',
                nonconformityType: 'performance',
                nonconformityTypeName: '性能不合格',
                disposalMethod: 'rework',
                disposalMethodName: '返工处理',
                documents: ['检验报告', '不合格通知单', '返工单'],
                photos: ['功率测试.jpg', '不合格标识.jpg'],
                notes: '功率测试不合格，需要返工调整'
            },
            {
                id: 'FQC202501002',
                inspectionCode: 'FQC-PACK-002',
                batchNumber: 'PACK2025002',
                productCode: 'PACK-MAIN-V2.1',
                productName: 'PACK主控模块',
                productType: 'PACK产品',
                quantity: 100,
                unit: 'PCS',
                workOrderCode: 'WO202501002',
                productionLine: 'PACK产线',
                productionDate: '2025-01-16',
                inspectionType: 'FQC',
                inspectionTypeName: 'FQC检验',
                inspectionDate: '2025-01-17',
                inspectionTime: '09:15:00',
                inspector: '王检验员',
                inspectorId: 'FQC002',
                status: 'completed',
                statusName: '已完成',
                inspectionItems: [
                    { item: '外观检查', standard: '无划伤、污染', result: 'pass', actualValue: '合格', notes: '外观良好' },
                    { item: '尺寸测量', standard: '100±0.5mm', result: 'pass', actualValue: '99.8mm', notes: '尺寸合格' },
                    { item: '电气测试', standard: '导通正常', result: 'pass', actualValue: '正常', notes: '电气性能良好' },
                    { item: '功能测试', standard: '所有功能正常', result: 'pass', actualValue: '正常', notes: '功能完整' },
                    { item: '密封测试', standard: 'IP65', result: 'pass', actualValue: 'IP65', notes: '密封合格' }
                ],
                samplingPlan: {
                    method: 'AQL',
                    aqlLevel: '0.65',
                    sampleSize: 13,
                    acceptNumber: 0,
                    rejectNumber: 1
                },
                inspectionResult: {
                    totalSamples: 13,
                    defectSamples: 0,
                    defectRate: 0,
                    judgment: 'accept',
                    judgmentName: '合格'
                },
                releaseStatus: 'released',
                releaseStatusName: '已放行',
                releaseDate: '2025-01-17',
                storageLocation: '成品仓A区-01',
                nonconformityType: null,
                nonconformityTypeName: null,
                disposalMethod: null,
                disposalMethodName: null,
                documents: ['检验报告', '合格证', '放行单'],
                photos: ['外观检查.jpg', '功能测试.jpg'],
                notes: '检验合格，质量稳定，已放行入库'
            },
            {
                id: 'OQC202501003',
                inspectionCode: 'OQC-PCBA-003',
                batchNumber: 'PCBA2025003',
                productCode: 'PCBA-CTRL-V1.5',
                productName: 'PCBA控制板',
                productType: 'PCBA产品',
                quantity: 200,
                unit: 'PCS',
                workOrderCode: 'WO202501003',
                productionLine: 'PCBA产线',
                productionDate: '2025-01-17',
                inspectionType: 'OQC',
                inspectionTypeName: 'OQC检验',
                inspectionDate: '2025-01-17',
                inspectionTime: '16:45:00',
                inspector: '赵检验员',
                inspectorId: 'OQC001',
                status: 'in_progress',
                statusName: '检验中',
                inspectionItems: [
                    { item: '外观检查', standard: '无缺件、偏移', result: 'pass', actualValue: '合格', notes: '外观良好' },
                    { item: '焊点检查', standard: '无虚焊、连焊', result: 'pass', actualValue: '合格', notes: '焊接质量良好' },
                    { item: '元件测试', standard: '功能正常', result: 'pass', actualValue: '正常', notes: '元件功能正常' },
                    { item: '电气测试', standard: '导通正常', result: 'pending', actualValue: '', notes: '测试中' },
                    { item: '包装检查', standard: '包装完整', result: 'pending', actualValue: '', notes: '待检查' }
                ],
                samplingPlan: {
                    method: 'AQL',
                    aqlLevel: '1.0',
                    sampleSize: 20,
                    acceptNumber: 1,
                    rejectNumber: 2
                },
                inspectionResult: {
                    totalSamples: 20,
                    defectSamples: 0,
                    defectRate: 0,
                    judgment: 'pending',
                    judgmentName: '检验中'
                },
                releaseStatus: 'pending',
                releaseStatusName: '待放行',
                releaseDate: null,
                storageLocation: '待检区B-01',
                nonconformityType: null,
                nonconformityTypeName: null,
                disposalMethod: null,
                disposalMethodName: null,
                documents: ['检验记录'],
                photos: ['外观检查.jpg'],
                notes: '检验进行中，预计今日完成'
            },
            {
                id: 'FQC202501004',
                inspectionCode: 'FQC-PKG-004',
                batchNumber: 'PKG2025004',
                productCode: 'PKG-COMPLETE-SET',
                productName: '完整包装产品',
                productType: '包装产品',
                quantity: 80,
                unit: 'SET',
                workOrderCode: 'WO202501004',
                productionLine: '包装产线',
                productionDate: '2025-01-17',
                inspectionType: 'FQC',
                inspectionTypeName: 'FQC检验',
                inspectionDate: null,
                inspectionTime: null,
                inspector: null,
                inspectorId: null,
                status: 'pending',
                statusName: '待检验',
                inspectionItems: [
                    { item: '包装完整性', standard: '包装完整无损', result: 'pending', actualValue: '', notes: '' },
                    { item: '标识检查', standard: '标识清晰正确', result: 'pending', actualValue: '', notes: '' },
                    { item: '配件检查', standard: '配件齐全', result: 'pending', actualValue: '', notes: '' },
                    { item: '说明书检查', standard: '说明书完整', result: 'pending', actualValue: '', notes: '' },
                    { item: '外箱检查', standard: '外箱无损伤', result: 'pending', actualValue: '', notes: '' }
                ],
                samplingPlan: {
                    method: 'AQL',
                    aqlLevel: '2.5',
                    sampleSize: 8,
                    acceptNumber: 1,
                    rejectNumber: 2
                },
                inspectionResult: {
                    totalSamples: 0,
                    defectSamples: 0,
                    defectRate: 0,
                    judgment: 'pending',
                    judgmentName: '待检验'
                },
                releaseStatus: 'pending',
                releaseStatusName: '待检验',
                releaseDate: null,
                storageLocation: '待检区C-01',
                nonconformityType: null,
                nonconformityTypeName: null,
                disposalMethod: null,
                disposalMethodName: null,
                documents: ['生产完工单'],
                photos: [],
                notes: '包装完成，等待质检员安排检验'
            }
        ];

        // 状态映射
        const statusMap = {
            pending: { text: '待检验', class: 'bg-gray-100 text-gray-800', icon: 'fas fa-clock' },
            in_progress: { text: '检验中', class: 'bg-blue-100 text-blue-800', icon: 'fas fa-spinner' },
            completed: { text: '已完成', class: 'bg-green-100 text-green-800', icon: 'fas fa-check-circle' }
        };

        // 放行状态映射
        const releaseStatusMap = {
            pending: { text: '待检验', class: 'bg-gray-100 text-gray-800', icon: 'fas fa-clock' },
            released: { text: '已放行', class: 'bg-green-100 text-green-800', icon: 'fas fa-check' },
            rejected: { text: '不合格', class: 'bg-red-100 text-red-800', icon: 'fas fa-times' }
        };

        // 检验结果映射
        const resultMap = {
            pass: { text: '合格', class: 'text-green-600', icon: 'fas fa-check' },
            fail: { text: '不合格', class: 'text-red-600', icon: 'fas fa-times' },
            pending: { text: '待测试', class: 'text-gray-600', icon: 'fas fa-clock' }
        };

        // 判定结果映射
        const judgmentMap = {
            accept: { text: '合格', class: 'text-green-600', icon: 'fas fa-check-circle' },
            reject: { text: '不合格', class: 'text-red-600', icon: 'fas fa-times-circle' },
            pending: { text: '检验中', class: 'text-blue-600', icon: 'fas fa-spinner' }
        };

        // 检验类型映射
        const inspectionTypeMap = {
            FQC: { text: 'FQC检验', icon: 'fas fa-clipboard-check', color: 'text-blue-600' },
            OQC: { text: 'OQC检验', icon: 'fas fa-shipping-fast', color: 'text-green-600' },
            sampling: { text: '抽样检验', icon: 'fas fa-random', color: 'text-purple-600' },
            full: { text: '全检', icon: 'fas fa-list-check', color: 'text-orange-600' }
        };

        let filteredData = [...finalInspectionData];

        // 渲染成品检验表格
        function renderFinalInspectionTable(dataToRender = filteredData) {
            const tbody = document.getElementById('finalInspectionTableBody');
            tbody.innerHTML = '';

            dataToRender.forEach(inspection => {
                const status = statusMap[inspection.status];
                const releaseStatus = releaseStatusMap[inspection.releaseStatus];
                const judgment = judgmentMap[inspection.inspectionResult.judgment];
                const inspectionType = inspectionTypeMap[inspection.inspectionType];
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 transition-colors';

                row.innerHTML = `
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-blue-600 cursor-pointer hover:underline" onclick="viewFinalInspectionDetail('${inspection.id}')">
                            ${inspection.inspectionCode}
                        </div>
                        <div class="text-xs text-gray-500">批次: ${inspection.batchNumber}</div>
                        <div class="text-xs text-gray-500">工单: ${inspection.workOrderCode}</div>
                        ${inspection.inspectionDate ? `
                            <div class="text-xs text-blue-600">检验: ${inspection.inspectionDate}</div>
                        ` : ''}
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">${inspection.productName}</div>
                        <div class="text-xs text-gray-500">${inspection.productCode}</div>
                        <div class="text-xs text-gray-500">${inspection.productType}</div>
                        <div class="text-xs text-blue-600">${inspection.quantity.toLocaleString()} ${inspection.unit}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${inspection.productionLine}</div>
                        <div class="text-xs text-gray-500">生产日期: ${inspection.productionDate}</div>
                        <div class="flex items-center mt-1">
                            <i class="${inspectionType.icon} ${inspectionType.color} mr-1"></i>
                            <span class="text-xs ${inspectionType.color}">${inspectionType.text}</span>
                        </div>
                    </td>
                    <td class="px-4 py-4">
                        <div class="space-y-1">
                            ${inspection.inspectionItems.slice(0, 3).map(item => `
                                <div class="flex items-center justify-between">
                                    <span class="text-xs text-gray-600">${item.item}</span>
                                    <span class="text-xs ${resultMap[item.result].class}">
                                        <i class="${resultMap[item.result].icon}"></i> ${resultMap[item.result].text}
                                    </span>
                                </div>
                            `).join('')}
                        </div>
                        ${inspection.inspectionItems.length > 3 ? `
                            <button onclick="viewAllInspectionItems('${inspection.id}')" class="text-xs text-blue-600 hover:underline mt-1">
                                查看全部 (${inspection.inspectionItems.length})
                            </button>
                        ` : ''}
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        ${inspection.inspector ? `
                            <div class="text-sm text-gray-900">${inspection.inspector}</div>
                            <div class="text-xs text-gray-500">${inspection.inspectorId}</div>
                        ` : `
                            <div class="text-sm text-gray-600">待分配</div>
                        `}
                        <div class="text-xs text-blue-600">AQL: ${inspection.samplingPlan.aqlLevel}</div>
                        <div class="text-xs text-gray-500">样本: ${inspection.samplingPlan.sampleSize}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">生产: ${inspection.productionDate}</div>
                        ${inspection.inspectionDate ? `
                            <div class="text-xs text-gray-500">检验: ${inspection.inspectionDate}</div>
                            <div class="text-xs text-gray-500">${inspection.inspectionTime}</div>
                        ` : `
                            <div class="text-xs text-gray-500">未开始检验</div>
                        `}
                        ${inspection.releaseDate ? `
                            <div class="text-xs text-green-600">放行: ${inspection.releaseDate}</div>
                        ` : ''}
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${status.class}">
                            <i class="${status.icon} mr-1"></i>
                            ${status.text}
                        </span>
                        ${inspection.inspectionResult.defectRate > 0 ? `
                            <div class="text-xs text-red-600 mt-1">
                                不良率: ${inspection.inspectionResult.defectRate}%
                            </div>
                        ` : ''}
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <span class="text-sm ${judgment.class}">
                                <i class="${judgment.icon} mr-1"></i>
                                ${judgment.text}
                            </span>
                        </div>
                        ${inspection.inspectionResult.totalSamples > 0 ? `
                            <div class="text-xs text-gray-500 mt-1">
                                ${inspection.inspectionResult.totalSamples - inspection.inspectionResult.defectSamples}/${inspection.inspectionResult.totalSamples} 合格
                            </div>
                        ` : ''}
                        <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${releaseStatus.class} mt-1">
                            <i class="${releaseStatus.icon} mr-1"></i>
                            ${releaseStatus.text}
                        </span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-1">
                            <button onclick="viewFinalInspectionDetail('${inspection.id}')" class="text-blue-600 hover:text-blue-900 p-1" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            ${inspection.status === 'pending' ? `
                                <button onclick="startFinalInspection('${inspection.id}')" class="text-green-600 hover:text-green-900 p-1" title="开始检验">
                                    <i class="fas fa-play"></i>
                                </button>
                            ` : ''}
                            ${inspection.status === 'in_progress' ? `
                                <button onclick="continueFinalInspection('${inspection.id}')" class="text-purple-600 hover:text-purple-900 p-1" title="继续检验">
                                    <i class="fas fa-spinner"></i>
                                </button>
                            ` : ''}
                            ${inspection.status === 'completed' && inspection.releaseStatus === 'pending' ? `
                                <button onclick="releaseDecision('${inspection.id}')" class="text-orange-600 hover:text-orange-900 p-1" title="放行决策">
                                    <i class="fas fa-gavel"></i>
                                </button>
                            ` : ''}
                            ${inspection.releaseStatus === 'rejected' ? `
                                <button onclick="handleNonconformity('${inspection.id}')" class="text-red-600 hover:text-red-900 p-1" title="不合格处理">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </button>
                            ` : ''}
                            ${inspection.photos.length > 0 ? `
                                <button onclick="viewPhotos('${inspection.id}')" class="text-indigo-600 hover:text-indigo-900 p-1" title="查看照片">
                                    <i class="fas fa-camera"></i>
                                </button>
                            ` : ''}
                            <button onclick="viewDocuments('${inspection.id}')" class="text-yellow-600 hover:text-yellow-900 p-1" title="查看文档">
                                <i class="fas fa-file-alt"></i>
                            </button>
                        </div>
                    </td>
                `;

                tbody.appendChild(row);
            });

            // 更新记录信息
            document.getElementById('recordInfo').textContent = `显示 1-${dataToRender.length} 条，共 ${finalInspectionData.length} 条记录`;
        }

        // 成品检验操作函数
        function viewFinalInspectionDetail(inspectionId) {
            const inspection = finalInspectionData.find(i => i.id === inspectionId);
            if (inspection) {
                let detailText = `成品检验详情：\n检验编号: ${inspection.inspectionCode}\n批次号: ${inspection.batchNumber}\n产品名称: ${inspection.productName}\n产品编号: ${inspection.productCode}\n数量: ${inspection.quantity.toLocaleString()} ${inspection.unit}`;

                detailText += `\n\n生产信息:\n工单编号: ${inspection.workOrderCode}\n生产线: ${inspection.productionLine}\n生产日期: ${inspection.productionDate}`;

                detailText += `\n\n检验信息:\n检验类型: ${inspectionTypeMap[inspection.inspectionType].text}`;
                if (inspection.inspectionDate) {
                    detailText += `\n检验日期: ${inspection.inspectionDate}\n检验时间: ${inspection.inspectionTime}`;
                }
                if (inspection.inspector) {
                    detailText += `\n检验员: ${inspection.inspector} (${inspection.inspectorId})`;
                }
                detailText += `\n检验状态: ${statusMap[inspection.status].text}`;

                detailText += `\n\n抽样方案:\n方法: ${inspection.samplingPlan.method}\nAQL水平: ${inspection.samplingPlan.aqlLevel}\n样本数: ${inspection.samplingPlan.sampleSize}\n接收数: ${inspection.samplingPlan.acceptNumber}\n拒收数: ${inspection.samplingPlan.rejectNumber}`;

                detailText += `\n\n检验项目:`;
                inspection.inspectionItems.forEach((item, index) => {
                    const resultText = resultMap[item.result].text;
                    detailText += `\n${index + 1}. ${item.item}\n   标准: ${item.standard}\n   结果: ${resultText}`;
                    if (item.actualValue) {
                        detailText += `\n   实测值: ${item.actualValue}`;
                    }
                    if (item.notes) {
                        detailText += `\n   备注: ${item.notes}`;
                    }
                });

                detailText += `\n\n检验结果:\n总样本数: ${inspection.inspectionResult.totalSamples}\n不良样本: ${inspection.inspectionResult.defectSamples}\n不良率: ${inspection.inspectionResult.defectRate}%\n判定结果: ${judgmentMap[inspection.inspectionResult.judgment].text}`;

                detailText += `\n\n放行状态: ${releaseStatusMap[inspection.releaseStatus].text}`;
                if (inspection.releaseDate) {
                    detailText += `\n放行日期: ${inspection.releaseDate}`;
                }
                detailText += `\n存放位置: ${inspection.storageLocation}`;

                if (inspection.nonconformityType) {
                    detailText += `\n\n不合格信息:\n不合格类型: ${inspection.nonconformityTypeName}\n处理方式: ${inspection.disposalMethodName}`;
                }

                if (inspection.notes) {
                    detailText += `\n\n备注: ${inspection.notes}`;
                }

                alert(detailText);
            }
        }

        function handleFinalAlert(alertId) {
            if (confirm(`确认处理成品异常？\n异常ID: ${alertId}\n\n处理措施：\n- 立即隔离问题批次\n- 分析不合格原因\n- 制定纠正措施\n- 通知相关部门`)) {
                alert('成品异常处理完成！\n- 问题批次已隔离\n- 原因分析已完成\n- 纠正措施已制定');
            }
        }

        function startFinalInspection(inspectionId) {
            const inspection = finalInspectionData.find(i => i.id === inspectionId);
            if (inspection) {
                if (confirm(`开始成品检验？\n产品: ${inspection.productName}\n批次: ${inspection.batchNumber}\n\n检验内容：\n- 按AQL ${inspection.samplingPlan.aqlLevel}抽样\n- 执行检验项目\n- 记录检验数据`)) {
                    inspection.status = 'in_progress';
                    inspection.inspectionDate = new Date().toISOString().split('T')[0];
                    inspection.inspectionTime = new Date().toTimeString().split(' ')[0];
                    inspection.inspector = '当前检验员';
                    inspection.inspectorId = 'FQC999';
                    renderFinalInspectionTable();
                    alert('成品检验已开始！\n- 检验员已分配\n- 抽样计划已生成\n- 检验项目已准备');
                }
            }
        }

        function continueFinalInspection(inspectionId) {
            const inspection = finalInspectionData.find(i => i.id === inspectionId);
            if (inspection) {
                if (confirm(`继续成品检验？\n产品: ${inspection.productName}\n\n当前进度：\n- 已完成部分检验项目\n- 继续执行剩余项目\n- 更新检验数据`)) {
                    // 模拟检验进度
                    inspection.inspectionItems.forEach(item => {
                        if (item.result === 'pending') {
                            item.result = Math.random() > 0.1 ? 'pass' : 'fail';
                            if (item.result === 'pass') {
                                item.actualValue = '合格';
                                item.notes = '检验合格';
                            } else {
                                item.actualValue = '不合格';
                                item.notes = '发现不合格';
                            }
                        }
                    });

                    // 检查是否所有项目都完成
                    const allCompleted = inspection.inspectionItems.every(item => item.result !== 'pending');
                    if (allCompleted) {
                        inspection.status = 'completed';
                        const defectItems = inspection.inspectionItems.filter(item => item.result === 'fail').length;
                        inspection.inspectionResult.defectSamples = defectItems * 2; // 模拟不良数量
                        inspection.inspectionResult.defectRate = (inspection.inspectionResult.defectSamples / inspection.inspectionResult.totalSamples * 100).toFixed(1);
                        inspection.inspectionResult.judgment = inspection.inspectionResult.defectSamples <= inspection.samplingPlan.acceptNumber ? 'accept' : 'reject';
                    }

                    renderFinalInspectionTable();
                    alert('检验进度已更新！\n- 检验项目继续进行\n- 检验数据已记录\n' + (allCompleted ? '- 检验已完成' : '- 继续执行检验'));
                }
            }
        }

        function releaseDecision(inspectionId) {
            const inspection = finalInspectionData.find(i => i.id === inspectionId);
            if (inspection) {
                if (confirm(`确认放行决策？\n产品: ${inspection.productName}\n检验结果: ${judgmentMap[inspection.inspectionResult.judgment].text}\n\n决策选项：\n- 合格放行\n- 不合格处理\n- 特采放行`)) {
                    if (inspection.inspectionResult.judgment === 'accept') {
                        inspection.releaseStatus = 'released';
                        inspection.releaseDate = new Date().toISOString().split('T')[0];
                        inspection.storageLocation = '成品仓A区-' + String(Math.floor(Math.random() * 10) + 1).padStart(2, '0');
                    } else {
                        inspection.releaseStatus = 'rejected';
                        inspection.releaseDate = new Date().toISOString().split('T')[0];
                        inspection.storageLocation = '不合格品区';
                        inspection.nonconformityType = 'performance';
                        inspection.nonconformityTypeName = '性能不合格';
                        inspection.disposalMethod = 'rework';
                        inspection.disposalMethodName = '返工处理';
                    }
                    renderFinalInspectionTable();
                    alert('放行决策完成！\n- 决策结果已确认\n- 产品状态已更新\n- 存储位置已分配');
                }
            }
        }

        function handleNonconformity(inspectionId) {
            const inspection = finalInspectionData.find(i => i.id === inspectionId);
            if (inspection) {
                if (confirm(`处理不合格品？\n产品: ${inspection.productName}\n不合格类型: ${inspection.nonconformityTypeName}\n\n处理选项：\n- 返工处理\n- 降级使用\n- 报废处理\n- 退货处理`)) {
                    alert('不合格品处理完成！\n- 处理方式已确定\n- 处理流程已启动\n- 跟踪记录已建立');
                }
            }
        }

        function viewAllInspectionItems(inspectionId) {
            const inspection = finalInspectionData.find(i => i.id === inspectionId);
            if (inspection) {
                let itemsText = `${inspection.productName} - 检验项目：\n\n`;
                inspection.inspectionItems.forEach((item, index) => {
                    const resultText = resultMap[item.result].text;
                    itemsText += `${index + 1}. ${item.item}\n   标准: ${item.standard}\n   结果: ${resultText}`;
                    if (item.actualValue) {
                        itemsText += `\n   实测值: ${item.actualValue}`;
                    }
                    if (item.notes) {
                        itemsText += `\n   备注: ${item.notes}`;
                    }
                    itemsText += '\n\n';
                });
                alert(itemsText);
            }
        }

        function viewPhotos(inspectionId) {
            const inspection = finalInspectionData.find(i => i.id === inspectionId);
            if (inspection) {
                let photosText = `${inspection.productName} - 检验照片：\n\n`;
                if (inspection.photos.length > 0) {
                    inspection.photos.forEach((photo, index) => {
                        photosText += `${index + 1}. ${photo}\n`;
                    });
                    photosText += `\n总计: ${inspection.photos.length}张照片`;
                } else {
                    photosText += '暂无检验照片';
                }
                alert(photosText);
            }
        }

        function viewDocuments(inspectionId) {
            const inspection = finalInspectionData.find(i => i.id === inspectionId);
            if (inspection) {
                let documentsText = `${inspection.productName} - 相关文档：\n\n`;
                if (inspection.documents.length > 0) {
                    inspection.documents.forEach((doc, index) => {
                        documentsText += `${index + 1}. ${doc}\n`;
                    });
                    documentsText += `\n总计: ${inspection.documents.length}份文档`;
                } else {
                    documentsText += '暂无相关文档';
                }
                alert(documentsText);
            }
        }

        // 事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            renderFinalInspectionTable();

            // 成品检验
            document.getElementById('finalInspectionBtn').addEventListener('click', function() {
                alert('成品检验功能：\n- 抽样计划制定\n- 检验项目执行\n- 检验数据记录\n- 不合格品标识\n- 检验过程监控');
            });

            // 结果判定
            document.getElementById('resultJudgmentBtn').addEventListener('click', function() {
                alert('结果判定功能：\n- 检验数据分析\n- 合格性判定\n- 质量等级评定\n- 不合格原因分析\n- 判定结果确认');
            });

            // 合格放行
            document.getElementById('qualifiedReleaseBtn').addEventListener('click', function() {
                alert('合格放行功能：\n- 合格品放行\n- 放行单生成\n- 入库位置分配\n- 状态标识更新\n- 放行记录管理');
            });

            // 不合格处理
            document.getElementById('nonconformityBtn').addEventListener('click', function() {
                alert('不合格处理功能：\n- 不合格品隔离\n- 原因分析调查\n- 处理方式决策\n- 纠正措施制定\n- 处理结果跟踪');
            });

            // 质量分析
            document.getElementById('qualityAnalysisBtn').addEventListener('click', function() {
                alert('质量分析功能：\n- 合格率统计\n- 质量趋势分析\n- 不合格原因分析\n- 质量改进建议\n- 质量报告生成');
            });
        });
    </script>
</body>
</html>
