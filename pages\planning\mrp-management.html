<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>物料需求计划(MRP) - 计划管理 - 数字工厂一体化平台</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">物料需求计划(MRP)管理</h1>
            <p class="text-gray-600">基于Process.md 2.1.13-2.1.14流程：MRP生成(6个控制点)→转采购(4个控制点)，实现BOM展开和净需求计算</p>
        </div>

        <!-- MRP流程状态指示器 -->
        <div class="mb-6">
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">MRP生成流程</h3>
                    <span class="text-sm text-gray-600">基于已发布MPS自动运行</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                            <span class="ml-2 text-sm font-medium text-gray-900">MPS汇总</span>
                        </div>
                        <div class="flex-1 h-1 bg-gray-200 mx-2">
                            <div class="h-1 bg-primary" style="width: 100%"></div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium">2</div>
                            <span class="ml-2 text-sm font-medium text-gray-900">BOM展开</span>
                        </div>
                        <div class="flex-1 h-1 bg-gray-200 mx-2">
                            <div class="h-1 bg-primary" style="width: 90%"></div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">3</div>
                            <span class="ml-2 text-sm text-gray-900">毛需求计算</span>
                        </div>
                        <div class="flex-1 h-1 bg-gray-200 mx-2">
                            <div class="h-1 bg-blue-500" style="width: 70%"></div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">4</div>
                            <span class="ml-2 text-sm text-gray-600">库存检查</span>
                        </div>
                        <div class="flex-1 h-1 bg-gray-200 mx-2">
                            <div class="h-1 bg-gray-300" style="width: 50%"></div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">5</div>
                            <span class="ml-2 text-sm text-gray-600">净需求计算</span>
                        </div>
                        <div class="flex-1 h-1 bg-gray-200 mx-2"></div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">6</div>
                            <span class="ml-2 text-sm text-gray-600">生成计划</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 顶部操作区域 -->
        <div class="flex flex-wrap gap-4 mb-6">
            <button id="runMRPBtn" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                <i class="fas fa-calculator mr-2"></i>
                运行MRP
            </button>
            <button id="bomExpandBtn" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors flex items-center">
                <i class="fas fa-sitemap mr-2"></i>
                BOM展开
            </button>
            <button id="inventoryCheckBtn" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center">
                <i class="fas fa-warehouse mr-2"></i>
                库存检查
            </button>
            <button id="toPurchaseBtn" class="bg-success text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
                <i class="fas fa-shopping-cart mr-2"></i>
                转采购申请
            </button>
            <button id="shortageAlertBtn" class="bg-warning text-white px-4 py-2 rounded-lg hover:bg-yellow-600 transition-colors flex items-center">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                缺料预警
            </button>
            <button class="bg-secondary text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center">
                <i class="fas fa-download mr-2"></i>
                导出计划
            </button>
        </div>

        <!-- MRP统计卡片 - 基于Process.md定义的MRP关键指标 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-blue-600">268</div>
                        <div class="text-sm text-gray-600">物料计划项</div>
                        <div class="text-xs text-gray-500">BOM展开结果</div>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-boxes text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-purple-600">156</div>
                        <div class="text-sm text-gray-600">毛需求项</div>
                        <div class="text-xs text-gray-500">计算完成</div>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-calculator text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-indigo-600">89</div>
                        <div class="text-sm text-gray-600">净需求项</div>
                        <div class="text-xs text-gray-500">库存扣减后</div>
                    </div>
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-minus-circle text-indigo-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-orange-600">23</div>
                        <div class="text-sm text-gray-600">待转采购</div>
                        <div class="text-xs text-gray-500">需要采购</div>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-shopping-cart text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-red-600">7</div>
                        <div class="text-sm text-gray-600">缺料风险</div>
                        <div class="text-xs text-gray-500">需要关注</div>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-green-600">91%</div>
                        <div class="text-sm text-gray-600">齐套率</div>
                        <div class="text-xs text-gray-500">整体水平</div>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- MRP运行控制面板 - 基于Process.md 2.1.13流程 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">MRP运行控制</h3>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-600">运行状态:</span>
                    <span id="mrpStatus" class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                        <i class="fas fa-check-circle mr-1"></i>就绪
                    </span>
                </div>
            </div>

            <!-- 6列控制参数 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">MPS来源</label>
                    <select id="mpsSource" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="all">全部已发布MPS</option>
                        <option value="specific">指定MPS计划</option>
                        <option value="product">特定产品线</option>
                        <option value="urgent">紧急需求</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">BOM版本</label>
                    <select id="bomVersion" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="latest">最新版本</option>
                        <option value="effective">有效版本</option>
                        <option value="specific">指定版本</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">库存数据</label>
                    <select id="inventoryData" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="realtime">实时库存</option>
                        <option value="available">可用库存</option>
                        <option value="safety">安全库存</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">运行模式</label>
                    <select id="runMode" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="full">完整重算</option>
                        <option value="incremental">增量计算</option>
                        <option value="simulation">仿真模式</option>
                        <option value="validation">验证模式</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">计划范围</label>
                    <div class="grid grid-cols-2 gap-1">
                        <input type="date" id="startDate" value="2025-01-16" class="border border-gray-300 rounded-md px-2 py-2 text-xs focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <input type="date" id="endDate" value="2025-03-31" class="border border-gray-300 rounded-md px-2 py-2 text-xs focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">执行选项</label>
                    <div class="space-y-1">
                        <label class="flex items-center text-xs">
                            <input type="checkbox" id="autoToPurchase" class="rounded mr-1">
                            <span>自动转采购</span>
                        </label>
                        <label class="flex items-center text-xs">
                            <input type="checkbox" id="alertShortage" class="rounded mr-1" checked>
                            <span>缺料预警</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- 运行控制按钮 -->
            <div class="flex flex-wrap gap-3">
                <button id="startMRPBtn" class="bg-primary text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center">
                    <i class="fas fa-play mr-2"></i>
                    开始运行MRP
                </button>
                <button id="stopMRPBtn" class="bg-red-600 text-white px-6 py-2 rounded-md hover:bg-red-700 transition-colors flex items-center" disabled>
                    <i class="fas fa-stop mr-2"></i>
                    停止运行
                </button>
                <button id="viewLogBtn" class="bg-secondary text-white px-6 py-2 rounded-md hover:bg-gray-700 transition-colors flex items-center">
                    <i class="fas fa-eye mr-2"></i>
                    查看运行日志
                </button>
                <button id="validateResultBtn" class="bg-purple-600 text-white px-6 py-2 rounded-md hover:bg-purple-700 transition-colors flex items-center">
                    <i class="fas fa-check-double mr-2"></i>
                    验证结果
                </button>
            </div>

            <!-- 运行进度条 -->
            <div id="mrpProgress" class="mt-4 hidden">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">MRP运行进度</span>
                    <span id="progressText" class="text-sm text-gray-600">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
                <div id="currentStep" class="text-xs text-gray-500 mt-1">准备开始...</div>
            </div>
        </div>

        <!-- 物料需求计划列表 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">物料需求计划 - 基于BOM展开和净需求计算</h3>
                <!-- 6列响应式筛选区域 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-4">
                    <select id="statusFilter" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="">全部状态</option>
                        <option value="planning">计划中</option>
                        <option value="locked">已锁定</option>
                        <option value="purchased">已转采购</option>
                        <option value="shortage">缺料风险</option>
                        <option value="sufficient">库存充足</option>
                    </select>
                    <select id="materialTypeFilter" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="">全部物料类型</option>
                        <option value="raw">原材料</option>
                        <option value="semi">半成品</option>
                        <option value="standard">标准件</option>
                        <option value="package">包装材料</option>
                        <option value="consumable">耗材</option>
                    </select>
                    <select id="supplierFilter" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="">全部供应商</option>
                        <option value="supplier-a">华东金属材料</option>
                        <option value="supplier-b">精密标准件</option>
                        <option value="supplier-c">电子元器件</option>
                        <option value="supplier-d">包装材料</option>
                    </select>
                    <select id="urgencyFilter" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="">全部紧急度</option>
                        <option value="urgent">紧急</option>
                        <option value="normal">正常</option>
                        <option value="low">低优先级</option>
                    </select>
                    <input type="date" id="dateFilter" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="需求日期">
                    <div class="flex gap-2">
                        <input type="text" id="searchInput" placeholder="搜索物料编码、名称..." class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <button onclick="searchMaterials()" class="bg-primary text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 transition-colors">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <!-- 快速操作按钮 -->
                <div class="flex flex-wrap gap-2">
                    <button onclick="showShortageOnly()" class="px-3 py-1 text-xs bg-red-100 text-red-800 rounded-full hover:bg-red-200 transition-colors">
                        <i class="fas fa-exclamation-triangle mr-1"></i>仅显示缺料
                    </button>
                    <button onclick="showToPurchaseOnly()" class="px-3 py-1 text-xs bg-orange-100 text-orange-800 rounded-full hover:bg-orange-200 transition-colors">
                        <i class="fas fa-shopping-cart mr-1"></i>仅显示待采购
                    </button>
                    <button onclick="showUrgentOnly()" class="px-3 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full hover:bg-yellow-200 transition-colors">
                        <i class="fas fa-clock mr-1"></i>仅显示紧急
                    </button>
                    <button onclick="clearFilters()" class="px-3 py-1 text-xs bg-gray-100 text-gray-800 rounded-full hover:bg-gray-200 transition-colors">
                        <i class="fas fa-times mr-1"></i>清除筛选
                    </button>
                </div>
            </div>

            <!-- 物料需求计划表格 - 12列数据表格 -->
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" id="selectAll" class="rounded">
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">物料信息</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">物料类型</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联MPS</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">毛需求</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">现有库存</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">在途数量</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">净需求</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">建议采购</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">需求日期</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">供应商</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="mrpTableBody">
                        <!-- MRP物料计划数据将通过JavaScript动态生成 -->

                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
                <div class="text-sm text-gray-700" id="recordInfo">
                    显示记录信息
                </div>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">上一页</button>
                    <button class="px-3 py-1 bg-primary text-white rounded text-sm">1</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">2</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">3</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 基于Process.md 2.1.13-2.1.14的MRP数据模型
        const mrpData = [
            {
                id: 'MRP202501001',
                materialCode: 'MT001',
                materialName: '硅钢片',
                materialSpec: '0.5mm*1200mm',
                materialType: 'raw',
                materialTypeName: '原材料',
                relatedMPS: 'MPS202501001',
                grossRequirement: 5000,
                currentInventory: 1200,
                inTransit: 2000,
                netRequirement: 1800,
                suggestedPurchase: 2000,
                requirementDate: '2025-02-01',
                supplier: 'supplier-a',
                supplierName: '华东金属材料',
                status: 'shortage',
                urgency: 'urgent',
                bomLevel: 1,
                leadTime: 7,
                safetyStock: 500
            },
            {
                id: 'MRP202501002',
                materialCode: 'MT002',
                materialName: '铜线',
                materialSpec: '2.5mm²',
                materialType: 'raw',
                materialTypeName: '原材料',
                relatedMPS: 'MPS202501001',
                grossRequirement: 3000,
                currentInventory: 5000,
                inTransit: 0,
                netRequirement: 0,
                suggestedPurchase: 0,
                requirementDate: '2025-02-05',
                supplier: 'supplier-c',
                supplierName: '电子元器件',
                status: 'sufficient',
                urgency: 'normal',
                bomLevel: 1,
                leadTime: 5,
                safetyStock: 1000
            },
            {
                id: 'MRP202501003',
                materialCode: 'ST001',
                materialName: 'M6螺栓',
                materialSpec: '不锈钢304',
                materialType: 'standard',
                materialTypeName: '标准件',
                relatedMPS: 'MPS202501002',
                grossRequirement: 2000,
                currentInventory: 500,
                inTransit: 1000,
                netRequirement: 500,
                suggestedPurchase: 1000,
                requirementDate: '2025-02-10',
                supplier: 'supplier-b',
                supplierName: '精密标准件',
                status: 'purchased',
                urgency: 'normal',
                bomLevel: 2,
                leadTime: 3,
                safetyStock: 200
            },
            {
                id: 'MRP202501004',
                materialCode: 'PK001',
                materialName: '包装箱',
                materialSpec: '600*400*300mm',
                materialType: 'package',
                materialTypeName: '包装材料',
                relatedMPS: 'MPS202501003',
                grossRequirement: 100,
                currentInventory: 50,
                inTransit: 0,
                netRequirement: 50,
                suggestedPurchase: 100,
                requirementDate: '2025-02-15',
                supplier: 'supplier-d',
                supplierName: '包装材料',
                status: 'planning',
                urgency: 'low',
                bomLevel: 1,
                leadTime: 2,
                safetyStock: 20
            },
            {
                id: 'MRP202501005',
                materialCode: 'SM001',
                materialName: 'PCBA主板',
                materialSpec: 'V2.1版本',
                materialType: 'semi',
                materialTypeName: '半成品',
                relatedMPS: 'MPS202501001',
                grossRequirement: 100,
                currentInventory: 20,
                inTransit: 30,
                netRequirement: 50,
                suggestedPurchase: 80,
                requirementDate: '2025-01-28',
                supplier: 'supplier-c',
                supplierName: '电子元器件',
                status: 'shortage',
                urgency: 'urgent',
                bomLevel: 1,
                leadTime: 10,
                safetyStock: 10
            }
        ];

        // 状态映射
        const statusMap = {
            planning: { text: '计划中', class: 'bg-blue-100 text-blue-800', icon: 'fas fa-clock' },
            locked: { text: '已锁定', class: 'bg-purple-100 text-purple-800', icon: 'fas fa-lock' },
            purchased: { text: '已转采购', class: 'bg-green-100 text-green-800', icon: 'fas fa-shopping-cart' },
            shortage: { text: '缺料风险', class: 'bg-red-100 text-red-800', icon: 'fas fa-exclamation-triangle' },
            sufficient: { text: '库存充足', class: 'bg-gray-100 text-gray-800', icon: 'fas fa-check-circle' }
        };

        // 紧急度映射
        const urgencyMap = {
            urgent: { text: '紧急', class: 'bg-red-100 text-red-800', icon: 'fas fa-exclamation-triangle' },
            normal: { text: '正常', class: 'bg-blue-100 text-blue-800', icon: 'fas fa-minus' },
            low: { text: '低优先级', class: 'bg-gray-100 text-gray-800', icon: 'fas fa-arrow-down' }
        };

        // 物料类型映射
        const materialTypeMap = {
            raw: { text: '原材料', icon: 'fas fa-industry', color: 'text-blue-600' },
            semi: { text: '半成品', icon: 'fas fa-cogs', color: 'text-purple-600' },
            standard: { text: '标准件', icon: 'fas fa-tools', color: 'text-green-600' },
            package: { text: '包装材料', icon: 'fas fa-box', color: 'text-orange-600' },
            consumable: { text: '耗材', icon: 'fas fa-tint', color: 'text-gray-600' }
        };

        let filteredData = [...mrpData];

        // 渲染MRP表格
        function renderMRPTable(dataToRender = filteredData) {
            const tbody = document.getElementById('mrpTableBody');
            tbody.innerHTML = '';

            dataToRender.forEach(mrp => {
                const status = statusMap[mrp.status];
                const urgency = urgencyMap[mrp.urgency];
                const materialType = materialTypeMap[mrp.materialType];
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 transition-colors';

                row.innerHTML = `
                    <td class="px-4 py-4 whitespace-nowrap">
                        <input type="checkbox" class="rounded mrp-checkbox" data-id="${mrp.id}">
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-blue-600 cursor-pointer hover:underline" onclick="viewMaterialDetail('${mrp.id}')">
                            ${mrp.materialCode}
                        </div>
                        <div class="text-sm text-gray-900">${mrp.materialName}</div>
                        <div class="text-xs text-gray-500">${mrp.materialSpec}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <i class="${materialType.icon} ${materialType.color} mr-2"></i>
                            <span class="text-sm text-gray-900">${materialType.text}</span>
                        </div>
                        <div class="text-xs text-gray-500">BOM层级: ${mrp.bomLevel}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-blue-600 cursor-pointer hover:underline" onclick="viewMPSDetail('${mrp.relatedMPS}')">
                            ${mrp.relatedMPS}
                        </div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-center">
                        <span class="text-sm font-medium text-gray-900">${mrp.grossRequirement.toLocaleString()}</span>
                        <div class="text-xs text-gray-500">${getUnit(mrp.materialType)}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-center">
                        <span class="text-sm text-gray-900">${mrp.currentInventory.toLocaleString()}</span>
                        <div class="text-xs text-gray-500">安全库存: ${mrp.safetyStock}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-center">
                        <span class="text-sm text-gray-900">${mrp.inTransit.toLocaleString()}</span>
                        ${mrp.inTransit > 0 ? '<div class="text-xs text-blue-600">在途中</div>' : '<div class="text-xs text-gray-500">无在途</div>'}
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-center">
                        <span class="text-sm font-medium ${mrp.netRequirement > 0 ? 'text-red-600' : 'text-green-600'}">${mrp.netRequirement.toLocaleString()}</span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-center">
                        <span class="text-sm font-medium ${mrp.suggestedPurchase > 0 ? 'text-blue-600' : 'text-gray-500'}">${mrp.suggestedPurchase.toLocaleString()}</span>
                        ${mrp.suggestedPurchase > 0 ? '<div class="text-xs text-blue-600">建议采购</div>' : '<div class="text-xs text-gray-500">无需采购</div>'}
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <span class="text-sm text-gray-900">${mrp.requirementDate}</span>
                        ${isDateUrgent(mrp.requirementDate) ? '<div class="text-xs text-red-600"><i class="fas fa-exclamation-triangle mr-1"></i>临期</div>' : ''}
                        <div class="text-xs text-gray-500">交期: ${mrp.leadTime}天</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${mrp.supplierName}</div>
                        <div class="text-xs text-gray-500">${mrp.supplier}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${status.class}">
                            <i class="${status.icon} mr-1"></i>
                            ${status.text}
                        </span>
                        <div class="mt-1">
                            <span class="inline-flex items-center px-1 py-0.5 text-xs rounded-full ${urgency.class}">
                                <i class="${urgency.icon} mr-1"></i>
                                ${urgency.text}
                            </span>
                        </div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-1">
                            <button onclick="viewMaterialDetail('${mrp.id}')" class="text-blue-600 hover:text-blue-900 p-1" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            ${mrp.suggestedPurchase > 0 ? `
                                <button onclick="toPurchase('${mrp.id}')" class="text-green-600 hover:text-green-900 p-1" title="转采购申请">
                                    <i class="fas fa-shopping-cart"></i>
                                </button>
                            ` : ''}
                            <button onclick="viewBOM('${mrp.materialCode}')" class="text-purple-600 hover:text-purple-900 p-1" title="查看BOM">
                                <i class="fas fa-sitemap"></i>
                            </button>
                            ${mrp.status === 'planning' ? `
                                <button onclick="lockMaterial('${mrp.id}')" class="text-orange-600 hover:text-orange-900 p-1" title="锁定计划">
                                    <i class="fas fa-lock"></i>
                                </button>
                            ` : ''}
                        </div>
                    </td>
                `;

                tbody.appendChild(row);
            });

            // 更新记录信息
            document.getElementById('recordInfo').textContent = `显示 1-${dataToRender.length} 条，共 ${mrpData.length} 条记录`;
        }

        // 获取物料单位
        function getUnit(materialType) {
            const units = {
                raw: 'kg',
                semi: '个',
                standard: '个',
                package: '个',
                consumable: 'L'
            };
            return units[materialType] || '个';
        }

        // 判断日期是否紧急（7天内）
        function isDateUrgent(dateStr) {
            const targetDate = new Date(dateStr);
            const today = new Date();
            const diffTime = targetDate - today;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return diffDays <= 7 && diffDays >= 0;
        }

        // 搜索物料
        function searchMaterials() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const materialTypeFilter = document.getElementById('materialTypeFilter').value;
            const supplierFilter = document.getElementById('supplierFilter').value;
            const urgencyFilter = document.getElementById('urgencyFilter').value;
            const dateFilter = document.getElementById('dateFilter').value;

            filteredData = mrpData.filter(mrp => {
                const matchesSearch = !searchTerm ||
                    mrp.materialCode.toLowerCase().includes(searchTerm) ||
                    mrp.materialName.toLowerCase().includes(searchTerm);

                const matchesStatus = !statusFilter || mrp.status === statusFilter;
                const matchesMaterialType = !materialTypeFilter || mrp.materialType === materialTypeFilter;
                const matchesSupplier = !supplierFilter || mrp.supplier === supplierFilter;
                const matchesUrgency = !urgencyFilter || mrp.urgency === urgencyFilter;
                const matchesDate = !dateFilter || mrp.requirementDate === dateFilter;

                return matchesSearch && matchesStatus && matchesMaterialType && matchesSupplier && matchesUrgency && matchesDate;
            });

            renderMRPTable();
        }

        // 快速筛选函数
        function showShortageOnly() {
            document.getElementById('statusFilter').value = 'shortage';
            searchMaterials();
        }

        function showToPurchaseOnly() {
            filteredData = mrpData.filter(mrp => mrp.suggestedPurchase > 0);
            renderMRPTable();
        }

        function showUrgentOnly() {
            document.getElementById('urgencyFilter').value = 'urgent';
            searchMaterials();
        }

        function clearFilters() {
            document.getElementById('statusFilter').value = '';
            document.getElementById('materialTypeFilter').value = '';
            document.getElementById('supplierFilter').value = '';
            document.getElementById('urgencyFilter').value = '';
            document.getElementById('dateFilter').value = '';
            document.getElementById('searchInput').value = '';
            filteredData = [...mrpData];
            renderMRPTable();
        }

        // MRP操作函数 - 基于Process.md的关键控制点
        function viewMaterialDetail(mrpId) {
            const mrp = mrpData.find(m => m.id === mrpId);
            if (mrp) {
                alert(`物料详情：\n编码：${mrp.materialCode}\n名称：${mrp.materialName}\n规格：${mrp.materialSpec}\n毛需求：${mrp.grossRequirement}\n现有库存：${mrp.currentInventory}\n净需求：${mrp.netRequirement}\n建议采购：${mrp.suggestedPurchase}\n供应商：${mrp.supplierName}\n交期：${mrp.leadTime}天`);
            }
        }

        function toPurchase(mrpId) {
            if (confirm('确认转为采购申请？转换后将进入采购流程。')) {
                const mrp = mrpData.find(m => m.id === mrpId);
                if (mrp) {
                    mrp.status = 'purchased';
                    renderMRPTable();
                    alert('已转为采购申请！采购部门将收到采购需求。');
                }
            }
        }

        function viewBOM(materialCode) {
            alert(`BOM展开详情：\n物料编码：${materialCode}\n- 显示完整的BOM结构\n- 各层级物料清单\n- 用量和损耗率\n- 替代料信息`);
        }

        function lockMaterial(mrpId) {
            if (confirm('确认锁定物料计划？锁定后不可随意修改。')) {
                const mrp = mrpData.find(m => m.id === mrpId);
                if (mrp) {
                    mrp.status = 'locked';
                    renderMRPTable();
                    alert('物料计划已锁定！');
                }
            }
        }

        // MRP运行控制函数
        function startMRPRun() {
            const mrpStatus = document.getElementById('mrpStatus');
            const progressDiv = document.getElementById('mrpProgress');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            const currentStep = document.getElementById('currentStep');
            const startBtn = document.getElementById('startMRPBtn');
            const stopBtn = document.getElementById('stopMRPBtn');

            // 更新状态
            mrpStatus.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>运行中';
            mrpStatus.className = 'inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800';

            // 显示进度条
            progressDiv.classList.remove('hidden');
            startBtn.disabled = true;
            stopBtn.disabled = false;

            // 模拟MRP运行过程
            const steps = [
                { text: '汇总MPS数据...', progress: 10 },
                { text: 'BOM展开计算...', progress: 30 },
                { text: '毛需求计算...', progress: 50 },
                { text: '库存数据检查...', progress: 70 },
                { text: '净需求计算...', progress: 90 },
                { text: '生成物料计划...', progress: 100 }
            ];

            let currentStepIndex = 0;
            const interval = setInterval(() => {
                if (currentStepIndex < steps.length) {
                    const step = steps[currentStepIndex];
                    progressBar.style.width = step.progress + '%';
                    progressText.textContent = step.progress + '%';
                    currentStep.textContent = step.text;
                    currentStepIndex++;
                } else {
                    clearInterval(interval);

                    // 完成状态
                    mrpStatus.innerHTML = '<i class="fas fa-check-circle mr-1"></i>完成';
                    mrpStatus.className = 'inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800';
                    currentStep.textContent = 'MRP运行完成！';

                    setTimeout(() => {
                        progressDiv.classList.add('hidden');
                        startBtn.disabled = false;
                        stopBtn.disabled = true;
                        mrpStatus.innerHTML = '<i class="fas fa-check-circle mr-1"></i>就绪';
                        mrpStatus.className = 'inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800';

                        // 刷新表格数据
                        renderMRPTable();
                        alert('MRP运行完成！物料需求计划已更新。');
                    }, 2000);
                }
            }, 1000);
        }

        // 事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            renderMRPTable();

            // 运行MRP
            document.getElementById('runMRPBtn').addEventListener('click', function() {
                startMRPRun();
            });

            // BOM展开
            document.getElementById('bomExpandBtn').addEventListener('click', function() {
                alert('BOM展开功能：\n- 基于最新BOM版本\n- 多层级展开计算\n- 考虑损耗率和替代料\n- 生成完整物料清单');
            });

            // 库存检查
            document.getElementById('inventoryCheckBtn').addEventListener('click', function() {
                alert('库存检查功能：\n- 实时库存数据\n- 可用库存计算\n- 安全库存检查\n- 在途物料统计');
            });

            // 转采购申请
            document.getElementById('toPurchaseBtn').addEventListener('click', function() {
                const checkedBoxes = document.querySelectorAll('.mrp-checkbox:checked');
                if (checkedBoxes.length === 0) {
                    alert('请选择要转为采购申请的物料！');
                    return;
                }

                if (confirm(`确认将选中的 ${checkedBoxes.length} 个物料转为采购申请？`)) {
                    checkedBoxes.forEach(checkbox => {
                        const mrpId = checkbox.dataset.id;
                        const mrp = mrpData.find(m => m.id === mrpId);
                        if (mrp && mrp.suggestedPurchase > 0) {
                            mrp.status = 'purchased';
                        }
                    });
                    renderMRPTable();
                    alert('批量转采购申请完成！');
                }
            });

            // 缺料预警
            document.getElementById('shortageAlertBtn').addEventListener('click', function() {
                const shortageItems = mrpData.filter(mrp => mrp.status === 'shortage');
                if (shortageItems.length > 0) {
                    let alertText = `发现 ${shortageItems.length} 个缺料风险项：\n`;
                    shortageItems.forEach(item => {
                        alertText += `- ${item.materialName} (${item.materialCode}): 缺料 ${item.netRequirement}\n`;
                    });
                    alert(alertText);
                } else {
                    alert('当前无缺料风险！');
                }
            });

            // 全选功能
            document.getElementById('selectAll').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('.mrp-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });
        });
    </script>
</body>
</html>
