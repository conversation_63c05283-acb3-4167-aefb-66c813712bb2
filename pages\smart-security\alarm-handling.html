<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报警事件处理 - 慧新全智厂园一体平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-bell text-primary mr-3"></i>
                报警事件处理中心
            </h1>
            <p class="text-gray-600 mt-2">智能报警监控，快速事件响应</p>
        </div>

        <!-- 报警概览统计 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">今日报警</h3>
                        <p class="text-3xl font-bold text-red-600 mt-2">12</p>
                        <p class="text-sm text-gray-500">件</p>
                    </div>
                    <div class="bg-red-100 p-3 rounded-full">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>已处理:</span>
                        <span class="text-green-600 font-medium">10件</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>处理中:</span>
                        <span class="text-yellow-600 font-medium">2件</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">响应时间</h3>
                        <p class="text-3xl font-bold text-blue-600 mt-2">2.5</p>
                        <p class="text-sm text-gray-500">分钟</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-clock text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>目标:</span>
                        <span class="text-blue-600 font-medium">≤5分钟</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>达标率:</span>
                        <span class="text-green-600 font-medium">100%</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">处理率</h3>
                        <p class="text-3xl font-bold text-green-600 mt-2">83.3%</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>已完成:</span>
                        <span class="text-green-600 font-medium">10件</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>总数:</span>
                        <span class="text-gray-600 font-medium">12件</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">误报率</h3>
                        <p class="text-3xl font-bold text-yellow-600 mt-2">8.3%</p>
                    </div>
                    <div class="bg-yellow-100 p-3 rounded-full">
                        <i class="fas fa-chart-pie text-yellow-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>误报:</span>
                        <span class="text-yellow-600 font-medium">1件</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>真实:</span>
                        <span class="text-green-600 font-medium">11件</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时报警事件 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-bell text-red-600 mr-2"></i>
                实时报警事件
            </h3>
            <div class="space-y-4">
                <div class="border border-red-200 bg-red-50 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">紧急</span>
                            <h4 class="font-semibold text-gray-800">火灾报警</h4>
                        </div>
                        <span class="text-sm text-gray-500">14:25:30</span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-3">
                        <div>
                            <span class="text-sm text-gray-600">报警位置:</span>
                            <p class="font-medium text-red-600">生产车间2</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">报警类型:</span>
                            <p class="font-medium">烟雾检测</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">报警设备:</span>
                            <p class="font-medium">FD-002</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">处理状态:</span>
                            <p class="font-medium text-yellow-600">处理中</p>
                        </div>
                    </div>
                    <div class="text-sm text-gray-700 mb-3">
                        <p>生产车间2检测到烟雾浓度异常，已自动启动消防系统，消防队伍正在赶往现场。</p>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            <span>处理人员: 张消防员 | 响应时间: 1.5分钟 | 预计到达: 3分钟</span>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700">
                                紧急处理
                            </button>
                            <button class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                                查看监控
                            </button>
                        </div>
                    </div>
                </div>

                <div class="border border-yellow-200 bg-yellow-50 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">警告</span>
                            <h4 class="font-semibold text-gray-800">入侵检测</h4>
                        </div>
                        <span class="text-sm text-gray-500">14:20:15</span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-3">
                        <div>
                            <span class="text-sm text-gray-600">报警位置:</span>
                            <p class="font-medium text-yellow-600">周界北侧</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">报警类型:</span>
                            <p class="font-medium">红外感应</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">报警设备:</span>
                            <p class="font-medium">IR-015</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">处理状态:</span>
                            <p class="font-medium text-yellow-600">处理中</p>
                        </div>
                    </div>
                    <div class="text-sm text-gray-700 mb-3">
                        <p>周界北侧红外感应器检测到异常活动，安保人员正在前往现场确认情况。</p>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            <span>处理人员: 李安保员 | 响应时间: 2.0分钟 | 预计到达: 5分钟</span>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700">
                                现场确认
                            </button>
                            <button class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                                查看监控
                            </button>
                        </div>
                    </div>
                </div>

                <div class="border border-green-200 bg-green-50 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">已处理</span>
                            <h4 class="font-semibold text-gray-800">设备故障</h4>
                        </div>
                        <span class="text-sm text-gray-500">13:45:20</span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-3">
                        <div>
                            <span class="text-sm text-gray-600">报警位置:</span>
                            <p class="font-medium text-green-600">东门入口</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">报警类型:</span>
                            <p class="font-medium">门禁故障</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">报警设备:</span>
                            <p class="font-medium">AC-001</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">处理状态:</span>
                            <p class="font-medium text-green-600">已完成</p>
                        </div>
                    </div>
                    <div class="text-sm text-gray-700 mb-3">
                        <p>东门入口门禁设备通信故障，已完成设备重启和网络连接修复，设备恢复正常运行。</p>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            <span>处理人员: 王技术员 | 处理时长: 25分钟 | 完成时间: 14:10</span>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700">
                                查看报告
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 报警统计分析 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-chart-bar text-purple-600 mr-2"></i>
                    报警类型统计
                </h3>
                <div class="space-y-4">
                    <div class="bg-gradient-to-r from-red-50 to-pink-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">火灾报警</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div class="flex justify-between">
                                <span>今日次数:</span>
                                <span class="font-medium text-red-600">2次</span>
                            </div>
                            <div class="flex justify-between">
                                <span>平均响应:</span>
                                <span class="font-medium">1.8分钟</span>
                            </div>
                            <div class="flex justify-between">
                                <span>处理状态:</span>
                                <span class="font-medium text-yellow-600">1处理中</span>
                            </div>
                        </div>
                        <div class="mt-2">
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-red-500 h-2 rounded-full" style="width: 50%"></div>
                            </div>
                            <div class="text-xs text-gray-500 mt-1">处理进度: 50%</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">入侵检测</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div class="flex justify-between">
                                <span>今日次数:</span>
                                <span class="font-medium text-yellow-600">5次</span>
                            </div>
                            <div class="flex justify-between">
                                <span>平均响应:</span>
                                <span class="font-medium">2.2分钟</span>
                            </div>
                            <div class="flex justify-between">
                                <span>处理状态:</span>
                                <span class="font-medium text-yellow-600">1处理中</span>
                            </div>
                        </div>
                        <div class="mt-2">
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-yellow-500 h-2 rounded-full" style="width: 80%"></div>
                            </div>
                            <div class="text-xs text-gray-500 mt-1">处理进度: 80%</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">设备故障</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div class="flex justify-between">
                                <span>今日次数:</span>
                                <span class="font-medium text-blue-600">3次</span>
                            </div>
                            <div class="flex justify-between">
                                <span>平均响应:</span>
                                <span class="font-medium">3.5分钟</span>
                            </div>
                            <div class="flex justify-between">
                                <span>处理状态:</span>
                                <span class="font-medium text-green-600">全部完成</span>
                            </div>
                        </div>
                        <div class="mt-2">
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: 100%"></div>
                            </div>
                            <div class="text-xs text-gray-500 mt-1">处理进度: 100%</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-users text-green-600 mr-2"></i>
                    处理人员状态
                </h3>
                <div class="space-y-4">
                    <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">消防应急组</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div class="flex justify-between">
                                <span>总人数:</span>
                                <span class="font-medium">24人</span>
                            </div>
                            <div class="flex justify-between">
                                <span>在岗人员:</span>
                                <span class="font-medium text-green-600">22人</span>
                            </div>
                            <div class="flex justify-between">
                                <span>出警人员:</span>
                                <span class="font-medium text-red-600">2人</span>
                            </div>
                            <div class="flex justify-between">
                                <span>可用率:</span>
                                <span class="font-medium text-green-600">91.7%</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">安保巡逻组</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div class="flex justify-between">
                                <span>总人数:</span>
                                <span class="font-medium">18人</span>
                            </div>
                            <div class="flex justify-between">
                                <span>在岗人员:</span>
                                <span class="font-medium text-green-600">16人</span>
                            </div>
                            <div class="flex justify-between">
                                <span>出警人员:</span>
                                <span class="font-medium text-yellow-600">1人</span>
                            </div>
                            <div class="flex justify-between">
                                <span>可用率:</span>
                                <span class="font-medium text-green-600">88.9%</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">技术维护组</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div class="flex justify-between">
                                <span>总人数:</span>
                                <span class="font-medium">12人</span>
                            </div>
                            <div class="flex justify-between">
                                <span>在岗人员:</span>
                                <span class="font-medium text-green-600">12人</span>
                            </div>
                            <div class="flex justify-between">
                                <span>出警人员:</span>
                                <span class="font-medium">0人</span>
                            </div>
                            <div class="flex justify-between">
                                <span>可用率:</span>
                                <span class="font-medium text-green-600">100%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 报警设备管理 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-cogs text-blue-600 mr-2"></i>
                报警设备管理
            </h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">位置</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">今日报警</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">FD-002</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">烟雾检测器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">生产车间2</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">报警中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1次</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 cursor-pointer hover:underline">查看详情</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">IR-015</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">红外感应器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">周界北侧</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">报警中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1次</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 cursor-pointer hover:underline">查看详情</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">AC-001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">门禁控制器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">东门入口</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">正常</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1次(已处理)</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 cursor-pointer hover:underline">查看详情</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">GS-008</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">燃气检测器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">食堂厨房</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">正常</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">0次</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 cursor-pointer hover:underline">查看详情</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-bolt text-blue-600 mr-2"></i>
                快速操作
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button class="flex flex-col items-center p-4 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors">
                    <i class="fas fa-exclamation-triangle text-red-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">紧急报警</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
                    <i class="fas fa-users text-blue-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">调度人员</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                    <i class="fas fa-chart-bar text-green-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">统计报告</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors">
                    <i class="fas fa-cog text-purple-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">系统设置</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 报警事件处理功能
        function initAlarmHandling() {
            console.log('初始化报警事件处理功能');
            
            // 报警事件处理按钮事件
            const alarmButtons = document.querySelectorAll('button');
            alarmButtons.forEach(button => {
                const text = button.textContent.trim();
                if (text.includes('紧急处理')) {
                    button.addEventListener('click', function() {
                        console.log('紧急处理火灾报警');
                        if (confirm('确认启动紧急处理程序？')) {
                            alert('紧急处理程序已启动，消防队伍正在赶往现场！');
                        }
                    });
                } else if (text.includes('现场确认')) {
                    button.addEventListener('click', function() {
                        console.log('现场确认入侵检测');
                        alert('已通知安保人员前往现场确认情况');
                    });
                } else if (text.includes('查看监控')) {
                    button.addEventListener('click', function() {
                        console.log('查看相关监控');
                        alert('正在调取相关区域监控画面...');
                    });
                } else if (text.includes('查看报告')) {
                    button.addEventListener('click', function() {
                        console.log('查看处理报告');
                        alert('正在查看事件处理详细报告...');
                    });
                } else if (text.includes('紧急报警')) {
                    button.addEventListener('click', function() {
                        console.log('手动触发紧急报警');
                        if (confirm('确认手动触发紧急报警？')) {
                            alert('紧急报警已触发！');
                        }
                    });
                }
            });
            
            // 设备管理表格操作
            const tableLinks = document.querySelectorAll('.cursor-pointer');
            tableLinks.forEach(link => {
                link.addEventListener('click', function() {
                    const deviceId = this.closest('tr').querySelector('td').textContent;
                    console.log('查看设备详情:', deviceId);
                    alert(`正在查看设备 ${deviceId} 的详细信息...`);
                });
            });
            
            // 实时数据更新
            function updateAlarmData() {
                console.log('更新报警数据');
                // 这里可以添加实时数据更新逻辑
            }
            
            // 启动定时更新
            setInterval(updateAlarmData, 10000); // 每10秒更新一次
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initAlarmHandling();
            console.log('报警事件处理页面加载完成');
        });
    </script>
</body>
</html>
