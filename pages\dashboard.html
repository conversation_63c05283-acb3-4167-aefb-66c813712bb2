<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 数字工厂一体化平台</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- AI助手样式 -->
    <link rel="stylesheet" href="../assets/css/ai-assistant.css">

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 font-sans">
    <div class="p-6">
        <!-- 智能制造模块 -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-rocket text-primary mr-2"></i>
                智能制造
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                <!-- 计划管理卡片 -->
                <div class="bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-lg p-4 border border-indigo-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" onclick="parent.switchModule('planning')">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-indigo-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-calendar-alt text-white text-xl"></i>
                        </div>
                        <i class="fas fa-arrow-right text-indigo-600"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">计划管理</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">需求管理、产能管理、MPS/MRP</p>
                    <button class="w-full bg-indigo-600 text-white py-2 px-4 rounded-md text-sm hover:bg-indigo-700 transition-colors mt-auto" onclick="parent.switchModule('planning'); return false;">
                        进入模块
                    </button>
                </div>

                <!-- 生产管理卡片 -->
                <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" id="production-card">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center">
                            <i class="fas fa-cogs text-white text-xl"></i>
                        </div>
                        <i class="fas fa-arrow-right text-primary"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">生产管理</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">工单管理、派工管理、生产计划</p>
                    <button class="w-full bg-primary text-white py-2 px-4 rounded-md text-sm hover:bg-blue-700 transition-colors mt-auto" id="production-button">
                        进入模块
                    </button>
                </div>

                <!-- 仓储管理卡片 -->
                <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-4 border border-purple-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" id="inventory-card">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-boxes text-white text-xl"></i>
                        </div>
                        <i class="fas fa-arrow-right text-purple-600"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">仓储管理</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">原料管理、成品管理、库存预警</p>
                    <button class="w-full bg-purple-600 text-white py-2 px-4 rounded-md text-sm hover:bg-purple-700 transition-colors mt-auto" id="inventory-button">
                        进入模块
                    </button>
                </div>

                <!-- 厂内物流卡片 -->
                <div class="bg-gradient-to-br from-teal-50 to-teal-100 rounded-lg p-4 border border-teal-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" id="logistics-card">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-truck text-white text-xl"></i>
                        </div>
                        <i class="fas fa-arrow-right text-teal-600"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">厂内物流</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">物料配送、运输管理、仓储调度</p>
                    <button class="w-full bg-teal-600 text-white py-2 px-4 rounded-md text-sm hover:bg-teal-700 transition-colors mt-auto" id="logistics-button">
                        进入模块
                    </button>
                </div>

                <!-- 质量管理卡片 -->
                <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4 border border-green-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" id="quality-card">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-success rounded-lg flex items-center justify-center">
                            <i class="fas fa-check-circle text-white text-xl"></i>
                        </div>
                        <i class="fas fa-arrow-right text-success"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">质量管理</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">质量检验、不良品管理、质量分析</p>
                    <button class="w-full bg-success text-white py-2 px-4 rounded-md text-sm hover:bg-green-700 transition-colors mt-auto" id="quality-button">
                        进入模块
                    </button>
                </div>

                <!-- 设备管理卡片 -->
                <div class="bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg p-4 border border-orange-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" id="equipment-card">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-warning rounded-lg flex items-center justify-center">
                            <i class="fas fa-tools text-white text-xl"></i>
                        </div>
                        <i class="fas fa-arrow-right text-warning"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">设备管理</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">设备资产、维护维修、预防性维护</p>
                    <button class="w-full bg-warning text-white py-2 px-4 rounded-md text-sm hover:bg-yellow-600 transition-colors mt-auto" id="equipment-button">
                        进入模块
                    </button>
                </div>

                <!-- 能源管理卡片 -->
                <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-4 border border-yellow-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" onclick="parent.switchModule('energy')">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-bolt text-white text-xl"></i>
                        </div>
                        <i class="fas fa-arrow-right text-yellow-500"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">能源管理</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">能耗监控、节能分析、电力管理</p>
                    <button class="w-full bg-yellow-500 text-white py-2 px-4 rounded-md text-sm hover:bg-yellow-600 transition-colors mt-auto" onclick="parent.switchModule('energy'); return false;">
                        进入模块
                    </button>
                </div>



                <!-- SAP系统卡片 -->
                <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" id="sap-card">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-building text-white text-xl"></i>
                        </div>
                        <i class="fas fa-external-link-alt text-blue-600"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">SAP</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">单点登录至SAP系统</p>
                    <button class="w-full bg-blue-600 text-white py-2 px-4 rounded-md text-sm hover:bg-blue-700 transition-colors mt-auto" id="sap-button">
                        进入系统
                    </button>
                </div>

                <!-- OA系统卡片 -->
                <div class="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg p-4 border border-emerald-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" id="oa-card">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-emerald-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-file-alt text-white text-xl"></i>
                        </div>
                        <i class="fas fa-external-link-alt text-emerald-600"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">OA</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">单点登录至OA系统</p>
                    <button class="w-full bg-emerald-600 text-white py-2 px-4 rounded-md text-sm hover:bg-emerald-700 transition-colors mt-auto" id="oa-button">
                        进入系统
                    </button>
                </div>
            </div>
        </div>

        <!-- 智慧园区模块 -->
        <div id="smart-park-module" class="bg-white rounded-lg shadow-sm p-6 mt-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-building text-primary mr-2"></i>
                智慧园区
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <!-- IOC中心卡片 -->
                <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" onclick="parent.switchModule('ioc')">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-desktop text-white text-xl"></i>
                        </div>
                        <i class="fas fa-arrow-right text-blue-600"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">IOC中心</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">智能运营中心、统一监控、数据大屏</p>
                    <button class="w-full bg-blue-600 text-white py-2 px-4 rounded-md text-sm hover:bg-blue-700 transition-colors mt-auto" onclick="parent.switchModule('ioc'); return false;">
                        进入模块
                    </button>
                </div>

                <!-- 智慧安防卡片 -->
                <div class="bg-gradient-to-br from-red-50 to-red-100 rounded-lg p-4 border border-red-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" onclick="parent.switchModule('security')">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-red-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-shield-alt text-white text-xl"></i>
                        </div>
                        <i class="fas fa-arrow-right text-red-600"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">智慧安防</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">视频监控、入侵检测、消防报警</p>
                    <button class="w-full bg-red-600 text-white py-2 px-4 rounded-md text-sm hover:bg-red-700 transition-colors mt-auto" onclick="parent.switchModule('security'); return false;">
                        进入模块
                    </button>
                </div>

                <!-- 便捷通行卡片 -->
                <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4 border border-green-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" onclick="parent.switchModule('access')">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-key text-white text-xl"></i>
                        </div>
                        <i class="fas fa-arrow-right text-green-600"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">便捷通行</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">门禁管理、访客预约、通行记录</p>
                    <button class="w-full bg-green-600 text-white py-2 px-4 rounded-md text-sm hover:bg-green-700 transition-colors mt-auto" onclick="parent.switchModule('access'); return false;">
                        进入模块
                    </button>
                </div>

                <!-- 高效能源卡片 -->
                <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-4 border border-yellow-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" onclick="parent.switchModule('energy-park')">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-yellow-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-bolt text-white text-xl"></i>
                        </div>
                        <i class="fas fa-arrow-right text-yellow-600"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">高效能源</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">能耗监控、智能调度、节能优化</p>
                    <button class="w-full bg-yellow-600 text-white py-2 px-4 rounded-md text-sm hover:bg-yellow-700 transition-colors mt-auto" onclick="parent.switchModule('energy-park'); return false;">
                        进入模块
                    </button>
                </div>

                <!-- 空间资产卡片 -->
                <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-4 border border-purple-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" onclick="parent.switchModule('space')">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-map text-white text-xl"></i>
                        </div>
                        <i class="fas fa-arrow-right text-purple-600"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">空间资产</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">空间管理、资产配置、使用统计</p>
                    <button class="w-full bg-purple-600 text-white py-2 px-4 rounded-md text-sm hover:bg-purple-700 transition-colors mt-auto" onclick="parent.switchModule('space'); return false;">
                        进入模块
                    </button>
                </div>

                <!-- 绿色环保卡片 -->
                <div class="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg p-4 border border-emerald-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" onclick="parent.switchModule('environment')">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-emerald-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-leaf text-white text-xl"></i>
                        </div>
                        <i class="fas fa-arrow-right text-emerald-600"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">绿色环保</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">环境监测、污染控制、碳排放管理</p>
                    <button class="w-full bg-emerald-600 text-white py-2 px-4 rounded-md text-sm hover:bg-emerald-700 transition-colors mt-auto" onclick="parent.switchModule('environment'); return false;">
                        进入模块
                    </button>
                </div>

                <!-- 综合服务卡片 -->
                <div class="bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-lg p-4 border border-indigo-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" onclick="parent.switchModule('service')">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-indigo-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-concierge-bell text-white text-xl"></i>
                        </div>
                        <i class="fas fa-arrow-right text-indigo-600"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">综合服务</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">生活服务、会议预约、设施维护</p>
                    <button class="w-full bg-indigo-600 text-white py-2 px-4 rounded-md text-sm hover:bg-indigo-700 transition-colors mt-auto" onclick="parent.switchModule('service'); return false;">
                        进入模块
                    </button>
                </div>

                <!-- 物流调度卡片 -->
                <div class="bg-gradient-to-br from-teal-50 to-teal-100 rounded-lg p-4 border border-teal-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" onclick="parent.switchModule('logistics-park')">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-truck text-white text-xl"></i>
                        </div>
                        <i class="fas fa-arrow-right text-teal-600"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">物流调度</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">车辆预约、月台管理、运输调度</p>
                    <button class="w-full bg-teal-600 text-white py-2 px-4 rounded-md text-sm hover:bg-teal-700 transition-colors mt-auto" onclick="parent.switchModule('logistics-park'); return false;">
                        进入模块
                    </button>
                </div>
            </div>
        </div>

        <!-- 运营中心模块 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mt-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-chart-line text-primary mr-2"></i>
                运营中心
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                <!-- 数据看板卡片 -->
                <div class="bg-gradient-to-br from-red-50 to-red-100 rounded-lg p-4 border border-red-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" onclick="parent.switchModule('operations')">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-chart-bar text-white text-xl"></i>
                        </div>
                        <i class="fas fa-arrow-right text-red-500"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">数据看板</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">数据大屏、运营分析、决策支持</p>
                    <button class="w-full bg-red-500 text-white py-2 px-4 rounded-md text-sm hover:bg-red-600 transition-colors mt-auto" onclick="parent.switchModule('operations'); return false;">
                        进入模块
                    </button>
                </div>

                <!-- 数字孪生卡片 -->
                <div class="bg-gradient-to-br from-violet-50 to-violet-100 rounded-lg p-4 border border-violet-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" onclick="parent.switchModule('digitaltwin')">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-violet-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-cube text-white text-xl"></i>
                        </div>
                        <i class="fas fa-arrow-right text-violet-600"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">数字孪生</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">3D建模、虚拟仿真、智能预测</p>
                    <button class="w-full bg-violet-600 text-white py-2 px-4 rounded-md text-sm hover:bg-violet-700 transition-colors mt-auto" onclick="parent.switchModule('digitaltwin'); return false;">
                        进入模块
                    </button>
                </div>
            </div>
        </div>

        <!-- 基础平台模块 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mt-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-layer-group text-primary mr-2"></i>
                基础平台
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                <!-- IOT平台卡片 -->
                <div class="bg-gradient-to-br from-cyan-50 to-cyan-100 rounded-lg p-4 border border-cyan-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" onclick="parent.switchModule('iot')">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-cyan-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-wifi text-white text-xl"></i>
                        </div>
                        <i class="fas fa-arrow-right text-cyan-500"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">IOT平台</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">设备连接、数据采集、远程监控</p>
                    <button class="w-full bg-cyan-500 text-white py-2 px-4 rounded-md text-sm hover:bg-cyan-600 transition-colors mt-auto" onclick="parent.switchModule('iot'); return false;">
                        进入模块
                    </button>
                </div>

                <!-- 低代码平台卡片 -->
                <div class="bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-lg p-4 border border-indigo-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" onclick="parent.switchModule('lowcode')">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-indigo-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-code text-white text-xl"></i>
                        </div>
                        <i class="fas fa-arrow-right text-indigo-500"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">低代码平台</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">可视化开发、流程设计、应用构建</p>
                    <button class="w-full bg-indigo-500 text-white py-2 px-4 rounded-md text-sm hover:bg-indigo-600 transition-colors mt-auto" onclick="parent.switchModule('lowcode'); return false;">
                        进入模块
                    </button>
                </div>

                <!-- 主数据平台卡片 -->
                <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-4 border border-gray-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" onclick="parent.switchModule('masterdata')">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-gray-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-database text-white text-xl"></i>
                        </div>
                        <i class="fas fa-arrow-right text-gray-600"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">主数据平台</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">数据管理、数据标准、数据治理</p>
                    <button class="w-full bg-gray-600 text-white py-2 px-4 rounded-md text-sm hover:bg-gray-700 transition-colors mt-auto" onclick="parent.switchModule('masterdata'); return false;">
                        进入模块
                    </button>
                </div>

                <!-- 数据集成平台卡片 -->
                <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" onclick="window.open('http://123.235.0.227:8081/login.html', '_blank')">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-exchange-alt text-white text-xl"></i>
                        </div>
                        <i class="fas fa-external-link-alt text-blue-600"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">数据集成平台</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">数据同步、接口管理、数据治理</p>
                    <button class="w-full bg-blue-600 text-white py-2 px-4 rounded-md text-sm hover:bg-blue-700 transition-colors mt-auto" onclick="window.open('http://123.235.0.227:8081/login.html', '_blank'); return false;">
                        进入平台
                    </button>
                </div>

                <!-- 慧图云平台卡片 -->
                <div class="bg-gradient-to-br from-cyan-50 to-cyan-100 rounded-lg p-4 border border-cyan-200 hover:shadow-md transition-shadow cursor-pointer flex flex-col h-full" onclick="window.open('https://imap.iimake.com/index/#/home', '_blank')">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-12 h-12 bg-cyan-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-cloud-upload-alt text-white text-xl"></i>
                        </div>
                        <i class="fas fa-external-link-alt text-cyan-600"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">慧图云平台</h3>
                    <p class="text-sm text-gray-600 mb-3 flex-grow" style="min-height: 2.5rem;">智能制图、云端协作、图形设计</p>
                    <button class="w-full bg-cyan-600 text-white py-2 px-4 rounded-md text-sm hover:bg-cyan-700 transition-colors mt-auto" onclick="window.open('https://imap.iimake.com/index/#/home', '_blank'); return false;">
                        进入平台
                    </button>
                </div>
            </div>
        </div>

        <!-- 下半部分：待办事项和消息通知 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 待办事项模块 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center justify-between">
                    <span class="flex items-center">
                        <i class="fas fa-tasks text-primary mr-2"></i>
                        待办事项
                    </span>
                    <span class="text-sm font-normal text-gray-500">共 5 项</span>
                </h2>
                <div class="space-y-3">
                    <!-- 待办事项列表 -->
                    <div class="flex items-center p-3 bg-red-50 border border-red-200 rounded-lg">
                        <div class="flex-shrink-0 w-3 h-3 bg-danger rounded-full mr-3"></div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <h4 class="font-medium text-gray-800">设备故障报修处理</h4>
                                <span class="text-xs text-danger font-medium">紧急</span>
                            </div>
                            <p class="text-sm text-gray-600">变频器生产线3号设备异常，需立即处理</p>
                            <p class="text-xs text-gray-500 mt-1">截止时间：今天 18:00</p>
                        </div>
                        <button class="ml-3 text-gray-400 hover:text-gray-600">
                            <i class="fas fa-check"></i>
                        </button>
                    </div>

                    <div class="flex items-center p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div class="flex-shrink-0 w-3 h-3 bg-warning rounded-full mr-3"></div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <h4 class="font-medium text-gray-800">生产计划审核</h4>
                                <span class="text-xs text-warning font-medium">重要</span>
                            </div>
                            <p class="text-sm text-gray-600">下周生产计划需要您的审核确认</p>
                            <p class="text-xs text-gray-500 mt-1">截止时间：明天 12:00</p>
                        </div>
                        <button class="ml-3 text-gray-400 hover:text-gray-600">
                            <i class="fas fa-check"></i>
                        </button>
                    </div>

                    <div class="flex items-center p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div class="flex-shrink-0 w-3 h-3 bg-primary rounded-full mr-3"></div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <h4 class="font-medium text-gray-800">质量检验报告确认</h4>
                                <span class="text-xs text-primary font-medium">普通</span>
                            </div>
                            <p class="text-sm text-gray-600">本月质量检验报告等待确认</p>
                            <p class="text-xs text-gray-500 mt-1">截止时间：本周五</p>
                        </div>
                        <button class="ml-3 text-gray-400 hover:text-gray-600">
                            <i class="fas fa-check"></i>
                        </button>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <button class="text-primary hover:text-blue-700 text-sm font-medium">
                        查看全部待办事项 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
            </div>
            <!-- 消息通知模块 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center justify-between">
                    <span class="flex items-center">
                        <i class="fas fa-bell text-primary mr-2"></i>
                        消息通知
                    </span>
                    <span class="text-sm font-normal text-gray-500">3 条未读</span>
                </h2>
                <div class="space-y-3">
                    <!-- 消息列表 -->
                    <div class="flex items-start p-3 bg-blue-50 border-l-4 border-primary rounded-r-lg">
                        <div class="flex-shrink-0 w-8 h-8 bg-primary rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-exclamation text-white text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <h4 class="font-medium text-gray-800">设备报警</h4>
                                <span class="text-xs text-gray-500">5分钟前</span>
                            </div>
                            <p class="text-sm text-gray-600">变频器生产线温度异常，请及时检查</p>
                        </div>
                        <div class="w-2 h-2 bg-primary rounded-full ml-2"></div>
                    </div>

                    <div class="flex items-start p-3 bg-green-50 border-l-4 border-success rounded-r-lg">
                        <div class="flex-shrink-0 w-8 h-8 bg-success rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-check text-white text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <h4 class="font-medium text-gray-800">工单完成</h4>
                                <span class="text-xs text-gray-500">1小时前</span>
                            </div>
                            <p class="text-sm text-gray-600">工单 WO-2024-001 已完成，等待质检</p>
                        </div>
                        <div class="w-2 h-2 bg-success rounded-full ml-2"></div>
                    </div>

                    <div class="flex items-start p-3 bg-gray-50 border-l-4 border-gray-300 rounded-r-lg">
                        <div class="flex-shrink-0 w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-info text-white text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <h4 class="font-medium text-gray-800">系统维护通知</h4>
                                <span class="text-xs text-gray-500">2小时前</span>
                            </div>
                            <p class="text-sm text-gray-600">系统将于今晚22:00-24:00进行维护</p>
                        </div>
                    </div>

                    <div class="flex items-start p-3 bg-gray-50 border-l-4 border-gray-300 rounded-r-lg">
                        <div class="flex-shrink-0 w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-calendar text-white text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <h4 class="font-medium text-gray-800">会议提醒</h4>
                                <span class="text-xs text-gray-500">昨天</span>
                            </div>
                            <p class="text-sm text-gray-600">明天上午10:00生产例会，请准时参加</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <button class="text-primary hover:text-blue-700 text-sm font-medium">
                        查看全部消息 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示功能开发中提示
        function showComingSoon(moduleName) {
            alert(`${moduleName}功能正在开发中，敬请期待！\n\n${moduleName} module is under development, coming soon!`);
        }

        // 测试父窗口通信
        function testParentCommunication() {
            console.log('Testing parent communication...');
            try {
                if (parent && parent.switchModule) {
                    console.log('Parent switchModule function found');
                    return true;
                } else {
                    console.error('Parent switchModule function not found');
                    return false;
                }
            } catch (e) {
                console.error('Error accessing parent:', e);
                return false;
            }
        }

        // 添加模块卡片点击事件
        function addModuleClickHandlers() {
            const modules = [
                { id: 'production', name: '生产管理' },
                { id: 'quality', name: '质量管理' },
                { id: 'equipment', name: '设备管理' },
                { id: 'inventory', name: '仓储管理' },
                { id: 'logistics', name: '厂内物流' }
            ];

            modules.forEach(module => {
                // 卡片点击事件
                const card = document.getElementById(module.id + '-card');
                if (card) {
                    card.addEventListener('click', function() {
                        console.log(`${module.name} card clicked`);
                        try {
                            if (parent && parent.navigateToModule) {
                                parent.navigateToModule(module.id);
                            } else if (parent && parent.switchModule) {
                                parent.switchModule(module.id);
                            } else {
                                console.error('Parent navigation functions not found');
                                alert(`无法切换到${module.name}模块，父窗口导航函数未找到。`);
                            }
                        } catch (e) {
                            console.error('Error calling parent navigation function:', e);
                            alert(`无法切换到${module.name}模块，请检查浏览器控制台获取详细错误信息。`);
                        }
                    });
                }

                // 按钮点击事件
                const button = document.getElementById(module.id + '-button');
                if (button) {
                    button.addEventListener('click', function(e) {
                        e.stopPropagation(); // 防止事件冒泡
                        console.log(`${module.name} button clicked`);
                        try {
                            if (parent && parent.navigateToModule) {
                                parent.navigateToModule(module.id);
                            } else if (parent && parent.switchModule) {
                                parent.switchModule(module.id);
                            } else {
                                console.error('Parent navigation functions not found');
                                alert(`无法切换到${module.name}模块，父窗口导航函数未找到。`);
                            }
                        } catch (e) {
                            console.error('Error calling parent navigation function:', e);
                            alert(`无法切换到${module.name}模块，请检查浏览器控制台获取详细错误信息。`);
                        }
                    });
                }
            });

            // 添加SAP和OA系统的特殊处理
            const sapCard = document.getElementById('sap-card');
            const sapButton = document.getElementById('sap-button');
            const oaCard = document.getElementById('oa-card');
            const oaButton = document.getElementById('oa-button');

            // SAP系统点击事件
            if (sapCard) {
                sapCard.addEventListener('click', function() {
                    alert('正在跳转到正泰SAP系统...\n\n注意：这是一个演示功能，实际部署时需要配置正确的SAP系统URL和单点登录参数。');
                    // 实际部署时的代码：
                    // window.open('https://sap.chint.com/sap/bc/gui/sap/its/webgui', '_blank');
                });
            }

            if (sapButton) {
                sapButton.addEventListener('click', function(e) {
                    e.stopPropagation();
                    alert('正在跳转到正泰SAP系统...\n\n注意：这是一个演示功能，实际部署时需要配置正确的SAP系统URL和单点登录参数。');
                    // 实际部署时的代码：
                    // window.open('https://sap.chint.com/sap/bc/gui/sap/its/webgui', '_blank');
                });
            }

            // OA系统点击事件
            if (oaCard) {
                oaCard.addEventListener('click', function() {
                    alert('正在跳转到正泰OA系统...\n\n注意：这是一个演示功能，实际部署时需要配置正确的OA系统URL和单点登录参数。');
                    // 实际部署时的代码：
                    // window.open('https://oa.chint.com', '_blank');
                });
            }

            if (oaButton) {
                oaButton.addEventListener('click', function(e) {
                    e.stopPropagation();
                    alert('正在跳转到正泰OA系统...\n\n注意：这是一个演示功能，实际部署时需要配置正确的OA系统URL和单点登录参数。');
                    // 实际部署时的代码：
                    // window.open('https://oa.chint.com', '_blank');
                });
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Dashboard loaded');
            const canCommunicate = testParentCommunication();
            if (canCommunicate) {
                console.log('Parent communication successful, adding click handlers');
                addModuleClickHandlers();
            } else {
                console.error('Parent communication failed, click handlers not added');
                alert('无法与父窗口通信，模块导航功能可能无法正常工作。请尝试使用现代浏览器直接打开index.html。');
            }

            // 控制智慧园区模块的显示/隐藏
            checkSmartParkVisibility();
        });

        // 检查并控制智慧园区模块的显示
        function checkSmartParkVisibility() {
            console.log('checkSmartParkVisibility called');
            try {
                // 从localStorage或sessionStorage获取登录数据
                const loginData = localStorage.getItem('loginData') || sessionStorage.getItem('loginData');
                console.log('Login data from storage:', loginData);

                if (loginData) {
                    const data = JSON.parse(loginData);
                    console.log('Parsed login data:', data);
                    const versionId = data.versionInfo ? data.versionInfo.id : 'general';
                    console.log('Version ID:', versionId);

                    const smartParkModule = document.getElementById('smart-park-module');
                    console.log('Smart park module element:', smartParkModule);

                    if (smartParkModule) {
                        if (versionId === 'general') {
                            smartParkModule.style.display = 'block';
                            console.log('Showing smart park module for general version');
                        } else {
                            smartParkModule.style.display = 'none';
                            console.log('Hiding smart park module for non-general version');
                        }
                    } else {
                        console.error('Smart park module element not found');
                    }
                } else {
                    console.warn('No login data found in storage');
                }
            } catch (error) {
                console.error('Error checking smart park visibility:', error);
                // 默认显示智慧园区模块
                const smartParkModule = document.getElementById('smart-park-module');
                if (smartParkModule) {
                    smartParkModule.style.display = 'block';
                    console.log('Showing smart park module by default due to error');
                }
            }
        }
    </script>

    <!-- AI助手脚本 -->
    <script src="../assets/js/ai-assistant.js"></script>
</body>
</html>
