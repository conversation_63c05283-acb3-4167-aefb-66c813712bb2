<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应急指挥调度 - 慧新全智厂园一体平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-exclamation-triangle text-primary mr-3"></i>
                应急指挥调度中心
            </h1>
            <p class="text-gray-600 mt-2">快速响应，科学调度，保障园区安全</p>
        </div>

        <!-- 应急状态概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">应急等级</h3>
                        <p class="text-3xl font-bold text-green-600 mt-2">正常</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-shield-check text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>风险等级:</span>
                        <span class="text-green-600 font-medium">低风险</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>安全天数:</span>
                        <span class="text-green-600 font-medium">365天</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">应急队伍</h3>
                        <p class="text-3xl font-bold text-blue-600 mt-2">156</p>
                        <p class="text-sm text-gray-500">人</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-users text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>在岗人员:</span>
                        <span class="text-blue-600 font-medium">148人</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>待命率:</span>
                        <span class="text-green-600 font-medium">94.9%</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">应急物资</h3>
                        <p class="text-3xl font-bold text-purple-600 mt-2">98.5%</p>
                        <p class="text-sm text-gray-500">充足率</p>
                    </div>
                    <div class="bg-purple-100 p-3 rounded-full">
                        <i class="fas fa-boxes text-purple-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>物资种类:</span>
                        <span class="text-purple-600 font-medium">28类</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>库存状态:</span>
                        <span class="text-green-600 font-medium">充足</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">响应时间</h3>
                        <p class="text-3xl font-bold text-yellow-600 mt-2">3.2</p>
                        <p class="text-sm text-gray-500">分钟</p>
                    </div>
                    <div class="bg-yellow-100 p-3 rounded-full">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>目标时间:</span>
                        <span class="text-blue-600 font-medium">≤5分钟</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>达标率:</span>
                        <span class="text-green-600 font-medium">100%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 应急事件处理 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-bell text-red-600 mr-2"></i>
                应急事件处理
            </h3>
            <div class="space-y-4">
                <div class="border border-green-200 bg-green-50 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">已处理</span>
                            <h4 class="font-semibold text-gray-800">设备故障应急处理</h4>
                        </div>
                        <span class="text-sm text-gray-500">处理时间: 2小时前</span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-3">
                        <div>
                            <span class="text-sm text-gray-600">事件等级:</span>
                            <p class="font-medium text-yellow-600">III级(一般)</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">影响范围:</span>
                            <p class="font-medium">生产线3</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">响应时间:</span>
                            <p class="font-medium text-green-600">2.5分钟</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">处理结果:</span>
                            <p class="font-medium text-green-600">已恢复</p>
                        </div>
                    </div>
                    <div class="text-sm text-gray-700 mb-3">
                        <p>生产线3号设备出现异常停机，应急小组立即响应，通过备用设备切换和紧急维修，在2小时内恢复正常生产。</p>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            <span>指挥员: 张工程师 | 参与人员: 8人 | 物资消耗: 备件3套</span>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700">
                                查看详情
                            </button>
                        </div>
                    </div>
                </div>

                <div class="border border-blue-200 bg-blue-50 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">演练</span>
                            <h4 class="font-semibold text-gray-800">消防应急演练</h4>
                        </div>
                        <span class="text-sm text-gray-500">计划时间: 明日 14:00</span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-3">
                        <div>
                            <span class="text-sm text-gray-600">演练类型:</span>
                            <p class="font-medium text-blue-600">消防疏散</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">参与人员:</span>
                            <p class="font-medium">全体员工</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">演练时长:</span>
                            <p class="font-medium">30分钟</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">演练状态:</span>
                            <p class="font-medium text-blue-600">准备中</p>
                        </div>
                    </div>
                    <div class="text-sm text-gray-700 mb-3">
                        <p>定期消防应急演练，提高员工应急响应能力和自救互救技能，确保在紧急情况下能够快速有序疏散。</p>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            <span>负责人: 李安全员 | 集合点: 东广场 | 预计参与: 280人</span>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                                演练准备
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 应急资源管理 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-users-cog text-blue-600 mr-2"></i>
                    应急队伍管理
                </h3>
                <div class="space-y-4">
                    <div class="bg-gradient-to-r from-red-50 to-pink-50 rounded-lg p-4 border border-red-200">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-semibold text-gray-800">消防应急队</h4>
                            <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">24人</span>
                        </div>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• 队长: 王消防员</div>
                            <div>• 在岗人员: 22人</div>
                            <div>• 装备状态: 完好</div>
                            <div>• 响应能力: 优秀</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-semibold text-gray-800">医疗救护队</h4>
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">18人</span>
                        </div>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• 队长: 李医生</div>
                            <div>• 在岗人员: 16人</div>
                            <div>• 装备状态: 完好</div>
                            <div>• 响应能力: 优秀</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-200">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-semibold text-gray-800">抢险救援队</h4>
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">32人</span>
                        </div>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• 队长: 张工程师</div>
                            <div>• 在岗人员: 30人</div>
                            <div>• 装备状态: 完好</div>
                            <div>• 响应能力: 良好</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-boxes text-purple-600 mr-2"></i>
                    应急物资管理
                </h3>
                <div class="space-y-4">
                    <div class="bg-gradient-to-r from-red-50 to-pink-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">消防器材</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div class="flex justify-between">
                                <span>灭火器:</span>
                                <span class="font-medium text-red-600">128台</span>
                            </div>
                            <div class="flex justify-between">
                                <span>消防栓:</span>
                                <span class="font-medium text-red-600">45个</span>
                            </div>
                            <div class="flex justify-between">
                                <span>防护服:</span>
                                <span class="font-medium text-red-600">30套</span>
                            </div>
                            <div class="flex justify-between">
                                <span>状态:</span>
                                <span class="font-medium text-green-600">完好</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">医疗用品</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div class="flex justify-between">
                                <span>急救包:</span>
                                <span class="font-medium text-blue-600">25套</span>
                            </div>
                            <div class="flex justify-between">
                                <span>担架:</span>
                                <span class="font-medium text-blue-600">8副</span>
                            </div>
                            <div class="flex justify-between">
                                <span>药品:</span>
                                <span class="font-medium text-blue-600">充足</span>
                            </div>
                            <div class="flex justify-between">
                                <span>状态:</span>
                                <span class="font-medium text-green-600">完好</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">抢险工具</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div class="flex justify-between">
                                <span>切割机:</span>
                                <span class="font-medium text-green-600">6台</span>
                            </div>
                            <div class="flex justify-between">
                                <span>起重设备:</span>
                                <span class="font-medium text-green-600">4套</span>
                            </div>
                            <div class="flex justify-between">
                                <span>照明设备:</span>
                                <span class="font-medium text-green-600">12套</span>
                            </div>
                            <div class="flex justify-between">
                                <span>状态:</span>
                                <span class="font-medium text-green-600">完好</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 应急预案管理 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-file-alt text-green-600 mr-2"></i>
                应急预案管理
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-gradient-to-r from-red-50 to-pink-50 rounded-lg p-4 border border-red-200">
                    <h4 class="font-semibold text-gray-800 mb-3">火灾应急预案</h4>
                    <div class="text-sm text-gray-600 space-y-2">
                        <div class="flex justify-between">
                            <span>版本:</span>
                            <span class="font-medium">V2.1</span>
                        </div>
                        <div class="flex justify-between">
                            <span>更新时间:</span>
                            <span class="font-medium">2024-12-01</span>
                        </div>
                        <div class="flex justify-between">
                            <span>演练次数:</span>
                            <span class="font-medium text-red-600">12次/年</span>
                        </div>
                        <div class="flex justify-between">
                            <span>状态:</span>
                            <span class="font-medium text-green-600">有效</span>
                        </div>
                    </div>
                    <button class="w-full mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                        查看预案
                    </button>
                </div>

                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
                    <h4 class="font-semibold text-gray-800 mb-3">设备故障预案</h4>
                    <div class="text-sm text-gray-600 space-y-2">
                        <div class="flex justify-between">
                            <span>版本:</span>
                            <span class="font-medium">V1.8</span>
                        </div>
                        <div class="flex justify-between">
                            <span>更新时间:</span>
                            <span class="font-medium">2024-11-15</span>
                        </div>
                        <div class="flex justify-between">
                            <span>演练次数:</span>
                            <span class="font-medium text-blue-600">6次/年</span>
                        </div>
                        <div class="flex justify-between">
                            <span>状态:</span>
                            <span class="font-medium text-green-600">有效</span>
                        </div>
                    </div>
                    <button class="w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                        查看预案
                    </button>
                </div>

                <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-200">
                    <h4 class="font-semibold text-gray-800 mb-3">人员伤亡预案</h4>
                    <div class="text-sm text-gray-600 space-y-2">
                        <div class="flex justify-between">
                            <span>版本:</span>
                            <span class="font-medium">V1.5</span>
                        </div>
                        <div class="flex justify-between">
                            <span>更新时间:</span>
                            <span class="font-medium">2024-10-20</span>
                        </div>
                        <div class="flex justify-between">
                            <span>演练次数:</span>
                            <span class="font-medium text-green-600">4次/年</span>
                        </div>
                        <div class="flex justify-between">
                            <span>状态:</span>
                            <span class="font-medium text-green-600">有效</span>
                        </div>
                    </div>
                    <button class="w-full mt-4 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                        查看预案
                    </button>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-bolt text-blue-600 mr-2"></i>
                快速操作
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button class="flex flex-col items-center p-4 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors">
                    <i class="fas fa-exclamation-triangle text-red-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">启动应急</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
                    <i class="fas fa-users text-blue-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">队伍调度</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                    <i class="fas fa-boxes text-green-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">物资调配</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors">
                    <i class="fas fa-file-alt text-purple-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">预案管理</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 应急指挥调度功能
        function initEmergencyCommand() {
            console.log('初始化应急指挥调度功能');
            
            // 应急事件处理按钮事件
            const eventButtons = document.querySelectorAll('button');
            eventButtons.forEach(button => {
                const text = button.textContent.trim();
                if (text.includes('查看详情')) {
                    button.addEventListener('click', function() {
                        console.log('查看应急事件详情');
                        alert('正在查看应急事件详细信息...');
                    });
                } else if (text.includes('演练准备')) {
                    button.addEventListener('click', function() {
                        console.log('准备应急演练');
                        alert('正在准备消防应急演练...');
                    });
                } else if (text.includes('查看预案')) {
                    button.addEventListener('click', function() {
                        const preplanType = this.closest('.rounded-lg').querySelector('h4').textContent;
                        console.log('查看应急预案:', preplanType);
                        alert(`正在查看 ${preplanType} 详细内容...`);
                    });
                } else if (text.includes('启动应急')) {
                    button.addEventListener('click', function() {
                        console.log('启动应急响应');
                        if (confirm('确认启动应急响应程序？')) {
                            alert('应急响应程序已启动！');
                        }
                    });
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initEmergencyCommand();
            console.log('应急指挥调度页面加载完成');
        });
    </script>
</body>
</html>
