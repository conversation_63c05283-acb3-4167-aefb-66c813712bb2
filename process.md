好的，这是一个包含248页的详细文档。我将为您提供完整的Markdown格式文本。

***

# 正泰电源光储智能制造基地
## 智能工厂规划咨询项目
## 未来业务流程设计
**慧新全智互联工业科技（上海）有限公司**

***

# 目录
## 1 总体蓝图设计
1.1 业务流程架构设计
1.2 数字化系统架构设计

## 2 业务流程设计
### 2.1 计划模块
2.1.1 销售需求管理流程
2.1.2 售后需求管理流程
2.1.3 项目需求管理流程
2.1.4 研发需求管理流程
2.1.5 返工需求管理流程
2.1.6 PCBA 需求管理流程
2.1.7 需求变更管理流程
2.1.8 返修需求管理流程
2.1.9 紧急插单需求管理流程
2.1.10 产能预估流程
2.1.11 MPS 主计划制定流程
2.1.12 MPS 主计划变更流程
2.1.13 物料需求计划生成流程
2.1.14 备料计划转采购流程
2.1.15 计划工单流程
2.1.16 工单变更流程
2.1.17 工单关闭流程
2.1.18 日排程制定流程

### 2.2 仓储管理
2.2.1 采购收货管理流程
2.2.2 退料入库流程
2.2.3 工单发料管理流程
2.2.4 成本中心领用流程
2.2.5 销售调拨出库流程
2.2.6 委外调拨出库流程
2.2.7 采购退货出库流程
2.2.8 委外反冲发料流程
2.2.9 生产入库管理流程
2.2.10 销售退货入库流程
2.2.11 成品仓调拨入库流程
2.2.12 销售出库流程
2.2.13 返工成品出库流程
2.2.14 成品仓调拨出库流程
2.2.15 OEM 直发出库流程
2.2.16 泰国原料出库
2.2.17 盘点管理流程
2.2.18 报损管理流程
2.2.19 库龄监控

### 2.3 生产管理
2.3.1 产线工艺规划
2.3.2 车间物料配送流程
2.3.3 线边库管理流程
2.3.4 生产异常处理流程
2.3.5 接线和线序检测流程
2.3.6 功率板螺钉漏装检测流程
2.3.7 清换线检查管理流程
2.3.8 物料装配防错流程
2.3.9 产品追溯管理流程
2.3.10 作业指导书管理流程
2.3.11 表单标签打印管理流程
2.3.12 测试程序管理流程
2.3.13 智能工装管理流程
2.3.14 智能耗材柜使用流程
2.3.15 智能工具柜使用流程
2.3.16 自动发料柜使用流程
2.3.17 生产过程监控流程
2.3.18 返工、返修流程
2.3.19 下线报工作业流程
2.3.20 人员资质管理流程
2.3.21 考勤管理流程

### 2.4 设备&能源模块
2.4.1 技术标准规范管理流程
2.4.2 主数据管理流程
2.4.3 设备到货确认流程
2.4.4 设备安装验收流程
2.4.5 设备运行监控流程
2.4.6 能源管理流程
2.4.7 设备台账管理
2.4.8 设备点巡检流程
2.4.9 设备保养流程
2.4.10 设备故障维修流程
2.4.11 技改管理流程
2.4.12 预算执行管理流程
2.4.13 备品备件管理流程
2.4.14 闲置设备评估流程
2.4.15 闲置设备处置流程

### 2.5 质量管理
2.5.1 质量管理指标
2.5.2 风险评估与管理控制流程
2.5.3 改进控制管理流程
2.5.4 合同评审管理流程
2.5.5 设计开发管理流程
2.5.6 研发变更管理流程
2.5.7 来料质检管理流程
2.5.8 来料不合格管理流程
2.5.9 来料不合格挑选管理流程
2.5.10 试产管理流程
2.5.11 过程质量管理流程
2.5.12 首件检验管理流程
2.5.13 在制不合格品管理流程
2.5.14 末件检验管理流程
2.5.15 成品检验管理流程
2.5.16 下线检验管理流程
2.5.17 包装检验管理流程
2.5.18 成品检验不合格管理流程
2.5.19 召回管理流程
2.5.20 出货检验管理流程
2.5.21 库存成品检验管理流程
2.5.22 NCR 管理流程
2.5.23 仪器校验管理流程
2.5.24 ECN 变更管理流程
2.5.25 PCN 变更管理流程
2.5.26 OEM 管理流程
2.5.27 ODM 管理流程
2.5.28 审核管理流程
2.5.29 分层审核管理流程
2.5.30 FAT 管理流程
2.5.31 PPAP 管理流程
2.5.32 退市联动管理流程
2.5.33 质量管理业务流集成

## 3 用户确认签字

***

# 1 总体蓝图设计
## 1.1 业务流程架构设计
业务流程的架构设计来自于核心业务主价值链：

[图 1.1-1 业务价值链]

通过以上主价值链分析结合本次新工厂的建设内容，本次咨询项目确定的一级流程架构如下：

[图 1.1-2 流程架构]
*   **运营流程**
    *   1. 从需求到计划
    *   2. 从收货到发货
    *   3. 从原料到成品
*   **支持流程**
    *   4. 质量管理
    *   5. 设备与能源管理

## 1.2 数字化系统架构设计
数字化系统架构设计的考虑点包括如下：

**核心流程驱动**：横向通过梳理核心业务流程，明确流程关键要素，如数据、业务规则、工作任务、部门岗位职责等，并进而明确系统定位，未来系统流程和集成关系。

**关键业务整合**：纵向通过整合业务，包括销售、需求、计划、采购、仓库等几个关键业务，实现跨系统、跨部门、跨业务流程的全面整合

**打造一体化管理平台**：从提升用户体验出发，通过合理的 UI 设计、单点登录等多方面技术考虑，打造融“业务流程、制度规则、系统功能、用户体验”于一体的管理平台。

结合业务流程架构设计范围，本次咨询项目建议数字化系统架构设计如下：

[图 1.2-1 数字化系统架构设计图]

着重建设如下数字化系统：
*   计划系统
*   WMS 仓储系统
*   MES 制造执行系统
*   LES 厂内物流调度系统
*   QMS 质量管理系统
*   设备与能源系统
*   物联网网关

***

# 2 业务流程设计
## 2.1 计划模块
计划模块总体业务流程框架设计：

[图 2.1-1 计划管理流程框架]

计划模块遵循“一单到底”的原则，即以需求单为起始，到最终需求单交付的端到端的全流程监控。无论是生产主计划、备料计划还是工单以及排程计划最终都可以归结到某个需求单上。可通过系统级别的设定，不同来源的需求，应通过不同的销售订单单别来区分。

决定计划能否正确执行的另外一个重要的因素就是 BOM 的准确度。在设计的过程中，我们依据不同的需求，不同的 MPS 主计划应分别在需求产生、计划产生的时候，及时记录下 BOM，以便后续追踪处理。

### 2.1.1 销售需求管理流程
销售需求是指处于正常销售状态的产品，在未来指定日期内的订单需求数量以及销售部门对未来销量的预测数量，在实际的管理过程中，销售部门需要将两类数量分别报呈给计划部门。并以不同的订单模板方式来区分。此类销售需求同时包括 ODM 产品的需求。

[图 2.1-2 销售需求管理流程图]

**表 2.1-1 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 提交销售需求** | 计划部门需要明确需求的来源：销售订单数据/预测数据，如果是销售订单数据，需要有期望的交期数据，并基于交期确认出需求的紧急度。流程上会自动提供可选择的产品料号供创建需求，针对不可选择到的产品,销售部门需要主动联系研发部门进行相关的ECR、ECN流程。一般来说，可选择的产品料号由市场部录入及维护,确定是可销售的产品料号。由于 ODM 的产品也属于正常销售产品，所以 ODM 产品需求归属与销售需求流程中，并在需求提交时候，明确出来需求产品类别。流程上会在上月 25 号-本月 5 号开放需求提交。**说明**: 1) 此类销售需求包含ODM需求。 2) 提交销售需求的时候，确保品创建流程的完整性。 3) 销售部门需要保证提交的需求数据的完整性。 4) 生产计划员检查数据填写的完整性和产品当前状态。 |
| **2 接收需求** | 生产计划员，针对需求数据，检查需求数量是否过大，并结合产能做初步评估；检查产品料号完整性和产品状态情况。针对检查不通过的，驳回至需求提出方。检查通过的，标识数据检查通过。|
| **3 需求锁定** | 计划员应及时对检查通过的需求，进行锁定。针对锁定后的需求才可进行 MPS 主计划的生成。|

### 2.1.2 售后需求管理流程
售后需求由售后部门统一进行管理，售后的产品由于产品迭代等原因，客观上可能存在产品已下市，零配件采购困难或者采购成本高等原因，售后可通过换新的方式来支持售后。而针对确实需要进行维修的机器，由技术部门评估技术方案后，提供售后商品的维修 BOM 组件，来支持计划部门进行后续的安排。

[图 2.1-3 售后需求管理流程图]

**表 2.1-2 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 提交售后需求** | 1. 售后提出的需加工的部件或组件需事先申请售后专用料号，同时通知工艺进行 BOM 及工艺路线维护，财务进行成本维护，确保料号存在并可使用。<br>2. 下市产品需售后组织评审，确定产品 BOM 的完整性及准确性。<br>3. 针对换机的售后需求，产品料号控制同销售需求。**说明**: 售后的需求需要明确指明是换机、维修。 |
| **2 接收需求** | 生产计划员接收售后的需求，检查产品料号有效性。生产专员判断，针对维修的售后需求，提交工艺部门进行维修方案评估。 |
| **3 提交技改方案** | 生产工艺部根据售后需求，提供维修方案，并更新需求的维修 BOM，并记录到需求单上。|

### 2.1.3 项目需求管理流程
项目需求是指针对特定客户的，定制化的项目产品，目前大多为储能项目相关产品的客制化研发与生产。项目启动会后，由项目人员提交项目计划和项目的产品需求后，PMC 进行需求锁定。

[图 2.1-4 项目需求管理流程]

**表 2.1-3 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 提交项目需求** | **储能项目要求**: <br>1. 必须由计划履行团队下达订单后,PMC 才能启动物料需求,否则由市场部提出风险备料。<br>2. 跑需求严格按 SAP BOM 执行，手稿 BOM 或 ECN、ECR 必须及时更新 PLM 及 SAP（储能研发、工艺）。<br>3. 项目团队应按项目里程碑计划节点，拆解相应的需求的交期。**说明**: 1. 项目大多为储能产品需求。 2. 立项会的产品大多是已经过ECN评审后的产品，即产品的ECN流程已完成。 |
| **2 接收需求** | 1. 生产计划员接收到项目需求，检查产品料号有效性。<br>2. 生产专员判断，针对维修的售后需求，提交工艺部门进行维修方案评估。 |
| **3 需求锁定**| 需求评估通过后，对需求进行锁定。|

### 2.1.4 研发需求管理流程
未来中试线的管理依然由生产部门统一进行管理，包括产线的安排，生产。研发部门按需求报 PMC 进行计划制定与备料采购。研发需求的显著特点是需要提交上线试产的计划上线日期，而非期望交期。目前研发需求基本都是到 P2 试产的产品生产。

[图 2.1-5 研发需求管理流程]

**表 2.1-4 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 提交研发需求** | 研发提交的需求，基本都是要走 P2 试产的。研发可选择的产品，必须是已经完成了 ECR、ECN 流程的产品。**说明**: 研发部依托市场部的需求，开展产品研发试制工作。 |
| **2 接收需求** | 生产计划员接收研发的需求，检查产品料号有效性。 |
| **3 评估需求** | 生产计划专员根据研发提供的需求，与采购及生产进行评估。评估完成后，对需求进行锁定。 |

### 2.1.5 返工需求管理流程
返工需求是针对已经入库的产成品，进行上线处理的。返工的工艺方案需要由生产部门评估后，出具返工 BOM。

[图 2.1-6 返工需求管理流程]

**表 2.1-5 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 提交售后需求** | 1. 销售部门提交的产品料号，必须是在成品仓存在库存的产品，包括不良品库存产品。<br>2. 产品状态应是正常状态。<br>3. 销售部门应能清晰的描述返工的需求信息，并在 OA 完成申请审批通过后，再提交需求至 PMC。**说明**: 返工的需求产品要从有库存的产品种类选择。 |
| **2 接收需求** | 1. 生产计划员接收返工的需求，检查产品料号有效性。<br>2. 生产专员判断，是否需要提交工艺部门进行返工方案评估。 |
| **3 提交技改方案** | 生产工艺部根据返工需求，提供返工方案。记录需求的返工 BOM 到需求单中。 |

### 2.1.6 PCBA 需求管理流程
PCBA 作为核心的电子元器件，未来规划上都为委外生产，故 PMC 需要及时提交 PCBA 需求，发送到委外供应商，进行生产。工厂的 PCBA 的需求分为2 大部分，一部分提供给泰国工厂，一部分是提供给区域工厂进行整机生产使用。此处流程特指泰国工厂提交的需求管理流程。

[图 2.1-7 PCBA 需求管理流程]

**表 2.1-6 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 提交 PCBA 需求** | 可选择提交的 PCBA 必须是有 BOM 其状态正常的料号。**说明**: 泰国工厂直接提交PCBA的需求数量至PMC。 |
| **2 接收/锁定需求** | 1. PMC 依据泰国工厂 PCBA 需求计算出与上期新增的部分,这部分就是新的订单需求。<br>2. 区域工厂需求：PMC 在区域工厂开出生产工单，结合区域工厂库存计算出 PCBA 缺料，即为区域工厂的 PCBA 需求。<br>3. 生产计划员接收和计算需求后，需要及时进行需求锁定，以支持后续的委外订单下发及 BOM 发料。 |

### 2.1.7 需求变更管理流程
锁定的需求，是经过多部门确认的需求，是 MPS 计划和备料计划的基础，后续所有的生产要素的准备都围绕着需求展开。如果对此类需求进行变更，其影响会比较广泛，在实际的过程中，应严格控制变更的发生。实际管理中，变更过程应结合与部门的考核指标相结合，控制变更的随意性。

变更一旦确认，会更新 MPS 主计划，然后通过 MPS 主计划变更流程完成后续的物料计划、工单计划的变更流程实现闭环管理。

[图 2.1-8 需求变更管理流程]

**表 2.1-7 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 选择需求** | 业务部门选择的需求必须是已锁定，且在上个计划周期内的需求，即正在执行的需求，才谈的上是变更。变更业务部门内部的流程在内部完成审批确认后，再提交至计划处。 |
| **2 发起变更** | 业务部门提交人员需要明确变更的类型：调增、调减、终止。业务部门同时要明确此次变更的优先级。 |
| **3 评估变更** | 生产计划专员针对变更做出评估影响：按评估影响分析表分别进行检查确认，并得出影响报告。并将影响报告提交至业务部门确认，其中报告着重输出结论：能否变更。**说明**: 1. 变更的类型包括：调增、调减、终止。 2. 计划专员评估佳影响时需要提供物料、工单层面的影响事件。 3. 流程执行过程种，需要明确影响的因素范围，定义影响指标。 4. 业务部门针对变更影响做最后确认，此时亦可关闭此变更请求。 |
| **4 变更确认** | 业务部门对变更报告进行确认，此时亦可关闭此变更。确认后，触发1.2.3 MPS主计划变更流程。 |

### 2.1.8 返修需求管理流程
返修是指产品下线后，包装前，质量检测未通过的产品，重新上线进行生产的日常场景。在实际的过程中，产品返修率是生产过程的重要考核指标，不应频繁发生。一旦出现了需要返工的产品，则由生产制造部门人员，提交返修需求至 PMC，PMC 调整日排程计划，支持返修产品重新上线。

[图 2.1-9 返修需求管理流程]

**表 2.1-8 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 提交返修需求** | 提交的返修需求必须是已下单工单的产品。同质量流程打通，可以由质量流程自动触发需求的提交。**说明**: 提交返修需求。 |
| **2 接收需求**| 计划部门接收返修需求。|
| **3 返修排程** | 工单专员结合产线产能情况，重新安排工单的返修需求直接上线。 |

### 2.1.9 紧急插单需求管理流程
定义所有的在 MPS 主计划锁定之后，提交的需求管理流程。例如，在 15号锁定了 MPS 主计划之后，再次提交的需求，需要归结为紧急插单流程。紧急插单的需求接收后，PMC 需要联合采购、生产、仓库部门联合评估。评估完成后，紧急插单需求被确认，并触发 MPS 计划变更流程。紧急插单的流程与需求变更流程的最大区别是：是否存在已锁定的需求，如果已经申报过需求，则走需求变更流程。计划人员在评估插单需求的同时，也是会基于此点驳回可能的紧急插单需求。

[图 2.1-10 紧急插单需求管理流程]

**表 2.1-9 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 提交插单需求** | 需求来源包括与售后、研发、销售。针对不同的来源，可选择的产品料号范围同相应的需求提交的范围。针对于已有的需求进行加急、调整等属于需求变更流程，不可以通过插单需求提交，流程层面也会校验此条件。 |
| **2 评估需求** | 计划专员接收需求后，协调各部门对插单影响进行评估，并输出影响报告。**说明**: 1. 计划专员评估影响时需要提供物料、工单层面的影响事件。 2. 业务部门针对插单影响做最后确认，此时亦可关闭此变更请求。 |
| **3 插单确认** | 提出部门需要最后进行插单确认，流程完成后，触发 MPS 主计划变更流程。 |

### 2.1.10 产能预估流程
产能数据是 PMC 进行产品交期承诺的重要数据之一。也是支撑 PMC 对工厂生产能力评估的重要手段，是后续进行生产排程的重要依据。产能数据应包括人员考勤、认证、在制品以及采购交期等等相关数据构成。

[图 2.1-11 产能预估流程]

**表 2.1-10 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 发起产能填报任务** | 计划员按需发起相应的填报任务。亦可通过定时任务，自动触发填报任务。 |
| **2 填报产能数据** | 填报完成后，并由部门审批确认后，方可正式生成预估报告。**说明**: 1. 生成制造部需要按表单填写相关的数据。 2. 并在部门确认后，计划员生成产能报告。|
| **3 产能数据确认**| 制造部确认产能数据。|
| **4 产能预估报告生成**| 计划员结合其他数据生成预估报告。可以结合仿真技术，更精准的进行产能预估。 |

### 2.1.11 MPS 主计划制定流程
对已经锁定的需求汇总后，依据产能数据、需求优先级、需求期望交期等其他相关因素生成相应的初步计划。计划需要明确给出业务部门提交的产品需求的交期承诺。初版计划是备料计划生成的数据输入，并在备料计划确认后，同备料计划同时锁定。

[图 2.1-12 MPS 主计划制定流程]

**表 2.1-11 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 汇总需求**| 汇总已锁定的各类需求。**说明**: 1. 计划员需要确认汇总的需求范围。 2. 初版计划的生成应结合合理的算法，并支持算法调整，来生成初版交期。 |
| **2 生成初版计划** | 可通过不同算法，试算不同需求的交期，例如产品族优先、紧急度优先。所有的交期最终应能映射到原始的需求单商。 |
| **3 提交初版计划**| 提交初版计划给销售部。|
| **4 评审初版计划** | 销售部门对初版计划进行评估确认。针对需要更新提交的计划，驳回后，计划员重新调整计划，并再次提交至销售部门评估确认。 |
| **5 发布初版计划** | 计划员针对评审通过的计划，发布最终的 MPS 主生产计划。 |

### 2.1.12 MPS 主计划变更流程
MPS 主计划变更大多是由于需求发生了变更触发此流程执行。MPS 主计划变更后，需要再次执行物流需求计划重新生成，并执行后续的采购及工单的变更流程。

[图 2.1-13 MPS 主计划变更流程]

**表 2.1-12 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 变更发起** | MPS 主计划的变更由需求变更流程在确认后，主动触发。 |
| **2 变更审批** | 业务部门针对需求变更引起的主生产计划变更再次确认。确认后，流转至生产计划员处。 |
| **3 变更确认** | 计划员对主计划的变更进行评估确认。确认通过的触发工单变更流程，不通过的，由业务部门重新进行变更发起。 |

### 2.1.13 物料需求计划生成流程
通过需求以及 MPS 主计划流程的关键点控制，发布后的 MPS 主计划产品都是有完整且有效的 BOM 组件。展开 BOM 后，基于原材料维度，按基本单位，进行需求数量合并，获取到毛需求。结合当前库存数据及安全库存，获得最终计划周期内的物料需求数量（包括各替代料），并生成物料计划。

PMC 提交至采购评审，在评审完成后，进行计划锁定，同时重新评估同一计划期内的关联的初版状态下的 MPS 主计划的交期承诺，完成 MPS 主计划的发布。

特别注意：如果选择的 MPS 主计划是变更后的主计划，需要及时提示BOM 差异（如果存在）。

[图 2.1-14 物料需求计划生成流程]

**表 2.1-13 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 MPS 主计划选择** | 已发布的 MPS 主计划方可被选择，进行物料需求计划的生成。**说明**: 1. 已发布的毛需求可被选择。 2. MPS计划需记录下物料计划生成的BOM数据。 3. 毛需求应按物料维度进行合并。 |
| **2 BOM 展开** | 按 BOM 组件及数量，生成相应的组件毛需求数量。MPS 主计划需记录下此时展开的 BOM 数据，以便在后续的变更流程中进行比对使用。 |
| **3 毛需求计算**| 合并库存检查后，计算毛需求。|
| **4 净需求计算**| 结合在途库存和安全库存，计算净需求。|
| **5 生成计划**| 生成物料需求计划。|
| **6 评审计划** | 采购部对生成的物料需求计划进行采购交期确认。BOM 的替代料在由采购部评审同时进行确认。 |
| **7 锁定计划**| 锁定物料需求计划。|

### 2.1.14 备料计划转采购流程
物料计划员选择已锁定的物料计划，转采购申请，并经由部门审批后，转采购申请单。

[图 2.1-15 备料计划转采购流程]

### 2.1.15 计划工单流程
计划工单专员，选择已发布完成的 MPS 主计划，进行计划工单生成。生成后的计划工单同时锁定了工单 BOM。建议按 MPS 主计划和计划工单 1 对 1 的方式生成工单。

[图 2.1-16 计划工单流程]

**表 2.1-14 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 MPS主计划选择**| 已发布的MPS主计划方可被选择。|
| **2 计划工单生成** | 计划工单与 MPS 主计划应 1 对 1 生成。计划提交时，需要进行齐套检查。 |
| **3 计划工单提交**| 提交计划工单。|
| **4 计划工单下达**| 下达计划工单。|

### 2.1.16 工单变更流程
工单的变更来源基本有 2 个：ECN 变更与 MPS 主计划变更。工单的变更最重要的是针对变更的评估，特别是 BOM 发生变化后，当前的库存是否能支撑工单原计划的生产，如果不能，需要及时与采购沟通，并发起采购申请。待采购交期承诺后，进行工单的变更审批以及后续的齐套检查通过。

[图 2.1-17 计划工单变更流程]

### 2.1.17 工单关闭流程
工单关闭亦称为工单结案。由工单专员执行监控。针对按工单数据正常生产及入库的，在入库后，依据入库数量与工单数量+发料数量及正常损耗，自动进行工单关闭的确认。

[图 2.1-18 工单关闭流程]
*   **说明**: 1. 满足如下2个条件，可以自动关闭闲置结案：入库单=工单数量，且发料数量<工单需求数量+损耗范围(剔除部分物料)。 2. 非自动入库工单，人工确认关闭结案。

### 2.1.18 日排程制定流程
针对已下达的工单，有排程计划专员进行 n（建议是 7）天后的日排程计划生成。形成连续 2 周的日排程滚动计划。日排程计划生成后，由生产制造部确认并下达。针对特殊情况需要对当日计划进行调整的，生产制造部上报 PMC进行计划调整即可。

[图 2.1-19 日排程制定流程]
*   **说明**: 例如，每周四前需下周一到周日的排程计划，预估下下周一下周三的排程计划。

## 2.2 仓储管理
仓储模块总体流程框架如下所示：

`[图 2.2-1 仓储管理流程框架]`
**流程框架描述:**
该框架图以“从收货到发货”为主线，将仓储管理分为五个核心部分：
- **2.1 收货入库:** 包括采购收货(2.2.1)、退料入库(2.2.2)。
- **2.2 仓内管理:** 包括盘点(2.2.17)、报损(2.2.18)、库龄监控(2.2.19)。
- **2.3 拣货出库:** 包括工单发料(2.2.3)、成本中心领用(2.2.4)、销售调拨出库(2.2.5)、委外调拨出库(2.2.6)、委外反冲发料(2.2.8)、采购退货出库(2.2.7)。
- **2.4 成品入库:** 包括生产入库(2.2.9)、销售退货入库(2.2.10)、成品仓调拨入库(2.2.11)。
- **2.5 成品出库:** 包括销售出库(2.2.12)、返工成品出库(2.2.13)、成品仓调拨出库(2.2.14)、OEM直发出库(2.2.15)、泰国原料出库(2.2.16)。

仓储模块包含了原材料仓和成品仓的业务管理全场景。通过仓储模块要达成账实一致，除了要管理好库存的数量，同时也要着重关注库存的价值。这就要求，仓储人员在实际的业务过程中，一切以实物流动为根本，见单入库与出库，保证账实一致。仓库操作人员也要秉承着：单据->实物->过账的标准业务流程，严禁无单入库、出库。

仓库的存储区在大的逻辑设计上应包括如下：
*   **原材料仓：** 收货暂存区、结构件存储区、电子件存储区、生产配送暂存区、不良品区、各委外仓存储区（按委外供应商建立）。
*   **成品仓：** 良品存储区、不良品存储区、返工/返修件存储区、呆滞品存储区、各 OEM 厂商存储区。

**特别注意的是：**
针对委外物料的管理，是通过建立不同仓库的方式，通过仓库调拨的方式来实现对委外物料的库存管理。

线边仓管理上，由生产部统一管理，不纳入到仓储模块流程中。

仓库管理的另外一个核心理念是要做到批次管理，结合合理的出入库策略（如 FIFO），实现清晰的从原料到成品的追溯数据流。

物料、仓位、容器等基础信息的条码化，是仓库数字化的基石。所有需要入库的物料，需按公司主数据管理模块的条码生成规则，给予物料条码信息。仓库可协同采购部门共同辅助完成供应商的贴码工作，并作为供应商遴选准入的重要指标之一。物料条码化是仓库数字化的起点，也是仓库能否实现较为准确的批次管理的最重要手段之一。

物料批次分类管理是本次流程改革的重点，通过物料的条码化，结合相关的硬件设备（PDA、AGV）以及不同物料分类设定的上下架策略，实现入库批次的生成，出库批次的准确指定，有效实现批次库存的管理。建议批次号生成规则可按原料生产日期（如果供应商提供）或者入库日期生成，以支持基于生产日期或者入库日期的 FIFO 出库策略。

### 2.2.1 采购收货管理流程
采购收货流程是个端到端的长流程，涉及仓库部分的流程节点为流程后半部分。仓库完成收货的前提是前置流程提供的数据准确无误，仓库人员见单收货，并留存收货凭证。仓库人员负责卸货，但仓库人员不对质检区的物料负责保管。由于收货流程链条长，牵涉的部门多，确保每个部门的边界是整个收货管理流程的重点内容。

所有供应商按仓库管理部门提供的送货单样式打印带条码的送货单，随车送抵仓库。供应商送货前，应提前预约，以便于质检与仓库进行合理的人员安排。

针对紧急标识的送货单，收货员结合工单发料单，直接收货至工单发料暂存区，以支持快速发往车间线边仓。

成品仓针对泰国原材料的收货入库流程，也遵循此流程实现。

`[图 2.2-2 采购收货管理流程]`
**流程描述:**
- **泳道:** 采购部, 供应商, 质检部, 收货员
- **流程:** 供应商通过`预约送货管理流程`后送货。收货员`0 卸货`后，物料进入`质检部`进行`质检`。质检通过后，收货员`1 选择送货单`进行`2 收货`，收货时可触发`2.3.6 委外反冲过程`。然后进行`4 打码与贴码`，最后`3 上架`。采购部的`采购订单管理流程`和`采购结算管理流程`与此流程关联。

**表 2.2-1 活动控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **0 卸货** | ⚫ 仓库依据供应商的预约送货情况，安排叉车人员适时的进行卸货操作<br>⚫ 卸货到来料暂存区，待质检部门进行审核 |
| **质检** | 质检合格的送货单物料，转为仓库的收货在途库存值 |
| **1 选择送货单** | 送货单质检通过后，可以被收货的叉车工进行选择 |
| **2 收货** | ⚫ 收货依据送货单，收货至暂存区，待收货人员上架。此时物料库存在收货暂存区过账。<br>⚫ 收货人员通过物料条码，确认相应的入库批次号和相应的批次分类值。<br>⚫ 收货的容器分为：料箱和托盘，分别进行容器码管理。不同于 LPN，容器码将张贴在料箱与托盘上，收货的物料应放置到相应的容器中，并在容器上架后，容器码与相应的货位进行绑定<br>⚫ 针对委外的物料，触发委外收货反冲流程。自动进行物料反冲操作，委外仓的库存扣除<br>⚫ 收货完成后，推送收货数据至采购结算流程，支持后续的供应商结算 |
| **3 上架** | 收货人员依据上架策略、确认收货批次后，操作 AGV 或叉车将货物从暂存区上架到存储区 |

### 2.2.2 退料入库流程
退料的场景包括如下：
*   **工单退料：** 是指发往车间的原材料或者发往成品仓的物料被退回后，重新入库的场景。
*   **成本中心领用退料：** 基于成本中心领料后退回
*   **销售调拨出库退料：** 发往成品仓进行销售的原料退回
*   **委外调拨出库退料：** 发往委外供应商的原料退回
*   **泰国原材料退回：** 成品仓发往泰国的原材料退回

`[图 2.2-3 退料入库流程]`
**流程描述:**
- **泳道:** 需求/退料申请部门, 质检部, 收货员
- **流程:** 申请部门根据`发料/领用单` `1 创建退料单`，并将料品送至`来料待检区`。`质检部`进行`退料质检流程`，输出`退料质检报告`。`收货员` `3 选择退料单`，进行`4 收货上架`，完成入库。

**表 2.2-2 活动控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 创建退料单** | 业务部门根据实际情况填写退料单，其中可以根据仓库的发料单，按原单进行退料。<br>选择需要退送的物料时候，需要提供发料的批次号，确保退送物料的批次正确性。 |
| **质检** | 针对销售/生产的退料，在质检通过后，进行成品仓/线边仓的扣料过账操作。<br>针对成本中心领料的退单，质检通过后创建冲销单据进行成本冲销。<br>以上场景下，质检后，仓库的收货在途库存增加。<br>委外退料的，质检通过后，进行库存转移，由委外仓转移到暂存区。<br>质检完成后触发账务处理环节，依据不同的退料场景进行不同的处理。<br>工单退料由车间 MES 进行扣料过账。<br>成本中心领用退料则对原单据进行冲销。<br>销售调拨出库退料，由成品仓进行扣料过账。<br>委外调拨出库退料，则完成委外虚拟仓到暂存区的库存转移。 |
| **4 收货上架** | 收货人员基于退料原因，结合质检结果，追溯相关的入库批次后，追加批次特征值：退料处理方式。<br>**退货：** 收货至不良品退货区，同时主动发起采购退货流程至采购员。<br>**报损：** 收货至不良品报损区，同时主动发起仓库报损流程。<br>**良品：** 正常收货到相应的存储区。 |

### 2.2.3 工单发料管理流程
工单计划专员按日排程计划，基于工单创建发料单，按工单 BOM 数量领料，一般提前 n(n>=1)天创建，并提交至仓库，支持仓库提前拣货。拣货员下架到齐套物料暂存区中后，由 AGV 配送发到车间线边仓中。原则上不允许进行补料或者超领的行为发生，如确实需要，工单计划专员需要调整日排程计划，并严格遵循工单变更流程管理规范，变更工单组件后，按工单发料。
物料一旦经过配送出库后，仓库库存扣减，成本转入生产车间下。

`[图 2.2-4 工单发料管理流程]`
**流程描述:**
- **泳道:** 计划/工单员, 调度员, 拣货员, 配送员
- **流程:** `计划/工单员`根据`日排程计划` `1 创建工单发料单`。`调度员` `2 接收发料数据`并`3 汇总发料数据`，生成`4 生成拣货任务`。`拣货员` `5 拣货下架至齐套暂存区`。`配送员` `6 配送出库`，完成流程。

**表 2.2-3 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 创建工单发料单** | 工单计划专员按工单组件创建发料单，检查齐套库存。 |
| **2 拣货任务** | 仓库调度员可根据发货频率及齐套暂存区存储情况，适时的进行拣货任务的生成。 |
| **5 拣货下架** | 货物拣货下架到齐套暂存区后，库存同时进行转移，成本记入线边仓存货，转入车间成本中心。 |
| **6 配送** | 配送人员通过 AGV 完成配送出库后，自动进行库存扣减过账。 |

### 2.2.4 成本中心领用流程
需要记入领用部门成本中心的领料业务，统一走成本中心领用流程。领用需求得到审批通过后，需求部门发起创建领料单，并提交至仓库。仓库拣货下架后，由需求部门领用出库。

`[图 2.2-5 成本中心领用流程]`
**流程描述:**
- **泳道:** 领料需求部门/成本中心, 调度员
- **流程:** 需求部门经`领料审批流程`后 `1 创建领料单`。`调度员` `2 选择领料单`，进行`3 拣货下架`，然后由需求部门`4 确认出库`。

**表 2.2-4 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 创建领料单** | 领料单创建后，需求部门应及时领料。仓库不保证，领用的库存的库存准确性。 |
| **3 拣货下架** | 拣货下架后，货物存在在出库暂存区，库存发生转移。 |
| **4 确认出库** | 仓库调度人员需当面在需求部门人员面前进行出库确认动作，完成出库交接。库存扣减过账完成。成本记入相应的成本中心。 |

### 2.2.5 销售调拨出库流程
销售部门依据销售订单，针对需要销售的原材料，向原料仓发起调拨申请，转入成品仓。
原料仓调度员按单进行拣货下架，并完成出库确认。

`[图 2.2-6 销售调拨出库流程]`
**流程描述:**
- **泳道:** 销售部, 调度员
- **流程:** `销售部`根据`销售订单` `1 创建调拨单`。`调度员` `2 选择调拨单`，进行`3 拣货下架`，最后`4 确认出库`。

**表 2.2-5 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 创建领料单** | 调拨单创建后，仓库应保证调拨单库存的有效。 |
| **3 拣货下架** | 拣货下架后，货物存在在出库暂存区，库存发生转移。 |
| **4 确认出库** | 仓库调度人员需当面在收货人面前进行出库确认动作，完成出库交接。库存扣减过账完成。成本转入成品仓，总体存货价值不变。 |

### 2.2.6 委外调拨出库流程
采购人员根据采购订单的需求，依据委外供应商的投料需求，创建委外调拨单，由仓库发料至委外供应商处。同时仓库通过为每个委外供应商建立虚拟仓的方式，进行委外物料库存的管理。

`[图 2.2-7 委外调拨出库流程]`
**流程描述:**
- **泳道:** 采购部, 拣货员
- **流程:** `采购部`根据`采购订单` `1 创建委外调拨单`。`拣货员` `2 选择调拨单`，进行`3 拣货下架`，最后`4 确认出库`。

**表 2.2-6 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 创建委外调拨单** | 调拨单创建后，仓库应保证委外调拨单库存的有效。 |
| **3 拣货下架** | 拣货下架后，货物存放至委外发货暂存区。 |
| **4 确认出库** | 仓库调度人员在完成出库后，同时完成库存转移，即同时触发委外虚拟仓的入库过账。存货价值无变化。 |

### 2.2.7 采购退货出库流程
仓库在收货入库后，发现的原料质量问题，应立即向采购发起退货申请，采购审批并转成供退货订单后，由仓库进行拣货下架，并当面交由供应商，完成退货出库。

`[图 2.2-8 采购退货出库流程]`
**流程描述:**
- **泳道:** 采购部, 财务员, 拣货员
- **流程:** `财务员` `1 创建退货申请`，`采购部`根据`采购入库单` `2 创建采购退货单`。`拣货员` `3 选择退货单`，进行`4 拣货下架`，最后`5 确认出库`。

**表 2.2-7 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 创建退货申请单** | 账务员要明确需要退货的批次，并且所退的物料应在不良品存储区。 |
| **4 拣货下架** | 拣货下架后，货物存放至委外发货暂存区。 |
| **5 确认出库** | 仓库调度人员需应在发货时候出库确认动作，完成出库。库存扣减过账完成。成本记入采购退货订单，冲销财务应付。 |

### 2.2.8 委外反冲发料流程
采购收货入库后，针对属于委外生产，触发此作业流程。
供应商选择特定的入库单后，自动展开 BOM，并按 BOM 建议数量，填写投料数量，同时支持自行添加 BOM 组件物料，填写实际投料。
仓库账务员根据供应商填写的情况，核对与标准 BOM 的差异数量，确认后，完成反冲过账确认。扣除委外虚拟仓的物料库存。
委外虚拟仓应设定按月盘点，及时通过上述业务找出库存差异，并及时反馈至采购部门，通知供应商及时补充说明库存差异情况。
建议与采购结算协同，建立考核指标，并落实到流程节点中，针对未能及时有效准确的提供投料数量的订单，予以暂结算或者延长账期的方式，直至落实投料数据。

`[图 2.2-9 委外反冲发料流程]`
**流程描述:**
- **泳道:** 供应商, 研发, 账务员
- **流程:** `供应商`在`采购收货流程`和`产品BOM`的基础上，`1 选择入库单`并`2 填写实际投料数`。`研发`进行`3 确认`。`账务员` `3 核对差异数量`，检查通过后`4 反冲过账确认`。

**表 2.2-8 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 选择入库单** | 支持采购收货流程自动创建入库填报单。<br>供应商可调整需要填写的 bom 组件，并按实际投料情况填写投料数据。 |
| **3 核对差异数量** | 研发部门在确认了供应商填报的数量后，系统自动执行库存可用检查，并反冲过账。针对虚拟仓库存可用检查不通过的，账务员结合当前委外虚拟仓的实际库存，进行异常处理。 |
| **4 反冲过账确认** | 账务员在反冲过账确认后，自动生成委外生产工单，并按供应商填写的投料数量，对此工单进行投料过账。成本直接记入上述的生产工单中，转成品存货。 |

### 2.2.9 生产入库管理流程
生产线完成组装调试后，质检部门进行全检后，由生产部门创建生产入库单。仓库接收到入库单后，成品仓前往包装区接收货物，并收货上架。
通过前置的生产主流程执行，在数据流层面，每个入库的产成品在生产下线时已张贴了成品的 SN 码：携带有批次+货主等相关信息，在收货上架需识别此类信息，触发相应的上架策略，收货到合适的仓位中。
其中半成品的入库不再经由成品仓，直接在生产线边仓进行管理即可。

`[图 2.2-10 生产入库管理流程]`
**流程描述:**
- **泳道:** 质检/生产部, 仓库员
- **流程:** `生产部`在`产成品质检流程`和`生产执行`、`生产包装`后，`1 创建入库单`。`仓库员`在车间包装区 `2 选择入库单`，进行`3 收货上架`。

**表 2.2-9 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 创建生产入库单** | 包装工序完成后，车间人员需要主动创建入库单，填写每个入库产成品的 SN 码信息。<br>车间将货物送至成品仓收货区。 |
| **2 选择入库单** | 成品仓仓管员再接收到入库单后，可择时携带叉车前往车间包装区，通过选择入库单，对相应的产成品进行收货操作。<br>收货时候，通过外包装的 SN 码进行货物确认。 |
| **3 收货上架** | 实物确认后，基于收货上架策略将实物送往指定仓位。 |

### 2.2.10 销售退货入库流程
销售或者人员创建销售退货单后（要确保填入正确的产品 SN 码），仓管员基于退货单接收货物到退货暂存区，并立即提交质检申请，待质检完成后，结合质检结论，收货到相应的存储区：
*   需返修产品，收货到返工返修区；
*   不良品转入不良品区，待后续走报损流程报废；
*   正常良品走直接收货至正常存储区。

`[图 2.2-11 销售退货入库流程]`
**流程描述:**
- **泳道:** 销售/售后, 仓管员, 质检员
- **流程:** `销售/售后`根据`退货销售订单流程` `1 创建退货入库单`。`仓管员` `2 选择入库单`，进行`3 卸货`，并`4 提交质检申请`。`质检员`进行`退货质检流程`，`5 质检`后出具`质检报告`。`仓管员`依据质检结果`6 收货上架`。

**表 2.2-10 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 创建退货入库单** | 销售或者售后人员创建销售退货单，亦可由销售退货流程触发单据创建。<br>需要明确退货产品的 SN 码，并校验 SN 的正确性。 |
| **3 卸货** | 仓管员基于退货单，核验好货物后，卸货到暂存区，提交质检申请，待质检人员质检。 |
| **5 质检** | 质检结论至关重要，直接决定了后续货物的处理方式。 |
| **6 收货上架** | 遵循质检结论，收货上架到相应的存储区中。 |

### 2.2.11 成品仓调拨入库流程
调拨入库的场景有 2 种：原材料仓的销售调拨入库和成品仓的仓间调拨入库。

`[图 2.2-12 成品仓调拨入库流程]`
**流程描述:**
- **泳道:** 仓库员
- **流程:** `仓库员`在接到调拨单后，`1 选择调拨单`，进行`2 收货上架`，完成入库。

**表 2.2-11 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 选择调拨单** | 仓管员接收到实物后，选择或者扫描调拨单，确认此调拨单已出库完成。 |
| **2 收货上架** | 卸货可与收货同时进行，即卸货到指定的存储区：需返工的存入返工存储区，不良品的存入不良品区，正常品存入良品区。存货价值不发生变化。 |

成品仓的采购入库流程，同原料仓的采购收货流程（2.2.1 采购收货流程）。即先质检、质检通过后，完成货物的收货上架。

### 2.2.12 销售出库流程
销售部门在可对库存充足的产品发起交货流程，并通知到物流团队进行车辆安排等发运工作。
物流部门按预约时间，进行装箱发运。仓库可按预约时间，及时进行拣货下架到发货区，等待装车。

`[图 2.2-13 销售出库流程]`
**流程描述:**
- **泳道:** 销售部, 物流部门, 财务员, 拣货员
- **流程:** `销售部`发起`销售订单管理流程`和`销售订单交货流程`，生成`运输需求单`和`出库单`。`物流部门`执行`发运流程`。`财务员` `1 选择销售出库单`并`2 匹配库存产品`。`拣货员` `3 拣货下架`并`4 装箱出库`。

**表 2.2-12 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 匹配库存产品** | 通过出库单的相关信息：货主、产品信息等以及出库批次策略，确定相应批次的产品，创建下架任务。 |
| **2 拣货下架** | 提前拣货的产品可下架到待发货区，也可由存储区直接下架到物流车上。 |
| **3 装箱出库** | 与物流人员交接完成后，发起装箱出库，扣减库存。成本记入相应销售订单，转应收。 |

### 2.2.13 返工成品出库流程
生产计划部针对日排程计划中，涉及到返工工单的，创建返工出库单，明确返工的产品 SN 码。
仓库接到返工出库单后，进行拣货下架，并配送到车间包装区，由车间人员确认后，完成交接。

`[图 2.2-14 返工成品出库流程]`
**流程描述:**
- **泳道:** 计划/工单员, 仓库员
- **流程:** `计划/工单员`根据`日排程计划` `1 创建返工出库单`。`仓库员` `2 选择返工出库单`并`2 拣货下架`。

**表 2.2-13 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 创建返工出库单** | 提供明确的 SN 码。 |
| **3 拣货下架** | 对指定 SN 的产品下架，并配送至车间包装区，确认后扣减库存，成本记入返工工单。 |

### 2.2.14 成品仓调拨出库流程
仓间调拨一般由调入方发起调拨申请，调出方确认后，进行拣货下架后，装箱出库。

`[图 2.2-15 成品仓调拨出库流程]`
**流程描述:**
- **泳道:** 调入方/仓库员, 调出方/仓库员, 仓管员
- **流程:** `调入方` `1 创建调拨单`。`调出方` `2 审批调拨单`。`仓管员` `3 拣货下架`并`4 装箱出库`。

**表 2.2-14 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 创建仓间调拨单** | 调入方只可选择有库存的产品发起调拨申请。 |
| **4 装箱出库** | 装箱出库后，调出方库存扣减，转入调入方的在途库存。存货价值无变化。 |

### 2.2.15 OEM 直发出库流程
销售部门在收到客户验收单后，创建 OEM 出库单，附上收货凭证。仓库账务员确认信息后，后续收货、反冲及发货流程自动触发。

`[图 2.2-16OEM 直发出库流程]`
**流程描述:**
- **泳道:** 销售部, 物流部门, OEM厂商, 账务员
- **流程:** `销售部`根据`OEM销售订单交货流程`创建`OEM出库单`。`OEM厂商` `1 发货确认`。`账务员` `2 确认OEM出库单`，触发`3 自动收货`和`4 自动发货出库`。

**表 2.2-15 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 创建 OEM 出库单** | 明确委外供应商及产品 SN 信息。 |
| **2 确认出库单** | 账务员在确认出库单后，自动进行收发货，成本记入销售订单，转应收账款。 |

### 2.2.16 泰国原料出库
国内负责泰国业务的订单管理员结合泰国工厂的生产计划数据，下达销售订单。仓库按销售订单数据进行交货出库。

`[图 2.2-17 泰国原料出库]`
**流程描述:**
- **泳道:** 订单交付员, 物流部门, 拣货员, 装车人员
- **流程:** `订单交付员`根据`销售订单流程`创建`销售订单`。`物流部门`执行`发运流程`。`拣货员` `1 选择销售订单`并`2 拣货下架`。`装车人员` `3 装箱出库`。

### 2.2.17 盘点管理流程
仓库盘点分为动态盘点、月盘以及年盘。针对月盘以及年盘的类型，一旦发起盘点后，所有货物的移动操作都必须停止，保证盘点数量的准确。仓库应对盘点结果做分析，进而提升仓库的日常管理水平。

`[图 2.2-18 盘点管理流程]` (注：图标题应为盘点管理流程)
**流程描述:**
- **泳道:** 拣货员, 财务员, 仓库主管
- **流程:** `拣货员` `1 创建盘点单`后进行`2 盘点`。`财务员` `3 核对`，判断是否需要`复盘`。核对无误后，提交`仓库主管` `需要审批`。审批通过后进行`5 过账`。

**表 2.2-16 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 创建盘点单** | 一旦盘点单建立后，针对盘点单上的物料，禁止一切的货物移动行为。 |
| **3 核对** | 仓库账务员应认证核对盘盈盘亏的数量以及存货价值的差异。判断是否需要复盘，如有需求，发起复盘后重新核对。核对完成后，针对需要进行过账审批的，转仓库主管审批。 |
| **5 过账** | 过账完成后，货物移动被允许。成本记入相应仓库成本中心。 |

### 2.2.18 报损管理流程
仓库的报损包括呆滞品处理、货物损坏等依然存在实物的出库管理流程。针对实物不存在的业务场景，仓库需要通过盘点操作，做盘亏处理。
在管理规范层面，建议仓库在做报损操作前，将待报损物料通过库内转移的操作，移动至不良品报废存储区，之后直接从报废区直接进行出库确认。
同时管理层面，需要设定好不同物料的库龄，基于库龄管理规范，协同库内检测流程，及时进行呆滞品的确认。

`[图 2.2-19 报损管理流程]`
**流程描述:**
- **泳道:** 财务员, 仓库主管, 拣货员
- **流程:** `财务员` `1 创建报损单`，`仓库主管`进行`库内检验流程`后`2 报损审批`。`拣货员` `3 拣货`后，进行`4 出库确认`。

**表 2.2-17 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **1 创建报损单** | 针对超期或者长期呆滞品可经由检验流程直接触发报损单。<br>报损单需要明确报损的原因。<br>报损的物料建议提前转入报废存储区。 |
| **5 出库确认** | 出库确认后，库存扣减，成本记入单据提供的成本中心。 |

### 2.2.19 库龄监控
仓库库龄监控管理是库存管理的重要组成部分，旨在通过监控物品在库时间，优化库存周转，减少呆滞物料和资金占用。
库龄的定义：从物品入库之日起到当前日期的在库时间。
库龄分类标准可按如下定义，具体情况可按实际执行：
*   **0-30 天（正常）**
*   **31-90 天（关注）**
*   **91-180 天（预警）**
*   **180 天以上（呆滞）**

后续管理过程中，通过数字化工具，应定期（如每周、每月）生成库龄分析报告，可按物料类别、价值、库龄段等不同维度展开报告，并对超期库存进行醒目标识，进行预警。

**表 2.2-18 活动关键控制点**
| 活动 | 关键控制点 |
| :--- | :--- |
| **数据采集与记录** | 收货入库的批次管理，批次特征值应包括入库日期。<br>库存物料的库龄台账记录。 |
| **库龄分析报告** | 定期分析：定期生成报告。<br>分类统计可按物料类别、价值、库龄段等维度分析。<br>异常标识：对超期库存醒目标识。 |
| **处理措施** | **报损处理：** 已过期或者无法使用物料按报损流程处理。<br>**供应商退货：** 与供应商协商退回。<br>**返工处理：** 对一些成品可定返工工艺后，重新生产。 |
| **优化措施** | 定期回顾库龄管理效果。<br>优化采购策略与供应链协同。<br>加强销售预测准确性。 |

## 2.3 生产管理
生产模块总体业务流程框架如下：

[图 2.3-1 生产制造流程]

新工厂主要有三个车间：逆变器生产车间、PACK 生产车间、PCBA 生产车间。建筑面积约 26000 ㎡，其中避难通道约 1500 ㎡；排烟、排风、消防等公共设施约 2200 ㎡。

[图 2.3-2 车间平面布局图]

其中：
(1) PACK 车间的产品供给储能工厂；
(2) PCBA 车间主要是做 PCBA 的软件烧录、FCT 测试、IGBT 压接、三防涂覆、点胶、配板、数采产品的加工工艺。
(3) 逆变器生产车间，涵盖逆变器的装配、检测、装箱打包，主要包含以下几条线：
*   逆变器组装线 100-350KW4 条
*   逆变器组装线 50-250KW2 条
*   逆变器组装线 3-50KW2 条
*   测试线 L1:50-350KW
*   测试线 L2:50-350KW
*   测试线 L3:50-250KW
*   测试线 L4:3-50KW
*   装箱打包一线
*   装箱打包二线
*   装箱打包三线
*   装箱打包四线

总体的生产工艺流程设计上分为逆变器产品工艺与 PACK 产品工艺：
逆变器工艺总体流程设计：共 47 工序，自动化/半自动 29 个工序，自动化设备覆盖率在 60%

[图 2.3-3 逆变器生产工艺流程]

PACK 产品工艺流程设计：

[图 2.3-4 PACK 产品工艺流程]

### 2.3.1 产线工艺规划
#### 2.3.1.1 自动产品识别流程
**业务场景**：描述产线首工位上线流程，产线是通过 RFID 进行识别跟踪，以下是说明生产管理模块如何与产线中控系统对接实现，完成产品的跟踪识别。

[图 2.3-5 自动产品识别流程]

**流程说明**：
PMC 通过工单派工流程，要实现从工单的一机多单拆分为一机一单，工单需要排台到产线，而且需要为每台机器生成 SN 号。

在每个产线线头增加 SQD 屏（sequencedisplay）,显示每条线的上线队列。

由 MES 系统打印 SN 标签。

由中控系统接扫码枪扫描 SN 号写入 RFID。

如果 RFID 写入或读取失败，需要转到异常呼叫流程。

[图 2.3-6 流程说明]

#### 2.3.1.2 拧紧防错流程
**业务场景**：描述如何与产线拧紧机和拧紧服务器集成，进行拧紧防错和拧紧数据的采集。

[图 2.3-7 场景展示]
[图 2.3-8 拧紧防错流程]

**流程说明**：
产线的拧紧机需要搭建拧紧服务器。
拧紧参数和拧紧的过程数据存储在拧紧系统中，拧紧系统提供报表，可以对实时数据进行分析和报表展示。
拧紧系统需要在工位上设置拧紧工位屏，用来显示拧紧的作业指导和拧紧实时数据。
生产管理模块接收拧紧的结果和拧紧数据，作为产品追溯数据。

#### 2.3.1.3 设备报警处理流程
**业务场景**：描述产线设备控制系统的故障如何跟产线管理模块进行对接。

[图 2.3-9 设备报警处理流程]

**流程说明**：
每条装配段的设备集中接入一个独立的控制系统。
每条测试线是一个独立的控制系统。
整个包装车间是一个独立的控制系统。
产线控制系统要定义设备异常信号的分类和规则，只有影响停线的信号才发送给生产管理模块，触发异常处理流程。

### 2.3.2 车间物料配送流程
#### 2.3.2.1 物料配送流程（组装线物料）
**业务场景**：生产部门根据生产计划创建生产工单，依据 BOM 表核算物料需求明确各工序的物料消耗定额；仓库进行备料并配送至暂存区；生产车间接收工单上线提醒，相关物料由 AGV 配送至原料暂存区，工单上线时呼叫物料，从暂存区取料至线边仓，工单开始投料生产，成品完工下线，并进入下线报工流程。

[图 2.3-10 物料配送流程（组装线物料）]

**流程说明**：
PMC 创建工单后，仓库把物料配送到线边缓冲区，仓库是根据车间流转到仓库的小车来驱动配送任务；
仓库配送到车间线边缓冲区后，库存从原料库调拨到线边库（此时实物在缓冲区）；
操作工通过产线工位屏，发起呼叫，从车间缓冲区拉动物料到线边仓（线边库的物料，从缓冲区转移到生产线边，在系统中进行记录），通过 AGV 进行配送；
AGV 配送到线边后，AGV 反馈送达信号，生产管理模块把物料库存从线边缓冲区转移到线边库。
生产下线报工后，进行下边报工的投料处理，投料的结果需要回传并扣减线边库；
不同的工单切换时，即使物料的料箱相同，也要做区分（不同工单用的通用料）；
库房配送的关键物料，需要按产品标识规范粘贴标签，记录批次号、生产日期等追溯信息；
按“两箱法”或“一备一”原则预存物料，确保生产启动时线边库存充足；
RCS/AGV 动态规划路线，避开拥堵区域；
线边仓按“先进先出”原则取用物料，监控余量并触发补料信号。

#### 2.3.2.2 物料配送流程（预装线及线外加工物料）
**业务场景**：生产部门根据生产计划创建生产工单，依据 BOM 表核算物料需求明确各工序的物料消耗定额；仓库进行备料并配送至暂存区；生产车间接收工单上线提醒，相关物料由 AGV 配送至原料暂存区，工单上线时呼叫物料，从暂存区取料至线边仓，工单开始投料生产，半成品完工下线，送至半成品暂存区。

[图 2.3-11 物料配送流程]

**流程说明**：
预装线包括 PCBA 线、风扇加工线、电感散热器组装线；
PCBA 线、风扇加工线、电感散热器组装线使用逆变器装配的工单，工单下达后，先进行预装线的生产；
其他流程同组装线物料配送。

#### 2.3.2.3 工单投料流程
**业务场景**：未来车间增加线边库，物料需要先从原料库调拨到线边缓冲区，然后再从车间缓冲区配送到线边，工单报工后才从线边库投料到工单。

[图 2.3-12 工单投料流程]

**流程说明**：
库房把物料配送到车间完成后，把物料从原料库调拨到线边缓冲区，然后再从车间缓冲区配送到线边；
工单报工后，把物料从线边库投料到工单（产品）。

### 2.3.3 线边库管理流程
**业务场景**：线边库管理对象是放置于车间的一些倒冲物料，如螺丝、胶水、胶带等，针对这些物料在车间按照水位进行管理，不按照工单进行领料。

[图 2.3-13 线边库管理流程]

**流程说明**：
生产管理模块根据线边库物料的水位（安全库存）触发仓库进行物料配送；
原料配送到指定地点（智能柜区域），物料调拨到线边库，暂存区的库位数量增加；
产品下线报工后，生产管理系统进行线边库物料扣减；
车间每月最后一天对线边库物料进行盘点，盘点的差异进行原因分析；
针对盘点差异，申请调账，经财务审批后，进行车间线边库库存调整。

### 2.3.4 生产异常处理流程
**业务场景**：工位发生异常：操作工发起呼叫（质量、设备、物料），来自产线控制系统（PLC）的故障错误（设备自身发生的内部故障、检测设备检测不通过）。

[图 2.3-14 生产异常处理流程]

**流程说明**：
产线的每个工位通过工位屏或按钮触发 ANDON 呼叫；
每条产线在线头安装一块 ANDON 显示大屏，建议为 65 吋以上，LED 显示屏；
每条产线在线头安装音响控制器（要求能够自定义语音播报，不少于 256 种），功放（要求可以调整音量），音响控制器通过 profinet 接入产线局域网；
异常发生后，工位操作工通过屏幕或按钮触发呼叫；
车间班组长，通过屏幕显示和语音播报确认是哪个工位发生故障；
产线班组长跟操作工进行问题诊断处理，如果超过工位节拍还没有处理完成，产线停线；
当异常发生超过 5 分钟（可配置）可以升级到办公室管理主管（短信或飞书），超过 10 分钟升级到办公室管理经理（短信或飞书）。

### 2.3.5 接线和线序检测流程
[图 2.3-15 接线和线序检测流程]
**线序**：功率板线序、输入板、电容板线序、输出板、通讯板线序；组串检测板（需评估）。

### 2.3.6 功率板螺钉漏装检测流程
[图 2.3-16 功率板螺钉漏装检测流程]
**螺钉**：电感、散热器螺钉；IGBT、功率板螺钉；输入板、电容板螺钉；输出板、通讯板螺钉；机框外部螺钉（需评估）；组串检测板螺钉（需评估）

### 2.3.7 清换线检查管理流程
#### 2.3.7.1 正常清换线流程
**业务场景**：正常生产过程中，上一个工单（批量）完成后，切换到下个工单（跟前一个工单的产品不同）生产时，需要执行正常清换线流程。

[图 2.3-17 正常清换线流程]

**流程说明**：
清换线涉及三方面的处理流程：物料、工装、螺丝和辅料；
工单剩余 10 台时，触发新工单的物料配送到原料暂存区，上一个工单的首站物料用完后，当前工装退出产线，进行正常流程进行补料，各岗位进行线边物料的再次确认；
工单剩余 2 台时，生产多能工螺丝物料和辅料从螺丝柜和辅料柜领料并存放到暂存区，上一个工单的首站物料用完后，班组长把螺丝物料和辅料转运到工位上；
工单剩余 2 台时，生产班长从工装柜领用工装，并且进行工装分发和力矩校验，新工单首件开始生产后，；
上述三个流程完成后，开始生产，首件检验车间和质量都需要参与，生产工艺的首检偏重的是过程，质量首检关注的是结果；
首检的任务，要通过系统规则自动触发生成首检任务（只要换工单就是触发首件检验）；
班组长对前一个工单的工装做退回处理；
针对线边仓多余的物料分两种情况，螺丝物料和辅料退回到辅料柜，由于退回的螺丝缺少标识，有些外观相近的物料，操作工有可能识别混淆，因此，针对退回的螺丝物料，需要经过班组长和质量人员进行二次确认、复核，防止螺丝的错放、混放，生产物料的结余执行“清换线物料异常处理流程”。

#### 2.3.7.2 异常清换线流程
**业务场景**：由于工单取消等原因，产线的工单要切换为不同产品的工单，导致的清换线流程。

[图 2.3-18 异常清换线流程]

**流程说明**：
异常清换线由 PMC 的计划变更发起：
计划调整后，由 PMC 创建新工单并进行派工；
仓车间对已上线产品全部组装后下线至成品缓存区，对已上线产品放置于成品缓冲区；
由生产多能工将线边物料清理并退库，将缓冲区的物料进行清理和退库；
由生产班组长进行新工单上线启动，然后进行正常的清换线流程；
或由车间工艺发起，原因可能是软件或测试异常、来料异常；
车间对当前工单进行停线；

#### 2.3.7.3 清换线物料异常处理流程
**业务场景**：针对清换线流程，结余的物料，需要进行退料，以下是描述清换线退料的流程。

[图 2.3-19 清换线物料异常处理流程]

**流程说明**：
车间针对车间物料进行过盘点，确认多出的需要退库的物料，并且发起车间物料退库流程；
由车间班组长跟生产工艺对退库的物料进行原因判定，在生产管理模块中发起退料申请；
由生产主管对退料单进行审批；
由质量进行物料质量判定；
对于需要报废的物料，在车间进行原料报废
对于需要退库的物料退回原料库，由仓库进行退库。

### 2.3.8 物料装配防错流程
**业务场景**：目前车间的 SN 码扫描只是进行记录，如果扫描的 SN 对应的零件跟当前产品（工单）不匹配，系统无法校验提示，存在错装风险。因此在扫描 SN 时，需要系统根据 BOM 等数据进行校验，对于错装的零件能够识别和防错。规范防错流程，配置防错件清单；车间站点扫描条码，与清单匹配的物料，记录防错条码信息；不匹配的物料，人工判断是否强制匹配，若强制匹配，则记录防错条码信息。

[图 2.3-20 物料装配防错流程]

**流程说明**：
需要扫描追溯的零件支持可配置，可以根据机型进行增加和删除；
物料装配防错流程中扫描的物料 SN 号等数据，后续需要对接产品追溯流程，可以通过产品追溯到完整的物料 SN 数据。
管理机制，规范防错流程，比如针对工位的某个零件，可以按照机型设置 SN 号包含某段特定字符。
下线检验人员可以在系统中快速查询检测结果。

### 2.3.9 产品追溯管理流程
**业务场景**：为达到产品可追溯管理目的，生产过程需记录设备工艺参数、物料数据、物料批次、工单工艺数据；装配过程记录操作工艺数据（如拧紧数据等），检测数据（包括照片）；测试环节记录所有测试数据（如安规测试、开闭环测试、老化测试、出厂测试、气密测试等）；包装环节记录包装物料。在追溯时，通过报表等形式快速检索产品历史数据。

[图 2.3-21 产品追溯管理流程]

**流程说明**：
*   **设备工艺参数**：IGBT 压力设定参数，其他不需要；
*   **工单数据**：工单号、机型号及工单相关的数据；
*   **物料追溯数据**：物料的 SN 号，通过扫描进行追溯；
*   **测试数据**：FCT 测试、气密性测试、安规测试、老化测试、三防测试等；
*   **检测结果**：测试工序的最终测试结果，OK/NOK；
*   **检测照片**：AI 检测工位的拍照照片；
*   **产品追溯报表**分为提供给外部的报表和提供内部的完整追溯数据查询报表；
*   在追溯时，上道工序没有完成，下到工序在执行确认时，需要做检查，如果检查不通过，就不能继续生产；
*   由于产品最终下线后一定是所有的缺陷都已经修复，因此提供给外部的报表所有结果都是 OK 的数据，过程检测的数据，包括设备工艺参数、检测数据等，不提供给外部报表查询。

### 2.3.10 作业指导书管理流程
**业务场景**：车间依据研发部产品设计相关文件，制作工艺文件，并以电子文件格式（包括视频、图片、PDF、Word、Excel 等），上传至 MES 系统，审核后发布；在车间，生产人员可以查看视频、动画演示，工艺作业指导文字描述，物料清单等，为工人提供详细的操作步骤和要求，指导工人完成每一道工序，降低误操作率，确保产品质量的一致性和稳定性。

[图 2.3-22 作业指导书管理流程]

**流程说明**：
工艺文件编制完成后，通过审核后，正式发布。
工艺文件要保留历史版本。
产线关键工位需要安装工位屏，可以实时调取显示已发布的工艺文件。

### 2.3.11 表单标签打印管理流程
**业务场景**：生产车间实时作业跟踪，生产工单分解成工位任务清单；生产任务分配后，工位任务列表生成打印数据。

[图 2.3-23 表单标签打印管理流程]

**流程说明**：
用户自定义打印模板，排版适配标签纸尺寸。
打印的数据，软件版本、SN 号、机型、工单号等信息从系统自动获取。
通过打印事件（例如工单发布、产品上线、产品到达某个站点）触发推送打印指令。

### 2.3.12 测试程序管理流程
**业务场景**：清换线后，测试设备的管理人员需要重新加载测试程序，同一个程序会存在多个版本，程序需要从本地选择加载。

[图 2.3-24 测试程序管理流程]

**流程说明**：
中控系统在车间搭建集中的程序文件管理模块，取代目前程序文件只保留在测试设备上的方式，统一集中保存在服务器端。
编制的程序文件提交后，保存在中控系统的服务器端，在服务器端要保留程序的历史版本。
同一个程序会存在多个版本，在中控系统需要建立 SN 号或料号与程序版本的对应关系，新版本的程序提交后，旧版本的程序自动归档。在执行清换线流程时，可以实现根据扫描的 SN 号（料号）自动选择加载程序。
测试机台通过扫描 SN 号，中控系统快速识别机型，并且根据机型和当前测试设备匹配下载正确的程序文件。

### 2.3.13 智能工装管理流程
#### 2.3.13.1 线边智能工装柜
**业务场景**：智能工装柜主要用于线边工装夹具领用管理场景，利用 RFID识别技术，提供唯一化标识，实现物品精准识别、出入库检测；适用于各型号夹具取用和归还自动化管理，提供自动借还、超期提醒、定检提示等功能。支持按需定制箱门，一门一物、多物按需存取，归还自动检测。前置的工装【需求收集-申请提交-采购-收货】等流程部分建议在采购业务域中完成，依据实际需要进行系统打通。

[图 2.3-25 智能工装管理流程]

**流程说明**：
*   能够实现智能身份验证、物品可以通过 RFID 芯片进行识别。
*   能够实现智能借还、实现逾期提醒。
*   实现自动出入库检测。
*   实时盘点在库状态。

#### 2.3.13.2 智能工装库
**业务场景**：智能工装库主要用于 PCBA 管理模式，建立无人值守智能库房。智能库分为货架仓储区和结算通道区，利用 RFID 原理，通过人脸门禁、RFID 通道门、结算台、手持仪、货架设备等，实现工装夹具的出入库自动识别、提交结算、记录、盘点等操作。并支持结算系统与第三方系统对接，同步状态、记录、库存等数据，打造智能化无人化的仓库管理模式。

[图 2.3-26 智能工装库管理流程]

**流程说明**：
*   能够实现人员验证开门、物品可以通过 RFID 芯片进行识别。
*   能够实现智能借还、自动记录人员、物品出入库情况。
*   实现物品出入库自动检测。
*   智能盘点物品在库状态。

### 2.3.14 智能耗材柜使用流程
**业务场景**：针对耗材类无需归还更换的物料（如绝缘胶带、硅脂刷、手套、自喷漆等）提供智能化领用管理。一位一屏货位设计，结合称重技术原理实现自动称重结算出入库物品数量，货位小屏自动更新显示库存，最小精度1g。实现物料的智能高效存取。前置的耗材【需求收集-申请提交-采购-收货】等流程部分建议在采购业务域中完成，依据实际需要进行系统打通。

[图 2.3-27 智能耗材柜使用流程]

**流程说明**：
*   支持货位亮灯指引，实时盘点库存，自动出入库结算；
*   能够实现智能身份验证；
*   支持领用数据报表统计导出；
*   支持系统对接，定制化开发功能；
*   存入物料是由资产管理员负责，取出物料由操作工执行；
*   设备的后台管理系统具备安全库存的管理。

### 2.3.15 智能工具柜使用流程
**业务场景**：适用于工具类物料借、还管理场景。利用激光打码技术，直接在工具上打码，不变形耐磨损；智能工具柜配备扫码识别模块，智能识别工具物料，用户验证开门，存取物品，并扫码进行核验确认，支持逾期提醒，实现物品智能借还管理。前置的工具【需求收集-申请提交-采购-收货】等流程部分建议在采购业务域中完成，依据实际需要进行系统打通。

[图 2.3-28 智能工具柜使用流程]

**流程说明**：
*   支持平面、曲面打码，打码深度可调节。
*   能够实现智能身份验证。
*   物品箱门绑定，扫码自动开门。
*   支持智能领还，逾期提醒功能。
*   支持系统对接，定制化开发功能。

### 2.3.16 自动发料柜使用流程
**业务场景**：结合 RFID 等技术，实现物料的智能高效存取。操作人领用物料，先进行身份验证，然后选择物料，输入数量，智能柜自动分拣出对应数量的物料，送到料品，操作人员领取物料。智能柜补料，操作人员进行身份验证，选择物料，智能柜打开补料箱，操作人在料口放入物料，关门确认完成。前置的物料【需求收集-申请提交-采购-收货】等流程部分建议在采购业务域中完成，依据实际需要进行系统打通。

[图 2.3-29 自动发料柜使用流程]

**流程说明**：
*   内置自动分料点数机，智能点数出货，兼容多种规格螺丝供料；
*   能够实现智能身份验证。
*   支持系统对接，定制化开发功能。

### 2.3.17 生产过程监控流程
**业务场景**：实时采集设备运行状态、生产参数、物料消耗、检测结果等数据，通过网络传输至服务器；通过 BI 工具在车间展示生产进度、设备利用率、质量合格率等关键指标，支持异常预警（如设备故障、超限报警）；通过数据分析，生成实时报表，移动终端设备便捷查询。

[图 2.3-30 生产过程监控流程]

**流程说明**：
*   通过 SCADA 系统集成设备控制层数据（如 PLC 控制器或 DCS 控制系统），形成从底层到上层的完整数据链路；
*   物料数据：物料库存统计数据、指标数据；
*   质量数据：下线合格率、直通率等指标数据，质量检验不合格，需要做提醒；
*   通过跟产线控制集成，采集产线上生产进度相关的数据；
*   设备参数采集，在后台做存储和处理；
*   检测数据在后台做存储和处理，检测不合格需要做提醒；
*   产线异常呼叫的信息，需要做提醒；
*   通过报表和产线的显示大屏展示产线的实时生产状态、关键的生产指标（计划数量、上线数量、下线数量、下线准时率等）；
*   数据可以把数据推送到移动端，比如飞书、小程序等。

#### 2.3.17.1 装配线数据采集清单
**表 2.3-1 装配线数据采集清单**
| 工艺流程 | 数据采集 |
| :--- | :--- |
| **软件烧录** | 烧录结果、不良品数据 |
| **FCT 测试** | 测试结果、关键数据、不良品数据 |
| **三防涂覆** | 三防检测结果 |
| **点胶** | 点胶检测结果 |
| **配板** | PCBA 供整机情况 |
| **电感、散热器组装** | 物料采集、螺丝结果 |
| **螺钉视觉检测** | 检测结果、图片 |
| **组装后盖板、外部风扇** | 物料采集、螺丝结果 |
| **螺钉视觉检测** | 检测结果、图片 |
| **安装 MC4 端子** | MC4 螺母结果 |
| **安装组串检测板 1** | 物料采集、螺丝结果 |
| **安装组串检测板 2** | 物料采集、螺丝结果 |
| **安装 AC 端子** | 螺丝结果 |
| **插接 MC4 端子线** | |
| **螺钉、线序视觉检测** | 检测结果、图片 |
| **MC4 端子铁芯检测** | 检测结果 |
| **是否压接 IGBT** | |
| **IGBT 压接、测高** | 压接过程数据、结果；测高数据 |
| **涂覆导热硅脂** | 检测结果、图片 |
| **安装功率板（功率板、IGBT 螺钉固定）** | 物料采集、螺丝结果；IGBT 螺丝扭力过程数据 |
| **功率板配线** | 螺丝结果 |
| **螺钉、线序视觉检测** | 检测结果、图片 |
| **安装电容板** | 物料采集、螺丝结果 |
| **安装输入板** | 物料采集、螺丝结果 |
| **螺钉、线序视觉检测** | 检测结果、图片 |
| **安装输出板、通讯板** | 物料采集、螺丝结果 |
| **安装电源板、控制板** | 物料采集、螺丝结果 |
| **螺钉、线序视觉检测** | 检测结果、图片 |

#### 2.3.17.2 测试检验数据采集清单
**表 2.3-2 测试检验数据采集清单**
| 工艺流程 | 数据采集 |
| :--- | :--- |
| **产品下线** | 组装半成品报工 |
| **安规测试** | 测试结果、数据、不良品数据 |
| **开、闭环测试** | 测试结果、关键数据、不良品数据 |
| **安装上盖** | 物料采集、螺丝结果 |
| **老化测试** | 测试结果、关键数据、不良品数据 |
| **出厂测试** | 测试结果、关键数据、不良品数据 |
| **产品下线** | 测试半成品报工 |
| **气密测试** | 测试结果、数据、不良品数据 |
| **附件配包** | 配包数据、图片 |
| **产品进箱** | 图片 |

### 2.3.18 返工、返修流程
#### 2.3.18.1 返修流程
**返修的业务场景**：首检、抽检、下线检验流程中发现产品不良，需要进行在线返修，以下是在线返修的流程：

[图 2.3-31 返修流程]

**流程说明**：
*   质量检验发现的缺陷信息，需要录入到系统中，进行在线返修处理。
*   车间测试员工在测试时发现的性能不良，由车间班组长开具不良维修单，并且录入生产管理模块，产品进入离线返修区进行返修处理；
*   针对装配操作工在装配线上发现的不良，如果无法在线返修，需要送到离线返修区返修的，需要经过班组长确认，如果确认需要进行离线返修，产品转运至离线返修进行返修。
*   车间维修工程师在返修时，通过移动终端或固定终端可以查询到产品的缺陷信息；
*   车间维修工程师在维修时，通过移动终端或固定终端可以录入维修记录、指定后续的工序节点。

#### 2.3.18.2 返工流程
**返工的业务场景**：针对已经下线入库的产品，由于销售需求变更或研发的 ECN 变更，则产品需要出库转移到返修区，根据工艺选择返工方案，创建返工工单进行返工。

[图 2.3-32 返工流程]

**流程说明**：
*   **需求提出**：销售提出返工需求、研发提出 ECN、工艺提出返工需求等；
*   **质量负责技术通知书审核**；
*   **PMC 创建返修工单**；
*   **返工方案确定**：由生产工艺确认返工方案，并且出具技术通知书，返工技术通知书需要上传到系统，工艺需要再系统中选择返工的工艺路线；
*   **成品仓库根据返工单进行备料**；
*   **车间在返工区**，操作人员扫描产品 SN 条码，获取该产品返工技术通知书，进行返工处理；
*   **车间在返工时**，根据实际返工方案，需要录入更新追溯数据、记录返工负责人、返工工时等，完成后，扫码更新处理状态；
*   **返工返修的追溯数据**，要跟 SN 绑定，能够关联到最初生产过程中的追溯数据；
*   如果 SN 号发生了变更，要能够根据变更后的 SN 号追溯到变更前的 SN 号，并且能够追溯变更前的 SN 号对应产品的追溯数据。
*   车间根据返工方案安排人员、工具及物料，必要时调整工艺参数。
*   返工完成后，经过下线检验，进入完工入库流程。

### 2.3.19 下线报工作业流程
**业务场景**：产品完成最终工序，在这个工位，操作人员扫码识别工位，扫 SN 识别产品，确认是否报工；确认报工，则发送报式数据到 SAP 系统；若发送提示失败，在 MES 系统进行人工处理。

[图 2.3-33 下线报工作业流程]

**流程说明**：
*   包装下线后，根据包装下线的动作，由系统自动触发在生产管理系统中创建入库单及入库明细。
*   生产管理系统把入库单及入库明细发给成品库。
*   成品库仓储管理模块接收到入库单和入库明细，跟实物进行核对，并完成后续的入库流程。
*   车间在生产管理模块发起报工，由生产管理模块后台发送给 SAP 进行自动报工，仓库入库时需要同时校验 SAP 报工是否完成，需要在 SAP 系统增加这个校验，防止没有报工的工单的成品入库。

### 2.3.20 人员资质管理流程
**业务场景**：为确保员工能力与岗位要求匹配，新员工入职需按标准进行培训，在上岗后进行动态监督。

[图 2.3-34 人员资质管理流程]

**流程说明**：
*   人力资源牵头，制订岗位任职要求，考核标准，管理层审核后发布。
*   车间培训工程师负责培训：新员工入职，需经基本的入职培训与安全培训；培训结束后进行考核，考核通过，再进行生产操作上岗培训；生产操作培训结束，分别进行理论考核与实操考核。两项任意一项考核不通过，都要重新培训再考核。
*   车间文员负责上岗证的颁发：生产理论与实操考核都通过后，颁发上岗证，上岗证录入到生产管理模块，后续操作工在生产管理模块上操作时，需要校验操作工是否具备上岗资质。
*   班组长、质量负责确认调岗：上岗期间进行动态监督，工作多次异常，要暂停上岗并重新考核实操；停岗考核通过后，复岗，不通过则调岗或淘汰。
*   员工培训、考核结果录入员工档案；通知员工考核结果。
*   员工上岗证，可以扫码查看岗位信息、有效期。

### 2.3.21 考勤管理流程
**业务场景**：目前车间人员的考勤数据统计以及加班申报占用车间文员大量的时间，需要通过信息化的手段提升考勤管理的效率。

[图 2.3-35 考勤管理流程]

**流程说明**：
*   由人事搭建统一的考勤系统，员工在考勤机上进行打卡，考勤的数据可以提供给车间进行导出，车间导出考勤数据后，每个月进行考勤数据的确认。
*   如果发生漏打卡的情况，需要在 OA 系统提报补卡申请，需要经过车间负责人和人事审批。
*   员工在 OA 系统提报加班申请。加班申请需要经过车间负责人和人事审批。
*   员工考勤的数据、补卡的考勤数据、加班的数据需要在人事系统进行统一汇总，进行绩效或薪酬的处理。

## 2.4 设备&能源模块

设备与能源模块的总体业务流程框架分为标准规范管理、设备到货安装、设备运行监控、设备维修与保养以及闲置设备处置 5 大部分，15 个三级流程。总体业务流程框架如下：

`[图 2.4-1 设备与能源流程]`
**流程框架描述:**
该框架图展示了设备与能源管理的五大核心模块：
- **4.1 标准规范管理:** 包括技术标准规范管理(4.1.1)、设备主数据管理(4.1.2)。
- **4.2 设备到货安装:** 包括设备到货确认(4.2.1)、设备安装验收(4.2.2)。
- **4.3 运行状态监控:** 包括设备运行监控(4.3.1)、能源管理(4.3.2)、设备台账管理(4.3.3)、报废管理(4.4.4)、共同闭环(4.4.5)。
- **4.4 设备维修与保养:** 包括设备点巡检(4.4.1)、设备保养(4.4.2)、设备故障维修(4.4.3)。
- **4.5 闲置设备处置:** 包括闲置设备评估(4.5.1)、闲置设备处理(4.5.2)、闲置设备管理(4.5.3)。

### 2.4.1 技术标准规范管理流程

技术标准规范管理流程是设备管理的基础流程，主要包括标准编制、标准审核、标准发布和标准归档四个部分，实现了设备技术标准的规范化管理。
当发生采购新设备、技术标准变化或外审等情况时，需要启动本流程。本流程涉及到设备工程师、设备部负责人、评审小组以及行政部四个角色。

`[图 2.4-2 技术标准规范管理流程]`
**流程描述:**
- **角色:** 设备工程师, 设备部负责人, 评审小组, 行政部
- **流程:** `设备工程师`根据 `生产需求/法律法规` 等 `1.编制/修订标准文件`，提交 `设备部负责人` `2.审核`。审核通过后，交由 `评审小组` `3.评审`。评审通过后，由 `行政部` `4.发布` 和 `5.归档(SOP维护)`。

首先，由设备工程师根据法律法规、生产技术性要求文件、设备厂商技术文件的要求，编制设备或标准技术文件，编制完成后在 BPM 系统里发起审批，由设备部负责人审批、评审小组审批，审批通过后，由行政部通过 BPM 系统进行标准发布和标准归档工作。在标准发布后，设备工程师根据已发布的标准在设备管理系统里维护相关 SOP 作业标准，以支持设备点巡检、仪器校验提醒、设备保养及设备维修等场景要求。

**表 2.4-1 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **编制技术文件** | 对于涉及到保养、维修的关键技术要求，要进行结构化、条目化处理，便于后续维护到系统里。 |
| **标准发布** | 不仅仅是文件的发布，还要与 PMS 系统集成，实现 SOP 要求的维护。SOP 包括设备工艺、设备维修保养要求等内容。 |

**本流程改进点：**
*   **组织上，** 建议成立临时的方案评审小组，可以包括质量、EHS、测试、工艺、研发等内部相关部门及部分外部专家，对技术标准规范进行专业的评审。
*   **流程上，** 把涉及到设备运行、维护和维修的相关量化标准对接到设备维护与维修流程，实现标准和流程的对接，并通过系统实现量化标准的线上维护。

### 2.4.2 主数据管理流程

主数据管理流程是设备与能源模块数字化建设的基础流程，主要包括主数据申请流程和主数据变更流程，涉及到的主数据范围包括设备分类、设备编号、功能位置以及设备人员等。
设备主数据作为基础流程之一，要保障在正泰电源所有业务系统中具有唯一性。因此，主数据只会在系统初始化、设备新增及淘汰等变化时发生变化。

`[图 2.4-3 主数据管理流程]`
**流程描述:**
- **角色:** 设备部规划/数智化工程师, 设备部负责人, 设备部主数据岗
- **流程:** `工程师`根据 `设备新增/淘汰` 触发 `1.设备主数据申请`，提交 `负责人` `2.审批`，通过后由 `主数据岗` `3.确认` 并 `4.主数据维护`。

设备主数据由设备部数智化设备工程师收集，并在 BPM 系统提起申请，由设备工程师对主数据内容进行确认，经设备部负责人、主数据管理岗审批，审批通过后主数据管理岗在设备管理系统中维护。

**表 2.4-2 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **主数据维护** | 系统根据主数据的编码规则，自动编码。 |

**本流程改进点：**
*   **组织上，** 设备部设置数智化方向的设备工程师岗位，统一负责设备部相关主数据的收集和整理工作。设置主数据管理岗，负责对各个业务部门提交的主数据进行统一的审批和赋码工作。
*   **流程上，** 本流程为新增流程，实现了正泰电源主数据的统一规范管理，为数字工厂的建设夯实基础。

### 2.4.3 设备到货确认流程

设备到货确认流程分为设备预验流程和设备到货流程两部分。
当设备采购合同签订完成，供应商完成生产，具备供货条件时，启动此流程。本流程涉及到供应商、设备工程师、验收小组及采购部采购岗等四个角色。

`[图 2.4-4 设备到货确认流程]`
**流程描述:**
- **角色:** 供应商, 设备工程师, 验收小组, 采购岗
- **流程:** `供应商`发起`1.发起交货流程`。如果需要预验收，`验收小组`进行`3.设备预验收`，然后`供应商` `4.预约送货`。如果不需要，则直接`4.预约送货`。`设备工程师`确认送货后，`供应商`进行`5.设备送货`。最后，`设备工程师`和`采购岗`分别进行`6.到货签收`和`7.到货确认`。

首先，由供应商通过在线微信、电话或邮件联系设备部相关设备工程师确认，并在在线协同工具上发起交货流程。若拟交付设备为合同约定的需要进行预验收设备，则由设备工程师牵头组建验收小组。验收小组根据采购技术文件，对设备进行预验收，形成设备预验单，签字后交给供应商备案。预验收完成后，供应商可以跟设备工程师预约送货时间。若拟交付设备为合同约定的不需要进行预验收设备，则直接预约送货时间即可。

设备工程师根据场地情况及生产计划，确定送货时间后，供应商送货。设备工程师根据采购合同、预验单进行到货签收，同时采购岗在 ERP 系统进行到货确认。到货确认完成后，ERP 系统更新库存信息，设备管理系统更新设备台账信息。

**表 2.4-3 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **发起交货流程** | 根据合同要求，确定是否需要预验收环节，并走不同的业务流程。 |
| **到货确认** | 设备贴码，同时自动更新设备台账信息，与 ERP 系统集成，自动更新库存信息。 |

**本流程改进点：**
*   **组织上，** 增加临时组织设备验收小组，该小组由设备部牵头组建，根据具体设备情况，可涵盖设备、质量、采购等相关部门人员。
*   **流程上，** 当设备到货确认后，增加设备贴码作业，实现通过扫码可查看设备档案信息。
*   **系统上，** 实现了设备台账的实时更新，同时给设备赋予设备编码，便于设备全生命周期管理。另外，建议未来实现供应商协同作业，通过供应商在线协同工具（小程序、APP 等），实现供应商预约线上管理。

### 2.4.4 设备安装验收流程

设备到货确认后进入设备安装验收流程，本流程分为标准设备安装运行流程、非标设备安装运行流程、设备验收流程。
当设备到货后，根据项目几乎要求和合同约定，启动本流程。本流程涉及到设备工程师、供应商、EHS、验收小组和采购岗等五个角色。

`[图 2.4-5 设备安装验收流程]`
**流程描述:**
- **角色:** 设备工程师, 供应商, EHS岗位, 验收小组, 采购岗
- **流程:** `设备工程师`根据`1.工程合同/技术文件`发起`2.安装申请`。非标设备由`供应商`安装，标设备自行安装。安装后进入`4.试运行`阶段。满足时长后，`设备工程师`发起`5.终验申请`，由`验收小组`进行`6.终验`，通过后`采购岗`进行`7.备案`，完成流程。

首先，由设备部设备工程师根据采购合同和技术文件的要求，在设备管理系统中提出安装申请。对于气密机等标准设备，由设备工程师根据设备技术要求自行安装，进入试运行阶段。对于非标准设备，由供应商设备工程师在设备管理系统里提出设备安装作业申请，设备部审批后，由 EHS 审核，审核通过后供应商进行现场安装后，进入试运行阶段。

当设备试运行时长满足标准设备 1 个月，非标设备 1~3 个月时，可以进入终验环节。设备终验由设备工程师在设备管理系统里发起终验申请，验收小组进行终验。终验完成后，在采购部备案，同时实时更新设备台账。

**表 2.4-4 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **安装作业申请** | 需要进行安全培训、签署相关协议。 |
| **试运行** | 标准设备正常试运行满 1 个月，非标设备根据设备情况，需要 1~3 个月，设备正常情况下，才能发起终验。 |

**本流程改进点：**
*   **组织上，** 增加临时组织设备验收小组，该小组由设备部牵头组建，根据具体设备情况，可涵盖设备、质量、采购等相关部门人员。
*   **流程上，** 补充了设备验收流程，增加供应商协同相关作业内容，实现供应商与正泰电源业务流程的衔接。
*   **系统上，** 实现了本流程业务全流程线上管理，实时更新设备台账信息，包括设备位置、人员、状态等。

### 2.4.5 设备运行监控流程

设备运行监控流程是设备管理的关键流程，涵盖设备运行监控指标维护流程、设备运行数据采集流程和设备运行告警管理流程三部分，实现对设备运行管理的精细化。
当设备安装验收完成后，需要启用此流程，实时采集关键设备的运行数据，对数据进行分析监控，及时发现并解决潜在问题。该流程涉及到设备工程师、车间和仓库的现场操作岗位、以及生产部门的领导岗位。

`[图 2.4-6 设备运行监控流程]`
**流程描述:**
- **角色:** 设备工程师, 设备权责人, 车间/仓库现场操作岗位
- **流程:** `设备工程师`进行`2.监控指标维护`，`现场操作岗位`进行`3.设备运行数据采集`，系统自动判断是否`4.超值告警`。若告警，则`设备权责人`进行`5.处理`，并由`设备工程师`进行`6.处理过程备案`。

首先，由设备工程师根据技术标准规范管理文件的要求，在设备管理系统里维护设备的监控指标。当设备运行时，在车间/仓库现场对核心设备进行自动数据采集，并对设备运行数据进行分析。结合系统维护的设备监控指标，若出现数据超值，进行告警提示，同时通知设备权责人进行处理，并对处理过程进行备案。若需要进入设备保养与维护流程，对设备进行保养维护。同时，设备管理系统对设备综合效率（OEE）分析，供生产部门领导查看。在此基础上，对设备监控指标进行优化，并在设备管理系统中对设备指标进行更新。

**表 2.4-5 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **监控指标维护** | 根据设备技术规范的要求，将对应指标及量化标准维护到系统里。 |
| **超值告警** | 系统根据相关指标的量化标准，自动发出告警信息。 |
| **通知权责人** | 通过短信、电话等形式通知权责人处理。 |
| **处理过程备案** | 记录处理过程，作为知识库内容备案。 |

**本流程改进点：**
*   **流程上，** 本流程为新增流程，链接并贯通了技术标准规范流程和设备监控流程，对于超值警告，则触发设备保养维护流程，实现设备运行监控管理的全流程管理。
*   **系统上，** 实现核心设备运行数据的实时采集，并在线维护监控设备的指标要求。实现数据和标准的实时联动，使得管理人员和操作人员对设备的运行情况更加了解。

### 2.4.6 能源管理流程

能源管理流程的主要监控范围是各办公、生产与仓储区域，包括 PACK 产线、PCBA 生产车间、逆变器车间、包装车间、试产车间及电子仓、原料仓、成品仓等。通过安装独立电表、设备技改等信息，实现电、水、气、热等能耗数据的精准采集与分析。该流程包含能源计划和目标设定流程、能源数据监测流程、能源执行绩效管理流程和能源管理改进流程。

`[图 2.4-7 能源管理流程]`
**流程描述:**
- **角色:** 车间/仓库现场操作人员, 能源管理工程师, 生产领导岗位
- **流程:** `能源管理工程师`进行`1.能源计划及目标设定`，`现场操作人员`进行`2.能耗数据采集`，工程师进行`3.能耗数据模型分析`和`4.实时监控`，并对`5.能源绩效进行考核`。

**表 2.4-6 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **能源计划和目标设定** | 针对不同的对象，设置不同的能耗管理目标。能源基准的制定参考一下内容：<br>1)能源的消耗总量、产品产量及单位产品产值能耗<br>2)年度各生产车间能耗耗量、产品产量、产品能源单耗及单位产品综合能耗 |
| **场景技术改造** | 关键设备和区域加装智能电表、智能照明系统、关键设备技改、工艺改进。 |
| **能源数据模型** | 根据实际需求，建设单位产品能耗、人均能耗指标、异常负荷等模型。 |
| **实时监控** | 通过仪表盘追踪能耗数据，设置阈值警报。 |
| **能耗分析** | 通过数据和模型，识别高耗能设备、工艺及能源浪费点。 |
| **能源绩效考核** | 跟踪分析能耗变化，评估考核相应的人或部门。 |

**本流程改进点：**
*   **组织上，** 建议新增能源管理中心，下设能源管理工程师岗位，管理正泰电源的综合能源。
*   **流程上，** 本流程为新增流程，实现从能源目标制定、数据采集、能耗平衡、能耗警告及能源分析的闭环管理。
*   **系统上，** 建设能源管理系统，实现对能耗分区域（PACK 产线、PCBA 生产车间、逆变器车间、包装车间、试产车间及电子仓、原料仓、成品仓）安装）、分类（总耗电、照明、生产、损耗）精准化管理。

### 2.4.7 设备台账管理

设备台账管理流程是对正泰电源设备清单及其情况进行管理的基础流程，主要管理的设备信息包括设备类型、名称、型号、厂家、安装单位、安装时间、安装位置、维护日期、维护人员、维护内容、保养频率等。

`[图 2.4-8 设备台账管理]`
**流程描述:**
- **角色:** 设备部规划/数智化工程师
- **流程:** 基于`设备安装验收/技改/点巡检/闲置处置`等流程，触发`1.设备台账信息更新申请`，经`2.设备台账信息确认`后，完成`3.设备台账更新`。

设备台账管理主要由设备部规划工程师管理。当设备安装验收、设备技改、设备点巡检、闲置设备处置时均会触发更新设备信息和设备台账确认。

**本流程改进点：**
*   **系统上，** 实现了设备台账根据设备管理的业务变动情况自动更新。

### 2.4.8 设备点巡检流程

设备点巡检主要是根据技术标准规范要求，对设备的外观、按钮、指示灯、功能方面进行检查。主要包括设备日常点检流程和点巡检结果校验流程。
设备点巡检流程由设备工程师根据设备类型和使用情况，制定巡检计划并自动发起点巡检作业。该流程涉及到车间技术员、设备工程师和行政岗位三个角色。

`[图 2.4-9 设备点巡检流程]`
**流程描述:**
- **角色:** 车间技术员, 设备工程师, 行政岗
- **流程:** `设备工程师`制定`2.设备点检计划`，`车间技术员`执行`3.设备点检`并`4.点检结果上传`。`设备工程师`进行`5.点检结果确认`，最后由`行政岗``6.点检结果归档`。

**表 2.4-7 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **设备点检计划** | 需要根据技术标准规范，制定明确点检项目和时间的点检计划。 |
| **点检结果上传** | 设备点检完成后，需要上传点检对象照片或视频。 |
| **点检结果确认** | 设备工程师需要对设备点检结果确认，完成本流程。 |

**本流程改进点：**
*   **流程上，** 新增了点巡检结果校验流程，实现了点检作业全流程管理，避免了设备漏检、忘检现象。
*   **系统上，** 通过实现点检提醒和结果上传功能，实现了对设备点检过程的精细化管理。

### 2.4.9 设备保养流程

正泰电源的设备保养按照设备类型，分为三级体系：
*   **三级保养：** 主要包含装配流水线、包装流水线、自动点胶机、手动单板测试、手动老化房，由产线人员每日执行，侧重设备清洁、润滑及基础检查，确保生产连续性；
*   **二级保养：** 一个月一次，保养设备主要包含整机测试设备、FCT 测试设备、变压器、负载、恒温恒湿箱等比较重要的生产设备以设备部门为核心，产线人员配合，进行周期性部件检修与性能调试；
*   **一级保养：** 一年一次，保养设备主要包含悬臂吊、伺服压力机、直流源、涂覆机、自动涂硅脂机、自动螺丝机、6 轴机器人空压机、自动气密机、自动老化房等对生产影响大的自动化设备及对安全有重要影响的设备，由设备厂家主导，实施深度技术维护与关键部件更换，保障设备长期稳定运行。

`[图 2.4-10 设备保养流程]`
**流程描述:**
- **角色:** 设备部设备工程师, 供应商设备保养工程师, 产线技术员
- **流程:** `设备工程师`根据`技术标准规范`制定`2.保养计划`，系统根据计划自动提醒`产线技术员`或`供应商`进行`3.设备保养`，完成后由`设备工程师`进行`4.保养确认`。

**表 2.4-8 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **制定保养计划** | 需要根据技术标准规范，制定设备的保养内容和时间计划。 |
| **设备保养提醒** | 根据设备类型，确定保养级别，走不同的保养流程。 |
| **设备保养** | 保养完成后，保养人（产线、设备工程师或供应商）需要上传设备保养的照片或视频。 |
| **保养确认** | 需要进行设备保养确认，完成本流程。 |

**本流程改进点：**
*   **流程上，** 打通了技术标准、设备点巡检流程、设备保养计划、设备保养过程等各个流程，实现了设备保养的全流程管理，能有效的避免出现遗漏现象。
*   **系统上，** 基于设备技术标准，将设备保养 SOP 固化到系统里，明确保养规范、责任人和保养结果，提升设备保养质量。基于采集到的数据，实现设备预防性保养和维修，减少对工程师个人经验的依赖。

### 2.4.10 设备故障维修流程

正泰电源的设备维修主要分为关键元器件更换、备品备件更换以及工器具维修三种类型。关键元器件更换主要以供应商维修为主，设备部为辅；备品备件更换由设备部自行更换；工具维修则由供应商集中维修。因此，设备故障维修流程包含维修申请流程、关键元器件更换流程、备品备件更换流程、工器具维修流程、维修结果确认流程等。

`[图 2.4-11 设备故障维修流程]`
**流程描述:**
- **角色:** 产线技术员, 设备工程师, 质量岗, 供应商设备维修工程师
- **流程:** `产线技术员`发起`2.设备维修申请`。`设备工程师`进行`3.初步评估`，根据维修类型走不同流程：备品备件更换、工器具维修、关键元器件更换。完成后进行`8.维修结果确认`。

**表 2.4-9 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **设备维修申请** | 设备维修可以直接发起，也可以通过设备点巡检或设备保养流程中发起。根据设备类型，确定不同的设备维修流程。 |
| **备品备件领料** | 生成备品备件领料单，对接到 WMS 系统领料出库流程。 |
| **初步评估** | 对于关键元器件维修，需要设备工程师初步评估方案和价格，以便支持采购部门采购。 |
| **供应商维修** | 供应商维修的前提条件是完成采购流程。 |
| **维修结果确认** | 若本次设备维修需要质量部确认维修结果，则由设备部加签给质量部工程师。同时更新设备档案信息。 |

**本流程改进点：**
*   **流程上，** 新增了维修申请和维修结果确认流程，增加了设备工程师初步评估节点，以更好的支撑采购流程和更合理的确定最终维修方案。在维修结果确认环节，增加了质量部确认，是可选项。
*   **系统上，** 增加设备维修和故障诊断知识库，支撑设备工程师更好的评估和维修设备。同时，详细记录每次设备维修信息，包括维修时长、解决方案等，设备工程师可以通过系统查阅。

### 2.4.11 技改管理流程

设备的性能和功能改造提升是设备技改的核心内容，也是正泰电源生产质量和运营效率的重要保障。技改管理流程主要包括技改项目立项流程、技改项目执行流程和技改项目验收流程。
当市场需求对企业生产和运营管理提出更高的要求时，部分设备可能逐渐暴露出性能不足的问题，需要执行设备技改管理流程。该流程涉及到设备工程师/规划工程师、预算执行工程师、供应商、采购岗位和财务部等多个角色。

`[图 2.4-12 技改管理流程]`
**流程描述:**
- **角色:** 设备规划工程师, 设备工程师, 供应商, 采购岗, 预算执行工程师, 财务部
- **流程:** `设备规划工程师`提出`2.技改需求`，`供应商`提供`3.技改方案`，经过`4.方案评审`、`5.预算预估`和`6.预算核验`后，进行`7.项目立项`。立项后`采购岗`执行`8.采购`，`供应商`进行`9.设备技改`，技改后`设备工程师`进行`10.收货确认`、`11.试运行`和`12.验收申请`。最后进行`13.验收审批`、`14.成本归集`和`15.资产更新`。

**表 2.4-10 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **技改方案评审** | 技改方案是技改项目预算和立项的基础，通过后才能进行项目立项。 |
| **项目预算核验** | 核验技改项目是否在预算范围内，若在进入立项流程。若不在，需要更高级别的项目立项审批流程。 |
| **设备技改作业** | 设备技改作业的前提是完成采购流程。 |
| **试运行** | 标准设备试运行满 1 个月，非标设备根据设备情况，需要 1~3 个月，设备正常情况下，才能发起验收申请。 |
| **设备台账更新** | 根据技改项目验收情况，与 SAP 集成，更新财务资产信息。 |

**本流程改进点：**
*   **流程上，** 增加了方案评审、项目立项和预算校验等环节/子流程，拉通了需求与方案、预算与成本、立项和验收等业务，实现了全流程的线上管理和技改关键环节的精细化管理。
*   **系统上，** 通过建设数字化系统，实现预算的实时校验、技改成本自动归集和设备台账的自动更新。

### 2.4.12 预算执行管理流程

当进行设备维修、备品备件、生产辅料、劳保用品等 MRO 时，需要校验部门的预算执行情况。该流程包括预算执行情况申请流程和预算执行情况分析流程两部分，涉及到设备部预算执行工程师和财务部成本核算岗。

`[图 2.4-13 预算执行管理流程]`
**流程描述:**
- **角色:** 设备部预算执行工程师, 财务部成本核算岗
- **流程:** `工程师`根据`1.MRO采购需求`发起`2.预算执行情况申请`，`财务部`反馈`3.预算执行情况`，工程师`4.查看预算执行情况`。

**本流程改进点：**
*   **流程上，** 融合了 MRO 相关流程与预算执行流程，实现预算管理和数据分析的实时性。
*   **系统上，** 打通了设备管理系统和 SAP 系统，实现了成本费用数据的实时查询和归集。

### 2.4.13 备品备件管理流程

备品备件管理流程主要是管理正泰电源生产使用备品备件，包含采购与领用流程、出入库管理流程以及库存盘点流程。
根据正泰电源实际情况，建议设置备品备件库，其归口管理部门依然在设备部，但使用专业的仓储管理系统管理。

`[图 2.4-14 备品备件管理流程]`
**流程描述:**
- **角色:** 设备工程师, 备品备件仓管理员
- **流程:** `设备工程师`根据`1.设备维修/MRO需求`发起`2.采购/领用`申请。采购到货后，由`管理员`进行`4.入库管理`。领用时，由`管理员`进行`5.出库管理`。同时定期进行`6.库存盘点`。

**本流程改进点：**
*   **组织上，** 设置专门的仓库管理员岗位，同时设置专门的独立区域管理备品备件；
*   **系统上，** 使用仓储管理系统统一管理备品备件，与设备管理系统集成，形成统一的备品备件管理业务。

### 2.4.14 闲置设备评估流程

闲置设备评估流程主要包括闲置设备认定申请流程和闲置设备技术评估流程，实现对闲置设备的类型认定和技术评估，从而更合理的支持闲置设备处置。

`[图 2.4-15 闲置设备评估流程]`
**流程描述:**
- **角色:** 车间技术员/设备工程师, 设备部规划工程师/设备工程师, 设备部领导, 财务部资产管理岗
- **流程:** `技术员/工程师`发起`2.闲置设备评估申请`，`规划工程师`进行`3.技术评估`，并给出`4.处置建议`。根据建议，`设备部领导`确认后，`财务部`进行`6.资产价值评估`，最后`规划工程师``7.确认处置结果`。

**表 2.4-11 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **闲置设备处置建议** | 根据闲置设备评估内容，给出封存、售卖或报废等不同的处置建议，走不同的处置流程。 |
| **资产价值更新** | 与 SAP 系统集成，针对不同的处置建议，更新相应的资产状态及价值。 |

**本流程改进点：**
*   **流程上，** 新增了闲置设备评估流程，覆盖了闲置设备认定确认和技术评估。同时，联通了财务部与设备部关于资产价值评估，整个流程更加合理。
*   **系统上，** 新增设备管理系统，并于 BPM 系统、SAP 系统集成，实现了设备和资产的集成、统一管理。

### 2.4.15 闲置设备处置流程

闲置设备评估流程主要包括闲置设备封存管理流程、闲置设备转卖流程、闲置设备对外交易流程和设备报废处理流程。
当完成闲置设备评估流程后，启动该流程。该流程涉及设备部规划工程师/设备工程师、采购部、行政部和财务部资产管理岗等多个岗位。

`[图 2.4-16 闲置设备处置流程]`
**流程描述:**
- **角色:** 设备部规划工程师/设备工程师, 采购部, 行政部, 财务部资产管理岗
- **流程:** `工程师`发起`2.处置申请`，根据处置类型（封存/交易/报废），走不同审批流程，最终由`财务部`进行`7.资产调整`。

**表 2.4-12 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **闲置设备处置申请** | 根据闲置设备处置类型，进行封存、交易或报废等不同处置方式。 |
| **拟定合同** | 若是对外交易，则需要采购部确定买家并拟定合同，若是集团内部交易，则走集团内部资产调拨，跨法人时，也需要拟定合同。 |
| **资产调整** | 资产报废、交易都涉及到资产归属变化，需要在 SAP 系统中进行资产更新。 |

**本流程改进点：**
*   **流程上，** 新增了设备封存的流程化管理，细化了设备交易处理流程。
*   **系统上，** 新增设备管理系统，并于 BPM 系统、SAP 系统集成，实现了资产在线调整和设备台账的自动更新。
好的，这是您指定的 **2.5 质量模块** 下属章节的完整Markdown格式内容。

---

## 2.5 质量模块

通过对业务流程现状诊断可知，效率、协同、追溯等具体问题，可从应用层面，通过上自动化检测设备、信息流及业务模块集成打通等方式，解决自动化、智能化、数字化的问题，相应信息流业务模块及产品，均有成熟解决方案可以直接进行应用。

但在实际工作过程中，应用工具的先进与否，虽对质量管理起到很大的作用，也决定了质量管理的精细化和专业程度，但更多的则需要依靠质量管理体系决定质量管理水平的高低，因此，就需要结合质量管理体系对业务场景进行详细梳理，以助正泰电源建立起可落地的质量管理流程体系。

基于跟正泰电源领导层进行访谈及资料汇总分析可知，当前正泰电源三年规划目标指标参数规划如下所示：

`[图 2.5-1 正泰电源三年规划目标指标]`
**指标图描述:**
该图展示了从现状到三年后目标的四个关键维度：
- **效率 (直通率达到99%)**: 从`流程断点、信息不通畅`和`管理松散、计划不准`的现状，通过`数据智能和流程、尊重物理设备运行特性`以及`以数据为核心，将业务串联起来`的思路，达到`选用于系列化，可复制的方案，质量控制，物控区域`和`提高MTO业务比例，重构计划逻辑`的目标。
- **协同 (成品仓库存周转<30)**: 从`成品仓库存高居不下`和`营销预测偏差、客诉高`的现状，通过`倒逼营销和流程、尊重物理设备运行特性`和`以数据为核心，将业务串联起来`的思路，达到`实施闭环功能和营销组织业务状态数据`和`数据智能和流程、尊重物理设备运行特性`的目标。

而且当前正泰电源对于质量体系的建设规划是逐步从 ISO90001 向更严格的 IATF16949 质量管理体系标准进行转型，而 IATF16949 质量管理体系流程框架如下所示：

`[图 2.5-2 质量管理体系流程]`
**流程图描述:**
这是一个典型的PDCA（策划-实施-检查-改进）循环的质量管理体系流程图。
- **输入 (客户要求):** 客户要求和需求
- **核心流程:**
    - `8.1 机构和组织管理` -> `8.2 制造过程` -> `8.3 产品和服务测量` -> `8.4 产品交付` -> `8.5 产品服务`
- **支持流程:**
    - `5.4 创业工业管理` -> `5.5 制造管理` -> `5.6 产品监管和质量管理` -> `5.7 产品追溯和测量`
- **基础支撑:**
    - `文件记录管理` -> `人力资源管理` -> `管理策划` -> `内部审核` -> `纠正预防和持续改进`
- **输出 (客户满意):** 客户满意

即：质量管理部门应从客户需求即开始介入到产品质量策划到最终产品服务的全生命周期，整体目标就是确保客户需求被满足，协同市场、销售团队，让客户对我们的产品服务达到满意的程度。此体系很好的解决了当前正泰电源质量管理部门战略目标定位的问题。

正泰电源质量管理部门，对于质量管理也有初步规划，相应的规划蓝图如图所示：

`[图 2.5-3 数字化规划蓝图]`
**蓝图描述:**
该蓝图展示了2024年通过IT驱动的质量管理数字化(QMS)平台建设蓝图。
- **顶层:** 运营管理层
- **核心平台:** 数据驱动的智能制造平台
- **业务层:**
    - **整合协作:** 供应商协同(JQE&SPLM), 过程检验, 制造协同(MES), 客户协同(CRM)
    - **质量管理:** 覆盖从供应商管理到客户服务的全流程质量管控。
    - **数据共享:** 产品生命周期, 质量分析数据, 质量计划数据, 质量过程数据, 产业智能数据库
- **IT基础层:** 移动应用, 数据平台, 开发平台, 集成平台, 安全服务

规划的核心功能清单如下所示：

`[图 2.5-4 质量数字化系统规划核心功能]`
**功能清单描述:**
- **新品质量保证(3):** 新品项目质量管理, 质量知识库, 工具管理/DFMEA
- **采购质量保证(7):** 供应商现场审核, 新零件验证, 进货检验, 零部件在线不良, 供应商周期物料, 供应商4M变更管理
- **制程质量保证(7):** 生成要素管控, 过程检验, 成品检验, 质控点管理, 不合格品处置, 质量追溯
- **交付质量保证(4):** 客户投诉, 质量退货, 市场监督, OEM/ODM产品交互
- **质量检测(5):** 理化实验, 可靠性实验, 测量设备管理, 测量过程管理, 分布式实验室(共享)

因此，结合业务现状诊断，基于正泰电源质量管理流程现状进行解析和完善，正泰电源质量管理整体业务处理架构流程如下所示：

`[图 2.5-5 质量管理整体业务处理架构流程]`
**架构图描述:**
该架构图以“质量管理体系要求”为顶层输入，以“客户满意”为最终输出，展示了质量管理的核心业务流程。
- **管理层:** 企业发展方针/计划, 公司质量方针, 公司质量目标, 部门质量目标
- **核心流程:**
    - **质量策划:** 人力资源管理过程, 设施和环境控制过程
    - **产品实现:** 产品设计开发过程, 过程设计开发过程, 试生产和补充生产评审过程, 生产件批准过程
    - **测量、分析和改进:** 质量数据分析和改进过程, 内部审核过程, 纠正/预防措施管理过程, 顾客满意度评价和测量过程
- **基础流程:** 生产计划控制过程, 生产和服务控制过程, 文件资料控制过程, 供应商管理控制过程, 监视和测量装置控制过程, 不合格品控制过程
- **输入/输出:** 输入, 输出, 产品

以此流程架构作为指导，结合正泰电源当前已有功能流程体系，梳理出正泰电源质量管理整体蓝图规划如下所示：

`[图 2.5-6 质量管理业务规划]`
**规划图描述:**
这是一个以客户价值为导向的质量管理体系规划图。
- **顶层目标:** 以客户价值为导向的质量管理体系
- **战略愿景:** QEHS方针: 创世界名牌，铸地方精品，为国内外用户提供安全、节能、安全、高效的核电和配套设备
- **核心业务:**
    - **质量策划:** 质量目标, 质量成本, 体系运行, 质量规划
    - **质量保证:** 风险管理, 资源保证, 内部审核, 供应商管理, 内部稽核
    - **质量控制:** 进料检验, 过程检验, 出货检验, 客户服务
- **产品质量:** 覆盖从设计开发到售后服务的全生命周期。
- **基础支撑:** 模块分析, 缺失, 持续强化, 成熟度高

### 2.5.1 质量管理指标

根据上述业务流可知，质量管理相应的指标包含以下七部分内容：
`[图 质量指标体系]`
**质量指标体系:**
- 质量成本
- 产品质量
- 来料质量
- 制程质量
- 交付质量
- 质量体系

`[图 2.5-39 质量指标体系]`
**表 2.5-1 质量管理指标**
| 序号 | 类型 | 指标名称 | 采集数据 | 数据来源 | 计算公式 | 备注 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| 1 | 产品质量 | 产品质量平均等级 | 1.优等品产量<br>2.一等品产量<br>3.合格品产量 | 系统自动采集+人工抽检<br>产量来源 MES<br>质量等级来源 QMS | 产品质量平=∑(产品等级×该等级产量)/总产量 | 设定等级系数：通常将最优等级（如优等品）系数设为 1，次优等级逐级递减（如一等品 0.8，合格品 0.6） |
| | | 产品抽检合格品率 | 1.合格品数量<br>2.总样本数量<br>3.统计维度 | 系统自动采集<br>数量来源于 MES | 合格品率=(合格品数量/总样本数量)×100% | |
| | | 不合格品率 | 1.不合格品数量<br>2.总样本数量<br>3.统计维度 | 系统自动采集<br>数量来源于 MES | 不合格品率=(不合格品数量/总样本数量)×100% | |
| | | 退货率 | 1.退货数量<br>2.销售总量<br>3.统计维度 | 系统自动采集<br>数量来源于 SAP | 退货率=(退货商品数量/销售总数量)×100% | |
| | | 召回率 | 1.召回数量<br>2.销售总量<br>3.统计维度 | 系统自动采集<br>数量来源于 SAP+CRM | 召回率=(召回商品数量/销售总数量)×100% | |
| | | 保修期内维修率 | 1.保内维修量<br>2.保内产品保有量<br>3.统计维度 | 系统自动采集<br>数量来源于 SAP+CRM | 保内维修率=(保内维修量/保修期内产品保有量)×100% | |
| 2 | 来料质量 | 来料良率 | 1.合格来料数量<br>2.总来料数量<br>3.统计维度 | 系统自动采集<br>数量来源于 WMS+QMS | 来料良率=(合格来料数量/总来料数量)×100% | |
| | | 来料直通率 | 1.合格来料投入生产总量<br>2.合格来料一次性通过生产总数量<br>3.来料产生问题返工次数<br>4.统计维度 | 系统自动采集<br>数量来源于 MES+QMS | 直通率=(一次性通过生产的合格来料数量/投入生产的合格来料总量)×100% | |
| | | 批次合格率 | 1.批次内合格来料数量<br>2.批次内总来料数量<br>3.统计维度 | 系统自动采集<br>数量来源 QMS | 批次合格率=(批次合格数量/批次总数量)×100% | |
| | | 来料不良率 | 1.不合格来料数量<br>2.总来料数量<br>3.不良分类数量<br>4.统计维度 | 系统自动采集<br>数量来源 QMS | 来料不良率=(不合格来料数量/总来料数量)×100% | |
| 3 | 过程质量 | 过程能力指数 | 1.Cp<br>2.Cpk<br>3.统计维度 | 系统自动采集<br>数量来源 QMS | | 优化生产流程，减少波动和偏移。工具：需结合控制图（如 Xbar-R 图）验证过程稳定性 |
| | | 工序合格率 | 1.工序加工总数量<br>2.一次合格数量<br>3.统计维度 | 系统自动采集<br>数量来源 MES | 工序合格率=(工序一次合格数量/工序加工总数量)×100% | |
| | | 直通率 | | 系统自动采集<br>数量来源 MES工序合格率 | | 直通率是各工序合格率的乘积，反映产品无缺陷通过全流程的概率；直通率越低，表明中间工序返工越多，工序合格率优化空间越大 |
| | | 产品一次合格率 | 1.投入总量<br>2.返工量<br>3.返修量<br>4.报废量<br>5.统计维度 | 系统自动采集<br>数量来源 MES | 一次合格率=(投入总量−(返工量+返修量+报废量))/投入总量×100% | |
| | | 一次交验合格率 | 1.初次检验合格数<br>2.总检验数量<br>3.统计维度 | 系统自动采集<br>数量来源 QMS | 一次交验合格率=(一次合格品数量/总检验数量)×100% | |
| 4 | 交付质量 | 合同履约率 | 1.技术满足数量<br>2.交付时间<br>3.统计维度 | 系统自动采集<br>数量来源 PDM+SAP+CRM | 合同履约率=(技术满足数量/总需求数量)×100% | |
| | | 交付准时率 | 1.准时交付数<br>2.总交付数<br>3.统计维度 | 系统自动采集<br>数量来源 SAP+CRM | 交付准时率=(按时交货的订单数量/总订单数量)×100% | |
| | | 技术参数达标率 | 1.技术达标数量<br>2.总参数数量<br>3.统计维度 | 系统自动采集<br>数量来源 PDM | 技术参数达标率=(符合技术标准的参数数量/总检测参数数量)×100% | 当产品涉及多个技术参数时，需根据参数重要性或权重调整计算方式 |
| | | 抱怨投诉量 | 1.质量投诉数量<br>2.服务投诉数量<br>3.统计维度 | 系统自动采集<br>数量来源 CRM+CQS | 有一件记录一件 | |
| | | 投诉响应及时率 | 1.抱怨投诉响应时间统计<br>2.响应周期<br>3.统计维度 | 系统自动采集<br>数量来源 CRM+CQS | 投诉响应及时率=(响应周期内抱怨投诉响应数量/总投诉量)×100% | |
| | | 客户满意度 | 1.客户评分分布统计<br>2.统计维度 | 系统自动采集<br>数量来源 CRM+CQS | 满意度指数=∑(评分×人数比例)/最高分值×100% | |
| 5 | 质量体系 | 及时率 | 1.在计划时间节点前完成事项<br>2.计划事项总数<br>3.统计维度 | 系统自动采集<br>数量来源 CRM+CQS+OA+PDM+QMS+SRM | 及时率=(在计划时间节点前完成事项数量/计划事项总数)×100% | 相应维度除时间维度、组织维度外，含顾客满意度调查、管理评审会议决议、合理化建议评审、持续改进、文件发放、不合格来料处理、供应商不符合项整改、不良质量成本分析改进、质量成本统计、采购物料交付 |
| | | 执行率 | 1.按计划执行次数<br>2.计划总次数<br>3.统计维度 | 系统自动采集<br>数量来源 CRM+CQS+OA+PDM+QMS+SRM | 执行率=(按计划时间执行事项数量/计划事项总次数)×100% | 含内部审核计划 |
| | | 按时整改率 | 1.按时整改次数<br>2.计划整改总次数<br>3.统计维度 | 系统自动采集<br>数量来源 CRM+CQS+OA+PDM+QMS+SRM | 按时整改率=(按计划时间整改事项数量/计划整改总次数)×100% | |
| | | 资料完整率 | 1.输入资料数量<br>2.应输入资料数量<br>3.统计维度 | 系统自动采集<br>数量来源 CRM+CQS+OA+PDM+QMS+SRM | 资料完整率=(按计划输入资料数量/应输入资料总数)×100% | |
| | | 准确率 | 1.正确执行次数<br>2.应执行总次数<br>3.统计维度 | 系统自动采集<br>数量来源 CRM+CQS+OA+PDM+QMS+SRM | 正确率=(正确执行次数/应执行总次数)×100% | |
| | | 完成率 | 1.完成事项次数<br>2.应完成事项总次数<br>3.统计维度 | 系统自动采集<br>数量来源 CRM+CQS+OA+PDM+QMS+SRM | 完成率=(完成事项次数/应完成事项总次数)×100% | 相应维度除时间维度、组织维度外，含资料存档、供应商审核、检定计划 |
| 6 | 质量成本 | 预防成本 | 1.质量培训费<br>2.质量管理活动费<br>3.质量改进费用<br>4.质量评审费用<br>5.质量管理人员薪酬福利<br>6.其他费用 | 系统自动采集+人工录入<br>数据来源财务系统 | | |
| | | 鉴定成本 | 1.试验检验费用<br>2.质量检验部门办公费用<br>3.试验、检验设备折旧费用<br>4.检验人员薪酬福利<br>5.其他费用 | 系统自动采集+人工录入<br>数据来源设备管理系统+QMS+财务系统 | | |
| | | 内部损失成本 | 1.废品损失费用<br>2.返工返修费用<br>3.降级损失费用<br>4.停工损失费用<br>5.质量事故处理费用<br>6.其他费用 | 系统自动采集+人工录入<br>数据来源 MES+WMS+财务系统 | | |
| | | 外部损失成本 | 1.索赔费用<br>2.退货损失费用<br>3.折价损失费用<br>4.保修费用<br>5.保修人员薪酬福利费用<br>6.其他费用 | 系统自动采集+人工录入<br>数据来源 CRM+SRM+MES+WMS+财务系统 | | |

### 2.5.2 风险评估与管理控制流程
**业务场景：** 本流程主要是基于质量管理方针和质量管理目标，基于质量管理部的职责和公司授权，由公司领导层对质量体系进行分解，同时初步确定相应风险及规避措施，交由质量管理部门编制相应的质量手册，并编制相应的风险评估表用于对各业务部门的质量风险进行实时监控和汇总，同时对各业务部门的各环节风险识别与风险应对规避措施及执行状态进行监控和数据采集，用于应对各类质量风险的管控和效果评估。
质量部门的工作从质量体系分解开始，到质量风险监控终止。

`[图 2.5-8 风险评估与管理控制流程]`
**流程描述:**
- **输入:** 质量方针和目标, 职责和授权
- **公司领导层:** 质量体系分解 -> 确定风险及规避措施
- **质量部门:** 编制质量手册 -> 编制风险评估表 -> 风险监控
- **业务部门:** 各环节风险识别与评估 -> 制定风险应对及规避措施 -> 制定应急计划 -> 应急预案
- **输出:** 质量管理应急预案

**流程描述：** 针对质量目标，在 QMS 系统中将质量目标进行分解，形成各类质量管理目标，同时针对目标及过程绩效目标进行实时监控，形成质量目标闭环管理。而相应的具体风险识别与评估及制定各类应对措施，则是由各业务部门执行，只需要将相应的应急计划和预案同步给到质量管理部，用于具体的风险实时监控。

**表 2.5-2 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **编制风险评估表** | 风险识别应覆盖人员、设备、方法、环境等核心维度，并根据场景细化子项，设计模块化模板，相应模板按流程阶段（如检测前/中/后）划分风险点，避免遗漏。 |

**本流程改善点：**
*   质量管理部对风险控制，有一套完整的管理流程，但更多偏向线下纸档执行；本流程通过系统化管理，实现风险评估表的在线化管理，避免了遗漏的问题，实现规范化管理；
*   本流程对公司的质量目标进行分解，通过模块化的风险评估表，将公司质量管理目标，分解下发到一线，并通过表单的形式进行呈现，做到可查、可控。
*   **采集指标数据：** 资料完整率、及时率、执行率、准确率、完成率
*   **解决的痛点问题：** 目标管理：月度公司层级目标及过程绩效目标监控，目标趋势图呈现，超标提醒，推送责任人分析改进，行程闭环。
*   **业务流集成：**
    1) 对接集团层面或者管理规划的质量方针和质量目标；
    2) 对接各业务部门的业务流管理模块，同时下发相应的质量管理目标与参数，同时实时获取各业务流模块的质量风险应急计划和应急方案，并将相应执行结果实时同步给 QMS 系统用于质量管理部门进行实时质量数据分析。
*   **产生的文件：** 质量管理应急预案
*   **涉及到的岗位：** 质量体系运行管理

### 2.5.7 来料质检管理流程
**业务场景：** 对供应商来料进行质量检测。相应的业务流开始于仓储管理发出的到货捡货通知，终止于来料合格判定后的供应商 VCAR 报告输出后的资料存档。

`[图 2.5-13 来料质检管理流程]`
**流程描述:**
- **输入:** 供应商的送货单、发货通知，采购的采购单
- **供应商:** 送货
- **仓库:** 物料到货，送至待检区
- **IQC:** 接到通知后，进行供应商等级校验、文件检验、物料检验。合格则盖章放行，不合格则走不合格品处理流程。
- **输出:** 来料检验报告、VCAR报告、质量趋势报告等。

**流程描述：**
1.  **供应商** 按照采购订单发货后，会发出发货通知，在到货前，会由**采购管理模块**对供应商资质进行审核，在审核通过后，会更新系统中的采购单，更新发货单号、来料型号、批次、数量等；若审核不同，在到货前，由采购走内审等相应流程，此无需质量管理部门参与；但相应的处理结果需要同步给质量管理部门进行存档保存；
2.  **采购** 确认没问题后，供应商物料送货到仓储接收点，由**仓储接收人员**对送货单进行确认，确认无误后，做型号、批次、数量、外观的初步检查，确认无误后，由仓储运输到待检区，同时由仓储管理业务模块触发到货通知，同步发送到来料质检任务管理模块中，通知 **IQC** 准备来料质检；
3.  **IQC** 在接到到货通知后，根据供应商信息，自动校验供应商等级，根据供应商等级触发检验规则和检验标准，同时给出相应的检验方法；相应校验及检验规则，同时跟供应商绩效考评、良率和送货周期挂钩，自动调整相应的检验规则；
4.  根据到货单，**QMS** 同时调出所需要校验的各类文件，如元件认证证书、合规性文件等，此可以由客户需求、市场规定、法律法规及研发要求等，提前配置到业务流模块中，业务流根据到货单信息，自动触发生成相应的文件检验任务，杜绝遗漏、过期等问题；
5.  在上述来料到货信息校验完成后，由 **IQC** 使用各类智能化设备，对物料按照规则进行检验，业务流模块自动对物料检验数据进行采集并上传，通过提前配置好的检验标准，业务流模块可以自动判断相应的检验项是否合格；
6.  对于检测到不合格品，则走不合格品处理流程；同时对来料不良造成的质量成本损失进行计算，用于对供应商进行索赔；
7.  对检验合格的来料，按公司的物料分级要求进行分类，并贴相应的合格标签，或者盖章后放行，由仓储进行收料入库操作；
8.  无论合格与否，都需要输出相应的来料质检报告，并给供应商进行绩效评核，用于实时更新后续的供应商来料检验规则；
9.  对于来料数据，生成相应的来料趋势数据分析报告，同时结合对供应商的需求，生成每个供应商的 VCAR 报告，同步发送给供应商，由其进行相应的质量改善。

**表 2.5-7 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **检验标准制定** | 依据设计图纸、BOM（物料清单）、客户签样、法规要求、供应商协议等技术文件锁定关键特性；在设计或工艺调整时，对质检标准的动态管理更新检验标准并培训相关人员； |
| **检验方法制定** | 抽样方案的合理性验证，抽样需覆盖不同位置/批次等，防止批次不均；新供应商首 5 批必须全检；跳批规则配置及自动更新； |
| **检验过程管理** | 四类证据链留存(实物标识\原始数据\图像证据\审核签名)；防错机制设置，如系统强制填写关键字段（如物料编码）方可提交报告；扫码枪自动关联物料信息，减少人工录入错误； |
| **来料不合格品处理** | 不合格物料需标识隔离存放；对返工过程IQC现场监督并重新检验； |
| **供应商质量改善** | 供应商质量指数（SQI）；共享检验工具；联合进行 MSA（测量系统分析）减少检测差异； |

**本流程改善点：**
*   本流程是在当前来料质检管理的基础上，新增质量管控自动化触发协同管理流程，构建来料质检信息化管理流程；
*   以无人化、少人化检验管理方式，重塑来料质检管理体系，减少当前来料质检信息传递周期，以信息化手段解决当前存在的漏检、错检等问题；
*   通过信息化数采手段，实现质检数据的自动采集、自动存储、自动上传、自动判断等功能，大幅提升质检效率，加速来料质检周转率。

### 2.5.8 来料不合格管理流程

**业务场景：** 在 IQC 对供应商来料检出不合格品后，需要对来料不合格品形成完整的处理方案。相应来料不合格品开始于检出不合格品，视为检测异常，终于供应商不合格改善。在此过程中，同步跟采购、仓储、生产进行联动，形成完整的来料不合格品处理流程。

`[图 2.5-14 来料不合格管理流程]`
**流程描述:**
- **输入:** 质检发现异常
- **IQC:** 对缺陷进行分类，严重/主要/批量问题则进入退货管理流程。
- **MRB评审:** 对退货进行评审，决定退货、返工或让步接收。
- **PMC/生产:** 若不合格品已流入生产，则执行停线管理。
- **输出:** 供应商 VCAR 报告，启动改善流程。

**流程描述：**
1) 在质检发现异常后，对缺陷进行分类，缺陷等级：严重、主要、次要；对于严重/主要及批量问题，在标识不合格运输到隔离区后，则走退货管理；需要由物料评审组进行审核后，由采购执行退货操作；如果相应的不良品，已经流入到生产环节，并正在进行生产，则需要立即通知给 PMC 和生产，执行停线管理；
2) 在不合格品的处理过程中，QMS 会同步记录不良的供应商信息、型号、数量等物料信息，同时记录参与质检的人员数量、资质、工时等人工信息；自动输出相应的报告，同时跟 MES 系统、仓储管理系统、财务系统、人力资源系统进行数据传递，在获取到相应物料、人工、场地、环境等单价，用于自动核算出相应的成本，即实现质量成本核算的自动化
3) 在不良品的处理方式中，可以设置处理时效、处理进度跟进、并输出相应的不良品处理报告；
4) 相应的流程及任务触发，都是在系统自动触发，并自动记录相应的不良信息、物料信息等，所有的数据自动更新；

**表 2.5-8 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **不合格标签** | 不合格品的即时识别与明确标识。 |
| **隔离管理** | 物理隔离防止误用，确保在处置前无异常流动。 |
| **来料不合格品记录表** | 详细记录不合格信息：供应商名称、物料编码、不合格描述、检验标准偏差等；生成《进料检验报告》或《不合格品评审单》，同步通知采购、质量、工艺等部门。 |
| **评审决策** | 跨部门评审与处置决策，质量部主导，采购、工艺、生产参与，重大争议时由原材料评审委员会（MRB）评审。 |
| **特采管理** | 仅限不影响功能/安全的不合格项，需客户书面批准（若涉及客供料），并加贴”特采”标识，同时构建全流程追溯体系。 |
| **供应商反馈与改进** | 采购部门向供应商发送《不合格品报告》，要求限期整改并提交纠正措施（如 8D 报告），质量部跟踪供应商整改效果，重复问题触发加严检验或暂停供货资格。 |

### 2.5.10 试产管理流程

**业务场景：** 研发/工程/质量等部门，结合新品开发/ECN 变更/物料变更等，都可以发起相应的试产管理申请，同时由相关业务部门组成联合试产管理小组进行审批管理，同步联动各部门进行业务协同处理。试产是需要多部门联动的业务流，对于质量管理部门，相应的业务流程起始于接到试产通知后，根据研发/工程部门的技术文档，对试产的工程能力进行确认，确认无误后，试产审批通过，终于将 PPAP 文档全部收集汇总后，同时对所有的试产问题进行跟踪确认所有的问题都被解决。

`[图 2.5-16 试产管理流程]`
**流程描述:**
- **输入:** 来自PLM, QMS, 供应商管理等模块的试产需求。
- **研发:** 发起试产申请，进行FMEA和过程流程图设计。
- **相关业务部门:** 进行试产审批。
- **采购(可选):** 进行试产物料采购。
- **IQC:** 进行来料质检。
- **工程/DQE/PQE:** 进行试产风险评估、工程能力确认和过程验证。
- **生产/IPQC:** 进行试生产、首件检验和制程检验。
- **SQE:** 汇总PPAP文件，进行问题追踪。
- **输出:** 试产总结报告、PFMEA知识库等。

**流程梳理：**
1) 在试产审批通过后，若需要涉及到试产新物料的采购，在 **IQC** 进行来料质检时，可以设置相应的试产物料加急检验规则，如时间、规格、供应商评选等，都可以走特殊规则，用于保障试产物料的的有效进行；
2) 由 **DQE/PQE** 对试产的工程能力(含质检标准、质检流程、量检模具等)进行确认，通过后，即可给生产发出同意试产信息；
3) **IPQC** 对于试产的首件和制程检验进行检验，并协助 **DQE/PQE** 进行可靠性测试和合规性测试；而对于试产的成品和包装，也同步进行相应的检测，用于确保产品质量；
4) 由 **SQE** 联合各业务部门，对于 PPAP 资料文件进行汇总，同时对于试产问题进行实时跟进，确保问题得到解决；
5) 对于试产的过程及产品质量检验，由 **DQE/PQE** 输出相应的试产报告，同时提交相关业务部门进行审核，审核通过后，即可进入到量产阶段；如果试产报告审核不通过，则相应的试产即被冻结，不会进入到量产阶段。

**表 2.5-10 关键活动控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **试产方案与标准制定** | 定义试产批次数量、关键验证指标及通过标准，确保 SOP、BOM、工艺流程图、质量控制计划等文件齐备且完成评审，强制文件签批后才可试产。 |
| **相关业务部门准备** | 试产前会议，明确研发、工程、生产、品质、采购等部门的职责；操作人员需提前接受设备操作、工艺要点、异常处理培训，并通过实操考核。 |
| **首件检验** | 严格制定首检流程，首件产品需由工程、品质、生产联合评审，确认符合设计规格和工艺要求，三方会签+留样复测。 |
| **过程控制与数据采集** | **参数控制：** 实时监控关键工艺参数（如温度、压力、时间），与 SOP 标准比对并记录偏差；<br>**检测数据采集：** 缺陷类型、频次及分布位置，用于失效模式分析；<br>**工时与节拍测算：** NPI 工程师需统计各工序实际工时，评估产能瓶颈。 |
| **异常处理机制** | 问题分类跟踪：使用问题追踪表，明确责任人、解决措施和闭环时间。 |
| **根因分析** | 从设计、物料、工艺、设备四个维度分析缺陷根源，制定相应对策进行改进验证。 |
| **量产准入评审** | 基于试产报告（含良率、问题关闭率、产能数据）决策是否转量产，其中核心评审项：对未关闭问题的残留风险是否可接受；多班次验证+IE（工业工程）复核工时测算；SOP、BOM、检验标准等文件的最终版更新和签核；物料供应稳定性，供应商量产能力审核等。 |

### 2.5.11 过程质量管理流程

**业务场景：** 在首件检测通过后，对于批量生产过程中的质量进行完整质量检验管理的流程。过程质量管理的核心在于生产过程的质量监控及数据监控，相应质检操作，由生产进行操作，质量管理部只需要抓取相应的质检数据进行分析。因此，此流程起始于质量等管理领导基于批量生产制定的质量控制计划，终于 PQE 对于生产执行过程中产生的不合格品进行分析后，若是由质检设备故障引起的不良，则提交停机检修审核，在质量等管理领导审批通过后，即进入停线用于进行停机检修。

`[图 2.5-17 过程质量管理流程]`
**流程描述:**
- **输入:** APQP/PPAP文件, 工序标准作业流程(SOP)
- **质量等管理领导:** 制定`质量控制计划`和`检验规则`。
- **生产管理员/PMC:** 进行批量生产审核和生产准备。
- **PQE:** 进行`量检模具调试`和`过程参数实时监控`。
- **IPQC:** 进行`首件检验`和`全检/巡检`。
- **输出:** 各种质量记录和报告。

**流程梳理：**
1) **质量等管理领导**，给出相应的质量控制计划，基于质量控制计划，给出相应的质检人员需求，同时制定相应的检验规则，并对质检人员进行质检规则培训；
2) **PQE** 根据质量控制计划和检验规则，对相应的量检模具进行调试，同时通过实时数据采集，对过程参数进行实时监控；
3) 对于生产过程中的质检数据，由 **PQE** 随时对相应的质检数据进行分析，对于发送的问题，进行实时分析；
4) 对于因量检模具的问题产生的不良，则需要启动停机检修审核，由 **PMC** 根据订单进度安排相应的停机事宜。

**表 2.5-11 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **参数防错与监控** | 关键参数管控，设置自动报警阈值并实时记录；防错验证（Poka-Yoke）：工装夹具防错（如错位无法安装）、光电传感器防漏工序，每日开班前测试有效性；SPC（统计过程控制）使用 X-R 图监控过程能力（CPK）。 |
| **首末件对比确认** | 批量结束时对比首末件差异，识别过程漂移。 |
| **变化点管理** | 新员工首 X 件 100%检验；设备维修后首 X 件全参数测量；材料批次变更新旧批次并行生产对比验证；工程变更单（ECN）审批后生效。 |
| **巡检（IPQC）机制** | 抽检频率与内容，检测数据录入 MES 系统；不合格触发停线规则（如连续 3 件不合格）；测试设备有效性，每日使用前用标准件验证设备，MSA 测量系统分析。 |
| **高风险场景控制** | 参数修改需双重确认，工艺变更前风险评估（FMEA 更新）+试生产验证，测量设备每日点检+校准追溯链。 |

### 2.5.12 首件检验管理流程

**业务场景：** 对于生产开启时，生产的第一个产品，做全面的检验，用于确保符合产品工艺设计要求、符合规格参数要求。需要开启首件检验的触发条件包含：NPI、ECN 变更、设备变更、物料变更、生产恢复及生产条件变更后的首件等，都需要触发首件检验流程。首件检验流程起始于文件确认，终于首件确认单签署审核通过后，进行批量生产。

`[图 2.5-18 首件检验管理流程]`
**流程描述:**
- **输入:** NPI, ECN变更, 设备/物料变更等触发条件
- **工程/PQE:** 进行`文件确认`和`质检设备调试`。
- **生产操作员:** `首件生产`并`自行检查`。
- **IPQC:** 进行`全面检查`，不合格则隔离。
- **PQE:** 对不合格品进行`质量根因分析`。
- **输出:** 首件报告, 质量管理文件, 检验记录。

**流程梳理：**
1) 在触发首件检验时，如果是由新物料触发的首件检验，则需要对新物料进行来料检验，确认来料信息、规格参数等；
2) 若不是新物料触发的首件检验，则在生产首件时，由 **PQE** 先对相应的文件进行确认，尤其是检验规范和质检流程进行确认，同步确认相应的设备参数是符合要求的；
3) 在上述确认完成后，先由**生产操作员**进行首件生产后的自行检查，同步提交给**生产管理员**进行检查复核；无论检查是否通过，都需要 **IPQC** 进行全面检查，即执行三检制度；
4) 对于三检部通过的首件，由 **IPQC** 进行拒收，同时对样品进行隔离；由 **PQE** 对质量原因进行分析，如果是质检设备参数原因，则需要对设备重新进行调试；如果不是，则需要研发/工程人员进行问题分析；
5) 对于三检通过的产品，则需要进行样品留样，因产品的特殊性，样品留样以数据留样为准，同时签署首件确认单，即生产可以进入批量生产阶段；
6) 无论是三检通过或者不通过，都需要对质检规范进行确定，形成标准质检规范，用于后续的生产检验。

**表 2.5-12 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **参数防错与监控** | 关键参数管控，设置自动报警阈值并实时记录；防错验证（Poka-Yoke）：工装夹具防错（如错位无法安装）、光电传感器防漏工序，每日开班前测试有效性；SPC（统计过程控制）使用 X-R 图监控过程能力（CPK）。 |
| **首末件对比确认** | 批量结束时对比首末件差异，识别过程漂移。 |
| **变化点管理** | 新员工首 X 件 100%检验；设备维修后首 X 件全参数测量；材料批次变更新旧批次并行生产对比验证；工程变更单（ECN）审批后生效。 |
| **巡检（IPQC）机制** | 抽检频率与内容，检测数据录入 MES 系统；不合格触发停线规则（如连续 3 件不合格）；测试设备有效性，每日使用前用标准件验证设备，MSA 测量系统分析。 |
| **高风险场景控制** | 参数修改需双重确认，工艺变更前风险评估（FMEA 更新）+试生产验证，测量设备每日点检+校准追溯链。 |

### 2.5.13 在制不合格品管理流程

**业务场景：** 在制不合格品是产品生产过程中，发现的不合格产品的处理流程；相应的业务起始于 IPQC 巡检，终于对不合格品的处理方式决策，并对相应的资料进行存档。

`[图 2.5-19 在制不合格品管理流程]`
**流程描述:**
- **输入:** IPQC巡检发现不合格品
- **生产/IPQC:** 确认不合格，贴标签，隔离。
- **质量领导:** 进行严重性和影响评审，决定是否停线。
- **处理决策:** 根据问题来源（物料/制程/制造）决定退料、返工、返修、让步、降级或报废。
- **输出:** 不合格品处理单, 纠正措施报告。

**流程梳理：**
1) **IPQC** 按照质量控制计划进行巡检，在 MES 通过质检设备发现不合格品后，先由**生产人员**进行自检，再由**生产管理人员**进行复检，并将相应的不合格品放置到不合格专区，由 **IPQC** 进行专检；
2) **IPQC** 确认不合格信息后，贴上不合格标签，将不合格品进行隔离，同时对不良品的缺陷进行分级；如果相应的缺陷等级是严重主要缺陷，则需要进行 100%全检；同时触发停线审核机制；
3) 当不合格率的阈值小于质量控制标准时，产线继续生产即可；如果不良率阈值大于质量控制要求，则自动触发停线评审；
4) 无论任何不良，都需要计入到不合格品记录中，并列出缺陷等级，由**质量等领导**进行严重性评审和影响评审；如果严重性及影响严重，如批量问题等，需要自动触发停线评审；
5) 如果严重性和影响一般，则领导需要给出相应的处理方案，同时填写不合格品处理单用于预防纠正报告输出；
6) 对于处理方式，如果是物料问题，来料导致的问题，则走退不合格料处理的处理流程，由 **IQC** 对来料进行检验退料操作；如果是制程问题，则需要验证首件；如果是制造问题，则需要根据是否影响使用、性能等，给出返工返修、让步接受、降级使用甚至报废等处理结果；
7) 在处理方式中，可以设置处理时效、处理进度跟进、并输出相应的不良品处理报告。

**表 2.5-13 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **标准化判定依据** | 依据文件包含工艺图纸、SOP、检验指导书、缺陷样板等，首先由操作工自检，再由 IPQC 巡检动态抽检关键参数； |
| **即时标识与分类** | 不合格品的即时识别与明确标识，同时对严重度进行分级； |
| **隔离管理** | 物理隔离防止误用，确保在处置前无异常流动 |
| **追溯体系** | 产品批次、工序、不合格数量、缺陷类型、责任机台/操作工；自动关联生产批次、设备参数、物料批次，实现一键追溯； |
| **评审机制** | **影响维度评审**，主要包含产品功能、安全合规、客户满意度；**让步接收条件**仅限非关键特性，需客户书面批准；同时构建**分级评审机制**：<br>轻微不良，可以由班组长+工程评审，车间主任批准返工；<br>一般不良，由质量工程师+生产主管评审，质量部经理签批（降级/让步）；<br>严重不良，需要跨部门 MRB（质量/工艺/生产/工程）评审，副总审批（报废/停线）； |
| **处理方式** | 相应的处理方式有如下控制要求：<br>**返工**，定义返工作业指导书，返工后 100%重检；<br>**返修**，客户确认接收标准，贴”返修”标识；<br>**降级**，更改包装标识，避免混入高等级订单；<br>**报废**，破坏处理，系统销账；<br>对**返工/返修过程管控**，要求持证人员操作，记录返工参数（如温度、时间），留存返工样品。 |
| **数据分析与改进** | 对缺陷数据闭环管理，通过实时看板监控不合格品处理进度，通过实时跟踪产线直通率（FPY）、TOP3 缺陷类型、报废率看板，制定相应的根因分析改进措施；制定质量预防机制，对高风险环节加强控制，尤其是：为避免不良品混入合格批次，建立双锁隔离区+巡检稽核；对返工返修未重检产品流入下工序风险，通过系统强制拦截（MES 未关闭不合格标签不放行）；设备异常维修后，首 X 件全参数检测，确保不会出现批量不良； |

### 2.5.14 末件检验管理流程

**业务场景：** 末件检验是一个生产工单在完成最后一个生产件生产后，生产即将完全下线并切换到新工单前，对最后一个生产件进行全面检查，并跟首件进行对照，确保生产质量的一致性。末件检验的流程在质量部门始于生产对于最后一件进行送检后，由质量部门对末件进行检查，终于对末件检验通过后，进行留样处理后，贴上标识完成工单的最后质检确认。

`[图 2.5-20 末件检验管理流程]`
**流程描述:**
- **输入:** 生产工单完成
- **生产操作员:** 对末件进行检验，送检。
- **IPQC:** 进行全项目检查，与首件确认单对比。
- **PQE:** 对质量偏移进行根因分析。
- **输出:** 末件检验报告, 检验记录。

**流程梳理：**
1) 在工单生产完成后的最后一个产品完成生产后，先由**产线操作人员**对末件进行检验，并贴上末件标识、零件编号、原材料标识，同时附上工艺文件和检验标准进行送检；
2) 由 **IPQC** 对末件进行全项目检查，重点是跟首件确认单进行对比，确认质量是否有偏移；
3) 若无偏移，则输出相应的末件检验报告，并对样品进行留样，贴上标识；因产品的特点，相应的留样，以数据留样为主；
4) 如果发生质量偏移，则需要对不合格品进行处理，同时由 **PQE** 对偏移原因进行根因分析，并制定相应的改进措施进行改进验证；若相应的产品还在进行生产，则需要立即启动停线审核管理流程；
5) 在发现偏移的同时，需要同步通知到工程人员对设备、工艺及工装进行分析，调整相应的参数，重新投入生产，并对生产末件重新进行检查。

**表 2.5-14 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **末件检验触发** | 每班次结束/生产批次切换前，设备维修后、材料批次更换后首末件需同步检验，系统设置检验触发逻辑，自动推送末件检验任务至 IPQC； |
| **检验对象识别** | 明确末件定义，连续生产中的最后一件；班次交接时双方确认实物与记录匹配，杜绝误检非末件导致漏判； |
| **检验标准制定** | 依据 CTQ（关键质量特性）清单，覆盖尺寸/外观/功能/材料特性；运用控制计划(CP)、FMEA 风险分析，规避检测项目不全、未覆盖关键特性的问题； |
| **检验方法选择** | 采用自动化检测测量设备，解决人工检测误差导致的误判； |
| **设备状态关联确认** | 通过 IoT 传感器实时监控+SPC 控制图，实时记录测量设备参数，与末件数据绑定分析，自动比对末件与首件差异，实时生成检测报告； |
| **追溯链条完整性** | 建立一物一码和追溯体系，通过末件绑定批次号/设备 ID/操作者，录入系统，解决问题末件无法关联生产批次的问题； |
| **异常响应与改进** | 对不合格末件，立即冻结本批次末段产品并复检，启动 RCA（根本原因分析），通过跨部门协同机制形成闭环管理，质量部更新控制图监控边界，聚焦质量管理漏洞； |

### 2.5.15 成品检验管理流程

**业务场景：** 成品检验管理是对产品成品在生产过程中的检测，相应的检测以成品的各类功能性检测为核心，确保产品成品符合客户需求及工艺工程设计要求。相应的业务流开始于质量管理的质量控制计划，终于成品检验完成后的检验放行。

`[图 2.5-21 成品检验管理流程]`
**流程描述:**
- **输入:** 客户需求, 行业标准
- **质量管理领导:** 制定`质量控制计划`和`检验规则`。
- **PQE:** 准备`检验环境`和`检验工具`。
- **FQC:** 进行`耐压测试`、`闭环测试`、`老化测试`等，并进行`合格判定`。
- **输出:** 检验报告, 异常处理单。

**流程梳理：**
1) **质量管理领导** 结合客户需求及产品工艺工程文件，制定产品成品的质量控制计划，同时给出相应的检验规则；
2) **PQE** 根据检验规则，准备相应的检测环境、调试相应的所需检测设备，并对制定对应的检验方法，同步提供给到 **FQC** 用于检测；
3) 相应的检测，根据产品的特点，初步设定为：耐压测试、闭环测试、老化测试、出厂测试、气密性测试；根据抽检测试规则，同时进行 OBA/ORT 测试；对于相应的测试，由 **MES** 系统执行记录测试结果，由 **FQC** 进行相应的质量数据进行趋势分析；
4) 对于相应的测试结果，进行合格性判定，如果测试结果合格，则进行放行，并附上检验报告、生产记录等文件；
5) 如果测试不合格，则需要走成品不合格处理，并对根因进行分析后，结合实际需求，调整检验规则，对返回的成品进行重新检测；
6) 对于成品，仓储可以对长期库存的成品，同步发起成品检验的需求，由产线和质量结合质量控制计划进行相应的产品检验，用以确保产品质量的可靠性。

**表 2.5-15 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **成品检验触发** | 订单批次生产完成（100%全检或 AQL 抽样），包装线封箱前（防错包装/标签错误），工艺变更后首批、客户投诉后复产批次；在未进行成品检验时，系统自动锁批，禁止入库，确保成品检验得到切实有效执行； |
| **检验标准动态化管理** | 外观/尺寸/功能/可靠性测试必须覆盖 FMEA 高风险项 |
| **检验方法科学选择** | 针对不同的检测类型，用对应的数字化检测手段，提升检测效率，降低漏检率，保证检测数据可追溯性； |
| **抽样方案风险管控** | 高价值/高风险产品推行加严检验，结合缺陷率设定动态抽样规则 |
| **追溯链条完整性** | 建立一物一码和追溯体系，通过检验结果绑定批次号/设备 ID/操作者，录入系统，确保数据的不可篡改性，解决无法关联生产批次的问题； |
| **放行决策多级审核** | 集成 QMS 系统建立数字化审批流，自动触发审批权限及流程锁定； |
| **异常响应与改进** | 对不合格成品，在返工/返修返回后，需要重新检测，同时针对不良原因启动 RCA（根本原因分析），通过跨部门协同机制形成闭环管理，质量部更新控制图监控边界，聚焦质量管理漏洞； |

### 2.5.17 包装检验管理流程

**业务场景：** 包装检验管理是产品成品在包装环节进行的检测，相应的检测主要是针对包装的配件、外观、尺寸等进行检查，在全部检查通过后，即可作为最终成品进行成品入库及发货。相应的业务流，起始于质量控制计划，终于检验通过后放行入库，检查操作承接成品检验放行。

`[图 2.5-23 包装检验管理流程]`
**流程描述:**
- **输入:** 客户需求, 生产通知单
- **质量管理领导:** 制定`质量控制计划`。
- **PQE:** 准备`检验环境`和`检验工具`。
- **OQC(包装):** 核对工序卡，进行`配件检查`、`标签检查`、`外观检查`、`尺寸检查`等。
- **输出:** 检验报告, 异常处理单。

**流程梳理：**
1) **质量管理领导** 结合客户需求及产品工艺工程文件，制定产品成品的质量控制计划，同时给出相应的检验规则；
2) **PQE** 根据检验规则，准备相应的检测环境、调试相应的所需检测设备，并对制定对应的检验方法，同步提供给到 **OQC** 用于包装检测；
3) 根据产品的特点，在成品到包装环节后，先核对工序卡和生产通知单的一致性，同时对产品成品的外观进行检查，对于成品产品的配件、标签、外观及尺寸进行检查，确保符合客户需求及出货要求；
4) 在产线包装完成后，对于包装进行检验，检验通过后，填写相应的包装检验表，进行产品包装检验放行；
5) **QMS** 记录相应的放行数量给到 **PMC**，由 **PMC** 再次核对数量、文件信息，验证通过后，即运输到仓库进行入库操作；
6) 对于包装检验不通过的产品，则需要进行生产返工，对于成品不合格品，则走成品不合格品处理流程；对于返回的产品进行重新检测；
7) 对于检验数据、报表、报告，建立历史数据库，配置不同的查询条件，用于各类追溯查询操作。

**表 2.5-17 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **标准规范** | 需制定《成品检验规范》《出货检验标准》等文件，明确外观、尺寸、印刷、密封性等检测项目及方法；制定抽样方案 AQL（可接受质量水平）及批量判定规则，避免过度检验或漏检； |
| **过程监控** | **4M1E 要素巡查：** 检查人员操作规范性、设备参数、物料标识、证书标识、配件等；<br>**巡检频率：** 按关键工序设定巡检频次，记录 CTQ 关键质量特性数据；<br>**三检制落地：** 自检（操作工）、互检（上下工序）、专检（IPQC）结合，防止批量异常； |
| **检验放行** | **全项目验证：** 成品需完成功能测试（如密封性测试）、包装完整性、标签准确性（位置、类型等）、运输标志清晰度；部分客户的特殊需求，如密封性测试，则需要根据需求进行检测；<br>**检验放行：** OQC 签字确认的产品可出厂，记录批次号以便追溯； |
| **实时反馈闭环** | 发现异常时，IPQC 需立即叫停生产，开具《品质异常单》并跟踪纠正措施；向前反馈（调整前工序）与向后反馈（拦截不良流出）双机制并行； |
| **数据追溯与记录** | 建立唯一性标识管理，物料贴标管理状态（合格/隔离/待处理），检验报告、首件确认单、巡检记录实时存档，支持数据分析；记录至少保存至产品有效期后一年，满足审计要求 |
| **放行决策多级审核** | 集成 QMS 系统建立数字化审批流，自动触发审批权限及流程锁定； |
| **异常响应与改进** | 对不合格成品，建立不合格分级管理，外购件不合格需退回供应商，生产急料需签署特采协议； |

### 2.5.18 成品检验不合格管理流程

**业务场景：** 成品检验不合格管理是产品成品在进入出货前的检测，相应的成品不合格分为产品成品不合格和包装成品不合格两部分，相应的处理流程遵循同一处理流程。相应业务流开始于出货检验需求检验，终于对不合格品处理的纠正措施报告。

`[图 2.5-24 成品检验不合格管理流程]`
**流程描述:**
- **输入:** 客户投诉, 内部检验发现不合格
- **销售/OQC:** 发现不合格，进行标识和隔离。
- **质量等领导评审会:** 进行严重性评估，决定处理方案（返工/返修/报废/让步/召回）。
- **相关部门:** 执行处理方案，如生产返工、采购退货、售后召回。
- **输出:** 不合格品处理单, 纠正措施报告, 召回报告等。

**流程梳理：**
1) 在进行成品检验的过程中，如果发现不合格项，先对缺陷进行分级，同时贴上不合格标签并对不合格产品进行隔离；如果相应的缺陷为重大缺陷，则需要质量管理领导签字确认；
2) **PMC** 在接到不合格品进行隔离的信息后，将不合格品运输到不良品区，同时对不良产品进行追溯，对于流向进行统计，相应的统计可以分各维度进行展示；
3) 对不合格项，需要**质量管理等领导**就严重性、影响性进行评估；如果影响比较严重、范围性的不合格项，且恰在生产中，则需要立即进行停线评审；
4) 对于相应的不合格项，进行根因分析，然后给出相应的处理方案，如降级使用、让步接受、返工/返修、报废等，如果相应的产品已经发货，则需要涉及召回管理；在每种的处理方式中，可以设置处理时效、处理进度跟进、并输出相应的不良品处理报告；
5) 在进行根因分析时，是来料问题导致的不合格项，则需要对来料不合格进行处理，并进行不合格记录，同时给出预防改正措施；
6) 如果是让步接受，需要由领导进行审批，领导同时需要具备加签审核功能；如果相应的不合格项，是安全及可靠性问题，则需要由总经理进行审批；
7) 如果是报废，则需要填写报废申请单，走报废管理流程；
8) 如果是返工/返修的产品，在相应的产品返回后，需要进行 100%全检，才能继续往下进行。

**表 2.5-18 活动关键控制点**
| 活动名称 | 关键控制点 |
| :--- | :--- |
| **不合格分级判定** | 需要对缺陷等级进行标准化定义(如严重/主要、次要、轻微等)，根据缺陷等级绑定抽样规则；依据《成品检验规范》《不合格品控制程序》文件判定，争议时由质量工程师仲裁； |
| **处理方案** | 相应的处理方式，包含：<br>**报废：** 严重/主要缺陷、返工成本＞新品成本，需要质量经理+生产总监签字，报废单上需要附照片/测试报告；<br>**返工/返修：** 次要缺陷、可修复，工艺工程师验证方案可行性，记录返工步骤、复检数据；<br>**让步接受：** 轻微缺陷且不影响核心功能，客户书面确认（如 PPAP 签字），保留让步接收单及风险承诺书；如果是涉及到安全/可靠性的问题，则需要分管副总和总经理签批；如果不是，则由质量管理部领导签批即可，由其自行决策是否需要分管业务副总加签；<br>**召回管理：** 对于已出货且存在安全性缺陷的产品，需遵循召回管理条例；<br>其中，返工品必须隔离标识（红色标签+独立区域），避免混入合格品；让步接受审批需客户/法规机构书面确认； |
| **实时反馈闭环** | 质检员开具《不合格品评审单》，由 QC 做根因分析，并制定遏制措施，生产部对返回不合格品进行重新检测，同时由研发/工程制定相应的长期整改措施用于解决造成不良的问题； |
| **数据追溯与记录** | 建立唯一性标识管理，实现一物一档，通过质量数据驱动持续改进； |
| **放行决策多级审核** | 集成 QMS 系统建立数字化审批流，自动触发审批权限及流程锁定； |