<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>员工权限管理 - 慧新全智厂园一体平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #3b82f6;
            --primary-dark: #1e40af;
            --secondary: #64748b;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-users-cog text-primary mr-3"></i>
                员工权限管理
            </h1>
            <p class="text-gray-600 mt-2">管理员工通行权限，确保园区安全有序</p>
        </div>

        <!-- 员工权限统计 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">在职员工</h3>
                        <p class="text-3xl font-bold text-blue-600 mt-2">1,248</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-users text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>正式员工:</span>
                        <span class="text-blue-600 font-medium">1,156人</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>临时员工:</span>
                        <span class="text-purple-600 font-medium">92人</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">已授权</h3>
                        <p class="text-3xl font-bold text-green-600 mt-2">1,235</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-user-check text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>授权率:</span>
                        <span class="text-green-600 font-medium">99.0%</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>待授权:</span>
                        <span class="text-orange-600 font-medium">13人</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">权限区域</h3>
                        <p class="text-3xl font-bold text-purple-600 mt-2">15</p>
                    </div>
                    <div class="bg-purple-100 p-3 rounded-full">
                        <i class="fas fa-map-marked-alt text-purple-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>覆盖率:</span>
                        <span class="text-purple-600 font-medium">100%</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>特殊区域:</span>
                        <span class="text-red-600 font-medium">3个</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">权限变更</h3>
                        <p class="text-3xl font-bold text-orange-600 mt-2">23</p>
                    </div>
                    <div class="bg-orange-100 p-3 rounded-full">
                        <i class="fas fa-exchange-alt text-orange-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>本周申请:</span>
                        <span class="text-blue-600 font-medium">23个</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>待审批:</span>
                        <span class="text-orange-600 font-medium">5个</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速权限设置 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-bolt text-blue-600 mr-2"></i>
                快速权限设置
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-semibold text-gray-800">全区域权限</h4>
                        <i class="fas fa-globe text-blue-600"></i>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">适用于管理人员，可访问所有区域</p>
                    <div class="space-y-2 text-xs text-gray-600">
                        <div>• 办公区域 ✓</div>
                        <div>• 生产车间 ✓</div>
                        <div>• 仓库区域 ✓</div>
                        <div>• 设备机房 ✓</div>
                    </div>
                    <button class="w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                        批量授权
                    </button>
                </div>

                <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-200">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-semibold text-gray-800">生产区权限</h4>
                        <i class="fas fa-industry text-green-600"></i>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">适用于生产人员，可访问生产相关区域</p>
                    <div class="space-y-2 text-xs text-gray-600">
                        <div>• 办公区域 ✓</div>
                        <div>• 生产车间 ✓</div>
                        <div>• 仓库区域 ✓</div>
                        <div>• 设备机房 ✗</div>
                    </div>
                    <button class="w-full mt-4 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                        批量授权
                    </button>
                </div>

                <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-200">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-semibold text-gray-800">办公区权限</h4>
                        <i class="fas fa-building text-purple-600"></i>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">适用于办公人员，仅可访问办公区域</p>
                    <div class="space-y-2 text-xs text-gray-600">
                        <div>• 办公区域 ✓</div>
                        <div>• 生产车间 ✗</div>
                        <div>• 仓库区域 ✗</div>
                        <div>• 设备机房 ✗</div>
                    </div>
                    <button class="w-full mt-4 px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors">
                        批量授权
                    </button>
                </div>
            </div>
        </div>

        <!-- 权限审批流程 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-clipboard-check text-green-600 mr-2"></i>
                权限审批流程
            </h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-center space-x-4">
                        <div class="bg-blue-600 text-white rounded-full w-10 h-10 flex items-center justify-center">
                            <i class="fas fa-user"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800">张三 - 生产部</h4>
                            <p class="text-sm text-gray-600">申请仓库区域访问权限</p>
                            <p class="text-xs text-gray-500">申请时间: 2025-01-17 14:30</p>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                            <i class="fas fa-check mr-1"></i>批准
                        </button>
                        <button class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                            <i class="fas fa-times mr-1"></i>拒绝
                        </button>
                    </div>
                </div>

                <div class="flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="flex items-center space-x-4">
                        <div class="bg-yellow-600 text-white rounded-full w-10 h-10 flex items-center justify-center">
                            <i class="fas fa-user"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800">李四 - 维修部</h4>
                            <p class="text-sm text-gray-600">申请设备机房权限</p>
                            <p class="text-xs text-gray-500">申请时间: 2025-01-17 13:45</p>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <span class="px-4 py-2 bg-yellow-100 text-yellow-800 rounded text-sm">
                            <i class="fas fa-clock mr-1"></i>审批中
                        </span>
                    </div>
                </div>

                <div class="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div class="flex items-center space-x-4">
                        <div class="bg-green-600 text-white rounded-full w-10 h-10 flex items-center justify-center">
                            <i class="fas fa-user"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800">王五 - 质检部</h4>
                            <p class="text-sm text-gray-600">申请生产车间权限</p>
                            <p class="text-xs text-gray-500">申请时间: 2025-01-17 12:20</p>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <span class="px-4 py-2 bg-green-100 text-green-800 rounded text-sm">
                            <i class="fas fa-check mr-1"></i>已批准
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 权限管理操作 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-tools text-purple-600 mr-2"></i>
                权限管理操作
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button class="flex flex-col items-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
                    <i class="fas fa-user-plus text-blue-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">新增员工</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                    <i class="fas fa-key text-green-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">权限配置</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors">
                    <i class="fas fa-users-cog text-purple-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">批量管理</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors">
                    <i class="fas fa-download text-orange-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">导出报表</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 权限管理功能
        function initEmployeePermissionManagement() {
            console.log('初始化员工权限管理功能');
            
            // 权限审批按钮事件
            const approvalButtons = document.querySelectorAll('button');
            approvalButtons.forEach(button => {
                const text = button.textContent.trim();
                if (text.includes('批准')) {
                    button.addEventListener('click', function() {
                        const employeeName = this.closest('.rounded-lg').querySelector('h4').textContent;
                        console.log('批准权限申请:', employeeName);
                        alert(`已批准 ${employeeName} 的权限申请`);
                    });
                } else if (text.includes('拒绝')) {
                    button.addEventListener('click', function() {
                        const employeeName = this.closest('.rounded-lg').querySelector('h4').textContent;
                        console.log('拒绝权限申请:', employeeName);
                        alert(`已拒绝 ${employeeName} 的权限申请`);
                    });
                } else if (text.includes('批量授权')) {
                    button.addEventListener('click', function() {
                        const permissionType = this.closest('.rounded-lg').querySelector('h4').textContent;
                        console.log('批量授权:', permissionType);
                        alert(`正在执行${permissionType}批量授权...`);
                    });
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initEmployeePermissionManagement();
            console.log('员工权限管理页面加载完成');
        });
    </script>
</body>
</html>
