<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分析统计 - 厂内物流执行系统(LES) - 数字工厂一体化平台</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">分析统计</h1>
            <p class="text-gray-600">配送统计、利用率分析、送达率统计、报警统计等数据分析和统计报表</p>
        </div>

        <!-- 统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-blue-600">1,245</div>
                        <div class="text-sm text-gray-600">配送任务</div>
                        <div class="text-xs text-gray-500">本月完成</div>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-truck text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-green-600">98.5%</div>
                        <div class="text-sm text-gray-600">按时送达率</div>
                        <div class="text-xs text-gray-500">质量指标</div>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-orange-600">85%</div>
                        <div class="text-sm text-gray-600">设备利用率</div>
                        <div class="text-xs text-gray-500">平均水平</div>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-pie text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-purple-600">23</div>
                        <div class="text-sm text-gray-600">异常报警</div>
                        <div class="text-xs text-gray-500">本月总数</div>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计报表 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- 配送统计 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">配送统计</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">今日配送任务</span>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: 75%"></div>
                            </div>
                            <span class="text-sm font-medium text-blue-600">156/208</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">本周配送任务</span>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                <div class="bg-green-600 h-2 rounded-full" style="width: 88%"></div>
                            </div>
                            <span class="text-sm font-medium text-green-600">1,056/1,200</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">本月配送任务</span>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                <div class="bg-purple-600 h-2 rounded-full" style="width: 92%"></div>
                            </div>
                            <span class="text-sm font-medium text-purple-600">4,245/4,600</span>
                        </div>
                    </div>
                </div>
                <div class="mt-6 pt-4 border-t border-gray-200">
                    <div class="grid grid-cols-3 gap-4 text-center">
                        <div>
                            <div class="text-lg font-bold text-blue-600">6.5分钟</div>
                            <div class="text-xs text-gray-600">平均配送时间</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-green-600">120米</div>
                            <div class="text-xs text-gray-600">平均配送距离</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-orange-600">2.3次</div>
                            <div class="text-xs text-gray-600">日均配送频次</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设备利用率分析 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">设备利用率分析</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">AGV自动导引车</span>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                <div class="bg-green-600 h-2 rounded-full" style="width: 92%"></div>
                            </div>
                            <span class="text-sm font-medium text-green-600">92%</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">叉车设备</span>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: 85%"></div>
                            </div>
                            <span class="text-sm font-medium text-blue-600">85%</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">输送带系统</span>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                <div class="bg-purple-600 h-2 rounded-full" style="width: 78%"></div>
                            </div>
                            <span class="text-sm font-medium text-purple-600">78%</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">料车料箱</span>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                <div class="bg-orange-600 h-2 rounded-full" style="width: 88%"></div>
                            </div>
                            <span class="text-sm font-medium text-orange-600">88%</span>
                        </div>
                    </div>
                </div>
                <div class="mt-6 pt-4 border-t border-gray-200">
                    <div class="text-center">
                        <div class="text-lg font-bold text-blue-600">85%</div>
                        <div class="text-xs text-gray-600">整体设备利用率</div>
                        <div class="text-xs text-green-600 mt-1">较上月提升 +3.2%</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 送达率统计和报警统计 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- 送达率统计 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">送达率统计</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                        <div>
                            <div class="text-sm font-medium text-green-800">按时送达</div>
                            <div class="text-xs text-gray-600">1,225任务</div>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-green-600">98.5%</div>
                            <div class="text-xs text-gray-500">目标: ≥95%</div>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                        <div>
                            <div class="text-sm font-medium text-yellow-800">延迟送达</div>
                            <div class="text-xs text-gray-600">15任务</div>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-yellow-600">1.2%</div>
                            <div class="text-xs text-gray-500">平均延迟: 3分钟</div>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                        <div>
                            <div class="text-sm font-medium text-red-800">异常中断</div>
                            <div class="text-xs text-gray-600">5任务</div>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-red-600">0.3%</div>
                            <div class="text-xs text-gray-500">需要处理</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 报警统计 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">报警统计</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                        <div>
                            <div class="text-sm font-medium text-red-800">设备故障</div>
                            <div class="text-xs text-gray-600">AGV、叉车等设备异常</div>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-red-600">8</div>
                            <div class="text-xs text-gray-500">本月</div>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                        <div>
                            <div class="text-sm font-medium text-orange-800">路径阻塞</div>
                            <div class="text-xs text-gray-600">运输路径被占用</div>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-orange-600">6</div>
                            <div class="text-xs text-gray-500">本月</div>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                        <div>
                            <div class="text-sm font-medium text-yellow-800">物料缺失</div>
                            <div class="text-xs text-gray-600">物料识别异常</div>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-yellow-600">5</div>
                            <div class="text-xs text-gray-500">本月</div>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <div>
                            <div class="text-sm font-medium text-blue-800">网络异常</div>
                            <div class="text-xs text-gray-600">通信连接问题</div>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-blue-600">4</div>
                            <div class="text-xs text-gray-500">本月</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 报表导出 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">统计报表导出</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <button class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow text-center">
                    <i class="fas fa-file-excel text-green-600 text-2xl mb-2"></i>
                    <div class="text-sm font-medium text-gray-800">配送统计报表</div>
                    <div class="text-xs text-gray-600">Excel格式</div>
                </button>
                <button class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow text-center">
                    <i class="fas fa-chart-bar text-blue-600 text-2xl mb-2"></i>
                    <div class="text-sm font-medium text-gray-800">利用率分析报表</div>
                    <div class="text-xs text-gray-600">图表格式</div>
                </button>
                <button class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow text-center">
                    <i class="fas fa-clock text-orange-600 text-2xl mb-2"></i>
                    <div class="text-sm font-medium text-gray-800">送达率统计报表</div>
                    <div class="text-xs text-gray-600">PDF格式</div>
                </button>
                <button class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow text-center">
                    <i class="fas fa-exclamation-triangle text-red-600 text-2xl mb-2"></i>
                    <div class="text-sm font-medium text-gray-800">异常报警报表</div>
                    <div class="text-xs text-gray-600">详细记录</div>
                </button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('分析统计页面已加载');
        });
    </script>
</body>
</html>
