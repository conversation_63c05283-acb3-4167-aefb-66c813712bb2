<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双碳管理 - 慧新全智厂园一体平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-leaf text-green-600 mr-3"></i>
                双碳管理
            </h1>
            <p class="text-gray-600 mt-2">碳排放监控与管理，助力碳达峰碳中和目标</p>
        </div>

        <!-- 碳排放统计 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">今日排放</h3>
                        <p class="text-3xl font-bold text-red-600 mt-2">2.8</p>
                        <p class="text-sm text-gray-500">吨CO₂</p>
                    </div>
                    <div class="bg-red-100 p-3 rounded-full">
                        <i class="fas fa-smog text-red-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>较昨日:</span>
                        <span class="text-green-600 font-medium">-8.2%</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">碳减排</h3>
                        <p class="text-3xl font-bold text-green-600 mt-2">1.2</p>
                        <p class="text-sm text-gray-500">吨CO₂</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-arrow-down text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>减排率:</span>
                        <span class="text-green-600 font-medium">30%</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">碳中和进度</h3>
                        <p class="text-3xl font-bold text-blue-600 mt-2">68%</p>
                        <p class="text-sm text-gray-500">年度目标</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-target text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>目标:</span>
                        <span class="text-blue-600 font-medium">2030年</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">碳信用</h3>
                        <p class="text-3xl font-bold text-purple-600 mt-2">156</p>
                        <p class="text-sm text-gray-500">吨CO₂当量</p>
                    </div>
                    <div class="bg-purple-100 p-3 rounded-full">
                        <i class="fas fa-certificate text-purple-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>本月新增:</span>
                        <span class="text-purple-600 font-medium">+28</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 碳管理措施 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-tasks text-green-600 mr-2"></i>
                碳管理措施
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-200">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-semibold text-gray-800">光伏替代</h4>
                        <i class="fas fa-solar-panel text-green-600"></i>
                    </div>
                    <div class="text-sm text-gray-600 space-y-2">
                        <div class="flex justify-between">
                            <span>替代电量:</span>
                            <span class="font-medium text-green-600">1,234 kWh</span>
                        </div>
                        <div class="flex justify-between">
                            <span>减排量:</span>
                            <span class="font-medium">0.8 吨CO₂</span>
                        </div>
                        <div class="flex justify-between">
                            <span>替代率:</span>
                            <span class="font-medium text-blue-600">35%</span>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: 35%"></div>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-semibold text-gray-800">能效提升</h4>
                        <i class="fas fa-chart-line text-blue-600"></i>
                    </div>
                    <div class="text-sm text-gray-600 space-y-2">
                        <div class="flex justify-between">
                            <span>节能量:</span>
                            <span class="font-medium text-blue-600">456 kWh</span>
                        </div>
                        <div class="flex justify-between">
                            <span>减排量:</span>
                            <span class="font-medium">0.3 吨CO₂</span>
                        </div>
                        <div class="flex justify-between">
                            <span>效率提升:</span>
                            <span class="font-medium text-green-600">15.2%</span>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: 68%"></div>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-200">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-semibold text-gray-800">绿色采购</h4>
                        <i class="fas fa-shopping-cart text-purple-600"></i>
                    </div>
                    <div class="text-sm text-gray-600 space-y-2">
                        <div class="flex justify-between">
                            <span>绿色产品:</span>
                            <span class="font-medium text-purple-600">85%</span>
                        </div>
                        <div class="flex justify-between">
                            <span>减排量:</span>
                            <span class="font-medium">0.1 吨CO₂</span>
                        </div>
                        <div class="flex justify-between">
                            <span>认证产品:</span>
                            <span class="font-medium text-green-600">92%</span>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-purple-500 h-2 rounded-full" style="width: 85%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 碳足迹分析 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-chart-pie text-red-600 mr-2"></i>
                    碳排放来源分析
                </h3>
                <div class="space-y-4">
                    <div class="bg-gradient-to-r from-red-50 to-orange-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">电力消耗</span>
                            <span class="text-lg font-bold text-red-600">65%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-red-500 h-3 rounded-full" style="width: 65%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">1.82 吨CO₂</div>
                    </div>
                    <div class="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">燃料消耗</span>
                            <span class="text-lg font-bold text-orange-600">20%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-orange-500 h-3 rounded-full" style="width: 20%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">0.56 吨CO₂</div>
                    </div>
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">交通运输</span>
                            <span class="text-lg font-bold text-blue-600">10%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-blue-500 h-3 rounded-full" style="width: 10%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">0.28 吨CO₂</div>
                    </div>
                    <div class="bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">其他来源</span>
                            <span class="text-lg font-bold text-gray-600">5%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-gray-500 h-3 rounded-full" style="width: 5%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">0.14 吨CO₂</div>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-chart-line text-green-600 mr-2"></i>
                    碳中和路径
                </h3>
                <div class="space-y-4">
                    <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">短期目标 (2025)</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• 碳排放强度降低 30%</div>
                            <div>• 可再生能源占比 50%</div>
                            <div>• 能效提升 20%</div>
                            <div class="text-green-600">• 进度: 68% 完成</div>
                        </div>
                        <div class="mt-2">
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 68%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">中期目标 (2030)</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• 实现碳达峰</div>
                            <div>• 可再生能源占比 80%</div>
                            <div>• 碳排放总量控制</div>
                            <div class="text-blue-600">• 进度: 45% 完成</div>
                        </div>
                        <div class="mt-2">
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: 45%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">长期目标 (2060)</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• 实现碳中和</div>
                            <div>• 100% 清洁能源</div>
                            <div>• 负碳技术应用</div>
                            <div class="text-purple-600">• 进度: 15% 完成</div>
                        </div>
                        <div class="mt-2">
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-purple-500 h-2 rounded-full" style="width: 15%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 碳报告生成 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-file-alt text-blue-600 mr-2"></i>
                碳报告生成
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
                    <h4 class="font-semibold text-gray-800 mb-3">月度碳报告</h4>
                    <div class="text-sm text-gray-600 space-y-2">
                        <div>• 碳排放统计</div>
                        <div>• 减排措施效果</div>
                        <div>• 目标完成情况</div>
                        <div>• 改进建议</div>
                    </div>
                    <button class="w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                        生成报告
                    </button>
                </div>

                <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-200">
                    <h4 class="font-semibold text-gray-800 mb-3">年度碳盘查</h4>
                    <div class="text-sm text-gray-600 space-y-2">
                        <div>• 全面碳足迹分析</div>
                        <div>• 第三方验证</div>
                        <div>• 合规性检查</div>
                        <div>• 认证申请</div>
                    </div>
                    <button class="w-full mt-4 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                        启动盘查
                    </button>
                </div>

                <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-200">
                    <h4 class="font-semibold text-gray-800 mb-3">ESG报告</h4>
                    <div class="text-sm text-gray-600 space-y-2">
                        <div>• 环境绩效</div>
                        <div>• 社会责任</div>
                        <div>• 公司治理</div>
                        <div>• 可持续发展</div>
                    </div>
                    <button class="w-full mt-4 px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors">
                        编制报告
                    </button>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-bolt text-blue-600 mr-2"></i>
                快速操作
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button class="flex flex-col items-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                    <i class="fas fa-calculator text-green-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">碳排放计算</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
                    <i class="fas fa-target text-blue-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">目标设定</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors">
                    <i class="fas fa-certificate text-purple-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">碳认证</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors">
                    <i class="fas fa-download text-orange-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">导出数据</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 双碳管理功能
        function initCarbonManagement() {
            console.log('初始化双碳管理功能');
            
            // 报告生成按钮事件
            const reportButtons = document.querySelectorAll('button');
            reportButtons.forEach(button => {
                const text = button.textContent.trim();
                if (text.includes('生成报告')) {
                    button.addEventListener('click', function() {
                        console.log('生成月度碳报告');
                        alert('正在生成月度碳报告...');
                    });
                } else if (text.includes('启动盘查')) {
                    button.addEventListener('click', function() {
                        console.log('启动年度碳盘查');
                        alert('正在启动年度碳盘查...');
                    });
                } else if (text.includes('编制报告')) {
                    button.addEventListener('click', function() {
                        console.log('编制ESG报告');
                        alert('正在编制ESG报告...');
                    });
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCarbonManagement();
            console.log('双碳管理页面加载完成');
        });
    </script>
</body>
</html>
