<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据质量监控 - 主数据平台 - 数字工厂一体化平台</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">数据质量监控</h1>
            <p class="text-gray-600">实时监控数据质量状况，提供质量检查、异常监控、清洗规则、质量报告等功能</p>
        </div>

        <!-- 数据质量概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-yellow-600">98.5%</div>
                        <div class="text-sm text-gray-600">整体质量评分</div>
                        <div class="text-xs text-gray-500">综合评价</div>
                    </div>
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-yellow-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-blue-600">28</div>
                        <div class="text-sm text-gray-600">监控规则</div>
                        <div class="text-xs text-gray-500">运行中</div>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-shield-alt text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-green-600">1,256</div>
                        <div class="text-sm text-gray-600">检查记录</div>
                        <div class="text-xs text-gray-500">今日完成</div>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-red-600">15</div>
                        <div class="text-sm text-gray-600">质量异常</div>
                        <div class="text-xs text-gray-500">待处理</div>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据质量监控功能选项卡 -->
        <div class="mb-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8">
                    <button onclick="showTab('dashboard')" class="tab-button border-b-2 border-yellow-500 text-yellow-600 py-2 px-1 text-sm font-medium" id="dashboard-tab">
                        质量监控面板
                    </button>
                    <button onclick="showTab('rules')" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 text-sm font-medium" id="rules-tab">
                        质量检查规则
                    </button>
                    <button onclick="showTab('anomalies')" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 text-sm font-medium" id="anomalies-tab">
                        异常监控
                    </button>
                    <button onclick="showTab('reports')" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 text-sm font-medium" id="reports-tab">
                        质量报告
                    </button>
                </nav>
            </div>
        </div>

        <!-- 质量监控面板 -->
        <div id="dashboard-content" class="tab-content">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- 数据质量指标 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">数据质量指标</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">完整性</span>
                            <div class="flex items-center">
                                <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: 98%"></div>
                                </div>
                                <span class="text-sm font-medium text-green-600">98%</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">准确性</span>
                            <div class="flex items-center">
                                <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 96%"></div>
                                </div>
                                <span class="text-sm font-medium text-blue-600">96%</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">一致性</span>
                            <div class="flex items-center">
                                <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                    <div class="bg-purple-600 h-2 rounded-full" style="width: 99%"></div>
                                </div>
                                <span class="text-sm font-medium text-purple-600">99%</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">及时性</span>
                            <div class="flex items-center">
                                <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                    <div class="bg-orange-600 h-2 rounded-full" style="width: 94%"></div>
                                </div>
                                <span class="text-sm font-medium text-orange-600">94%</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">有效性</span>
                            <div class="flex items-center">
                                <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                    <div class="bg-teal-600 h-2 rounded-full" style="width: 97%"></div>
                                </div>
                                <span class="text-sm font-medium text-teal-600">97%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据源质量分布 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">数据源质量分布</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                            <div>
                                <div class="text-sm font-medium text-green-800">物料主数据</div>
                                <div class="text-xs text-gray-600">1,256条记录</div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-green-600">98.5%</div>
                                <div class="text-xs text-gray-500">质量评分</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                            <div>
                                <div class="text-sm font-medium text-blue-800">设备主数据</div>
                                <div class="text-xs text-gray-600">156条记录</div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-blue-600">97.2%</div>
                                <div class="text-xs text-gray-500">质量评分</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                            <div>
                                <div class="text-sm font-medium text-orange-800">人员主数据</div>
                                <div class="text-xs text-gray-600">245条记录</div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-orange-600">99.1%</div>
                                <div class="text-xs text-gray-500">质量评分</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                            <div>
                                <div class="text-sm font-medium text-purple-800">供应商主数据</div>
                                <div class="text-xs text-gray-600">89条记录</div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-purple-600">96.8%</div>
                                <div class="text-xs text-gray-500">质量评分</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 质量趋势图 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">质量趋势分析</h3>
                <div class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                    <div class="text-center">
                        <i class="fas fa-chart-line text-4xl text-gray-400 mb-2"></i>
                        <div class="text-gray-600">质量趋势图表</div>
                        <div class="text-sm text-gray-500">显示过去30天的数据质量变化趋势</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 质量检查规则 -->
        <div id="rules-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-800">质量检查规则</h3>
                    <button class="bg-primary text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700" onclick="addQualityRule()">
                        <i class="fas fa-plus mr-2"></i>新增规则
                    </button>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规则名称</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查类型</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">应用范围</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">执行频率</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后执行</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">物料编号完整性检查</div>
                                    <div class="text-xs text-gray-500">检查物料编号是否为空</div>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                        完整性
                                    </span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm text-gray-900">物料主数据</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm text-gray-900">每小时</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm text-gray-900">2025-01-17 14:00</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        运行中
                                    </span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-600 hover:text-blue-900">编辑</button>
                                        <button class="text-green-600 hover:text-green-900">执行</button>
                                        <button class="text-red-600 hover:text-red-900">停用</button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">设备编号格式检查</div>
                                    <div class="text-xs text-gray-500">检查设备编号格式是否正确</div>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                                        格式
                                    </span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm text-gray-900">设备主数据</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm text-gray-900">每日</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm text-gray-900">2025-01-17 08:00</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        运行中
                                    </span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-600 hover:text-blue-900">编辑</button>
                                        <button class="text-green-600 hover:text-green-900">执行</button>
                                        <button class="text-red-600 hover:text-red-900">停用</button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">数据一致性检查</div>
                                    <div class="text-xs text-gray-500">检查跨系统数据一致性</div>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">
                                        一致性
                                    </span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm text-gray-900">全部主数据</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm text-gray-900">每4小时</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm text-gray-900">2025-01-17 12:00</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        运行中
                                    </span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-600 hover:text-blue-900">编辑</button>
                                        <button class="text-green-600 hover:text-green-900">执行</button>
                                        <button class="text-red-600 hover:text-red-900">停用</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 异常监控 -->
        <div id="anomalies-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">质量异常监控</h3>
                <div class="space-y-4">
                    <div class="bg-red-50 p-4 rounded-lg border-l-4 border-red-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-red-800">物料编号重复</div>
                                <div class="text-xs text-gray-600">发现3条物料记录编号重复</div>
                                <div class="text-xs text-gray-500">检测时间: 2025-01-17 14:30:15</div>
                            </div>
                            <div class="text-right">
                                <span class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">严重</span>
                                <div class="text-xs text-gray-500 mt-1">影响记录: 3条</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-orange-50 p-4 rounded-lg border-l-4 border-orange-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-orange-800">设备参数缺失</div>
                                <div class="text-xs text-gray-600">部分设备缺少关键技术参数</div>
                                <div class="text-xs text-gray-500">检测时间: 2025-01-17 14:15:30</div>
                            </div>
                            <div class="text-right">
                                <span class="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">警告</span>
                                <div class="text-xs text-gray-500 mt-1">影响记录: 8条</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-yellow-800">数据更新延迟</div>
                                <div class="text-xs text-gray-600">人员信息更新超过预期时间</div>
                                <div class="text-xs text-gray-500">检测时间: 2025-01-17 14:00:45</div>
                            </div>
                            <div class="text-right">
                                <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">提醒</span>
                                <div class="text-xs text-gray-500 mt-1">影响记录: 4条</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-blue-800">数据质量改善</div>
                                <div class="text-xs text-gray-600">供应商数据完整性提升至96.8%</div>
                                <div class="text-xs text-gray-500">检测时间: 2025-01-17 13:45:20</div>
                            </div>
                            <div class="text-right">
                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">信息</span>
                                <div class="text-xs text-gray-500 mt-1">改善记录: 12条</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 质量报告 -->
        <div id="reports-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-800">质量报告</h3>
                    <button class="bg-primary text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700" onclick="generateReport()">
                        <i class="fas fa-file-alt mr-2"></i>生成报告
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- 日报 -->
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="viewReport('daily')">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-gray-800">数据质量日报</h4>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">PDF</span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">报告日期:</span>
                                <span class="text-gray-900">2025-01-17</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">检查记录:</span>
                                <span class="text-gray-900">1,256条</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">质量评分:</span>
                                <span class="text-green-600">98.5%</span>
                            </div>
                        </div>
                        <div class="mt-3 flex space-x-2">
                            <button class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded hover:bg-blue-200">查看</button>
                            <button class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded hover:bg-gray-200">下载</button>
                        </div>
                    </div>

                    <!-- 周报 -->
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="viewReport('weekly')">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-gray-800">数据质量周报</h4>
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Excel</span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">报告周期:</span>
                                <span class="text-gray-900">第3周</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">检查记录:</span>
                                <span class="text-gray-900">8,792条</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">平均评分:</span>
                                <span class="text-green-600">97.8%</span>
                            </div>
                        </div>
                        <div class="mt-3 flex space-x-2">
                            <button class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded hover:bg-green-200">查看</button>
                            <button class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded hover:bg-gray-200">下载</button>
                        </div>
                    </div>

                    <!-- 月报 -->
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="viewReport('monthly')">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-gray-800">数据质量月报</h4>
                            <span class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">PPT</span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">报告月份:</span>
                                <span class="text-gray-900">2025年1月</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">检查记录:</span>
                                <span class="text-gray-900">35,468条</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">月度评分:</span>
                                <span class="text-green-600">98.2%</span>
                            </div>
                        </div>
                        <div class="mt-3 flex space-x-2">
                            <button class="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded hover:bg-purple-200">查看</button>
                            <button class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded hover:bg-gray-200">下载</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示选项卡
        function showTab(tabName) {
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // 重置所有选项卡样式
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('border-yellow-500', 'text-yellow-600');
                button.classList.add('border-transparent', 'text-gray-500');
            });
            
            // 显示选中的内容
            document.getElementById(tabName + '-content').classList.remove('hidden');
            
            // 设置选中的选项卡样式
            const activeTab = document.getElementById(tabName + '-tab');
            activeTab.classList.remove('border-transparent', 'text-gray-500');
            activeTab.classList.add('border-yellow-500', 'text-yellow-600');
        }

        // 操作函数
        function addQualityRule() {
            alert('新增质量检查规则功能');
        }

        function generateReport() {
            alert('生成质量报告功能');
        }

        function viewReport(type) {
            alert(`查看${type}质量报告功能`);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            showTab('dashboard');
        });
    </script>
</body>
</html>
