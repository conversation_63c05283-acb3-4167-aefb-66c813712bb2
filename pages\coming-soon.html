<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能开发中 - 数字工厂一体化平台</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="flex flex-col items-center justify-center min-h-screen p-6 text-center">
        <div class="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mb-6">
            <i class="fas fa-tools text-primary text-4xl"></i>
        </div>
        
        <h1 class="text-3xl font-bold text-gray-800 mb-4">功能开发中</h1>
        <p class="text-xl text-gray-600 mb-8">该功能模块正在开发中，敬请期待！</p>
        
        <div class="w-full max-w-md bg-white rounded-lg shadow-sm p-8 mb-8">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                    <i class="fas fa-info-circle text-primary"></i>
                </div>
                <h2 class="text-xl font-semibold text-gray-800">开发进度</h2>
            </div>
            
            <div class="space-y-4">
                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-sm font-medium text-gray-700">需求分析</span>
                        <span class="text-sm font-medium text-gray-700">100%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-primary h-2 rounded-full" style="width: 100%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-sm font-medium text-gray-700">设计阶段</span>
                        <span class="text-sm font-medium text-gray-700">80%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-primary h-2 rounded-full" style="width: 80%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-sm font-medium text-gray-700">开发阶段</span>
                        <span class="text-sm font-medium text-gray-700">45%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-primary h-2 rounded-full" style="width: 45%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-sm font-medium text-gray-700">测试阶段</span>
                        <span class="text-sm font-medium text-gray-700">10%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-primary h-2 rounded-full" style="width: 10%"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <p class="text-gray-500">预计完成时间：2024年第三季度</p>
    </div>
</body>
</html>
