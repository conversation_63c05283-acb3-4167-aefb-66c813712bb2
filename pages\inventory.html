<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仓储管理系统(WMS) - 数字工厂一体化平台</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">仓储管理系统(WMS)</h1>
            <p class="text-gray-600">实现仓储作业的精细化、自动化和无纸化，确保账实一致，并通过FIFO和批次管理优化库存</p>
        </div>

        <!-- WMS统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-blue-600">1,245</div>
                        <div class="text-sm text-gray-600">在库物料(SKU)</div>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-boxes text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-green-600">156</div>
                        <div class="text-sm text-gray-600">今日入库单</div>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-arrow-down text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-orange-600">89</div>
                        <div class="text-sm text-gray-600">今日出库单</div>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-arrow-up text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-red-600">12</div>
                        <div class="text-sm text-gray-600">库龄预警</div>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- WMS功能模块卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
            <!-- 入库管理 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer" onclick="navigateToSubModule('inbound-management')">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-arrow-down text-green-600 text-xl"></i>
                    </div>
                    <i class="fas fa-arrow-right text-green-600"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">入库管理</h3>
                <p class="text-gray-600 text-sm mb-4">采购收货、退料入库、预约管理</p>
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">待收货</span>
                        <span class="text-green-600 font-medium">24</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">待上架</span>
                        <span class="text-orange-600 font-medium">8</span>
                    </div>
                </div>
            </div>

            <!-- 出库管理 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer" onclick="navigateToSubModule('outbound-management')">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-arrow-up text-blue-600 text-xl"></i>
                    </div>
                    <i class="fas fa-arrow-right text-blue-600"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">出库管理</h3>
                <p class="text-gray-600 text-sm mb-4">生产发料、销售出库、调拨出库</p>
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">待拣货</span>
                        <span class="text-blue-600 font-medium">15</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">待发货</span>
                        <span class="text-purple-600 font-medium">6</span>
                    </div>
                </div>
            </div>

            <!-- 库内管理 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer" onclick="navigateToSubModule('warehouse-management')">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-warehouse text-purple-600 text-xl"></i>
                    </div>
                    <i class="fas fa-arrow-right text-purple-600"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">库内管理</h3>
                <p class="text-gray-600 text-sm mb-4">储位管理、盘点管理、库龄监控</p>
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">储位利用率</span>
                        <span class="text-green-600 font-medium">85%</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">盘点任务</span>
                        <span class="text-orange-600 font-medium">3</span>
                    </div>
                </div>
            </div>

            <!-- 委外/VMI管理 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer" onclick="navigateToSubModule('vmi-management')">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-handshake text-indigo-600 text-xl"></i>
                    </div>
                    <i class="fas fa-arrow-right text-indigo-600"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">委外/VMI管理</h3>
                <p class="text-gray-600 text-sm mb-4">委外发料收货、虚拟仓管理</p>
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">委外在制</span>
                        <span class="text-indigo-600 font-medium">45</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">VMI库存</span>
                        <span class="text-blue-600 font-medium">1,256</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function navigateToSubModule(subModuleId) {
            // 通知父窗口切换到对应的子模块
            if (parent && parent.switchSubMenu) {
                parent.switchSubMenu('inventory', subModuleId);
            }
        }
    </script>
</body>
</html>

</body>
</html>
