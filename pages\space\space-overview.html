<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空间概览与地图 - 慧新全智厂园一体平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #3b82f6;
            --primary-dark: #1e40af;
            --secondary: #64748b;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-map text-primary mr-3"></i>
                空间概览与地图
            </h1>
            <p class="text-gray-600 mt-2">园区空间布局展示，实时掌握空间利用情况</p>
        </div>

        <!-- 空间概览统计 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">建筑栋数</h3>
                        <p class="text-3xl font-bold text-purple-600 mt-2">12</p>
                        <p class="text-sm text-gray-500">栋建筑</p>
                    </div>
                    <div class="bg-purple-100 p-3 rounded-full">
                        <i class="fas fa-building text-purple-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>总建筑面积:</span>
                        <span class="text-purple-600 font-medium">85,600 m²</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>可用面积:</span>
                        <span class="text-green-600 font-medium">78,200 m²</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">房间总数</h3>
                        <p class="text-3xl font-bold text-blue-600 mt-2">156</p>
                        <p class="text-sm text-gray-500">个房间</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-door-open text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>办公室:</span>
                        <span class="text-blue-600 font-medium">89间</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>会议室:</span>
                        <span class="text-green-600 font-medium">23间</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">空间利用率</h3>
                        <p class="text-3xl font-bold text-green-600 mt-2">78%</p>
                        <p class="text-sm text-gray-500">当前使用</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-percentage text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>已使用:</span>
                        <span class="text-green-600 font-medium">122间</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>空闲:</span>
                        <span class="text-orange-600 font-medium">34间</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">今日预约</h3>
                        <p class="text-3xl font-bold text-yellow-600 mt-2">23</p>
                        <p class="text-sm text-gray-500">会议室预约</p>
                    </div>
                    <div class="bg-yellow-100 p-3 rounded-full">
                        <i class="fas fa-calendar-check text-yellow-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>进行中:</span>
                        <span class="text-green-600 font-medium">8个</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>待开始:</span>
                        <span class="text-blue-600 font-medium">15个</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 空间地图展示 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-map-marked-alt text-blue-600 mr-2"></i>
                园区空间地图
            </h3>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="lg:col-span-2">
                    <div class="h-96 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200 flex items-center justify-center">
                        <div class="text-center text-gray-500">
                            <i class="fas fa-map text-6xl mb-4 text-blue-400"></i>
                            <p class="text-lg font-medium">园区3D地图</p>
                            <p class="text-sm">交互式空间布局展示</p>
                            <div class="mt-4 flex justify-center space-x-4">
                                <button class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                                    <i class="fas fa-search-plus mr-1"></i>放大
                                </button>
                                <button class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                                    <i class="fas fa-layer-group mr-1"></i>楼层
                                </button>
                                <button class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">
                                    <i class="fas fa-filter mr-1"></i>筛选
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="space-y-4">
                    <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">楼层导航</h4>
                        <div class="space-y-2">
                            <button class="w-full text-left px-3 py-2 bg-purple-100 text-purple-800 rounded hover:bg-purple-200">
                                <i class="fas fa-building mr-2"></i>A栋 - 办公楼 (5层)
                            </button>
                            <button class="w-full text-left px-3 py-2 bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                                <i class="fas fa-industry mr-2"></i>B栋 - 生产车间 (3层)
                            </button>
                            <button class="w-full text-left px-3 py-2 bg-green-100 text-green-800 rounded hover:bg-green-200">
                                <i class="fas fa-warehouse mr-2"></i>C栋 - 仓储中心 (2层)
                            </button>
                            <button class="w-full text-left px-3 py-2 bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200">
                                <i class="fas fa-utensils mr-2"></i>D栋 - 餐饮中心 (2层)
                            </button>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">空间类型</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center justify-between">
                                <span class="flex items-center">
                                    <div class="w-3 h-3 bg-blue-500 rounded mr-2"></div>
                                    办公区域
                                </span>
                                <span class="text-blue-600 font-medium">89间</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="flex items-center">
                                    <div class="w-3 h-3 bg-green-500 rounded mr-2"></div>
                                    会议室
                                </span>
                                <span class="text-green-600 font-medium">23间</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="flex items-center">
                                    <div class="w-3 h-3 bg-purple-500 rounded mr-2"></div>
                                    生产区域
                                </span>
                                <span class="text-purple-600 font-medium">18间</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="flex items-center">
                                    <div class="w-3 h-3 bg-yellow-500 rounded mr-2"></div>
                                    公共区域
                                </span>
                                <span class="text-yellow-600 font-medium">26间</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 利用率分析 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-chart-pie text-green-600 mr-2"></i>
                    空间利用率分析
                </h3>
                <div class="space-y-4">
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">办公区域</span>
                            <span class="text-lg font-bold text-blue-600">85%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-blue-500 h-3 rounded-full" style="width: 85%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">76/89间 使用中</div>
                    </div>
                    <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">会议室</span>
                            <span class="text-lg font-bold text-green-600">65%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-green-500 h-3 rounded-full" style="width: 65%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">15/23间 使用中</div>
                    </div>
                    <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">生产区域</span>
                            <span class="text-lg font-bold text-purple-600">95%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-purple-500 h-3 rounded-full" style="width: 95%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">17/18间 使用中</div>
                    </div>
                    <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">公共区域</span>
                            <span class="text-lg font-bold text-yellow-600">55%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-yellow-500 h-3 rounded-full" style="width: 55%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">14/26间 使用中</div>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-chart-line text-blue-600 mr-2"></i>
                    空间统计报表
                </h3>
                <div class="space-y-4">
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">使用趋势</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• 平均利用率: 78%</div>
                            <div>• 峰值时段: 09:00-11:00</div>
                            <div>• 低谷时段: 14:00-15:00</div>
                            <div class="text-blue-600">• 较上月提升: +5.2%</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">空间效率</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• 人均使用面积: 15.2 m²</div>
                            <div>• 空间周转率: 1.8次/天</div>
                            <div>• 预约准时率: 92%</div>
                            <div class="text-green-600">• 满意度评分: 4.6/5</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">优化建议</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• 增加小型会议室</div>
                            <div>• 优化公共区域布局</div>
                            <div>• 推广灵活办公模式</div>
                            <div class="text-purple-600">• 预计提升: 8%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时空间状态 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-eye text-purple-600 mr-2"></i>
                实时空间状态
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-200">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-800">会议室A-101</h4>
                        <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">使用中</span>
                    </div>
                    <div class="text-sm text-gray-600 space-y-1">
                        <div>• 容量: 8人</div>
                        <div>• 当前: 6人</div>
                        <div>• 预约: 14:00-16:00</div>
                        <div>• 使用人: 张经理</div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-800">会议室A-102</h4>
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">已预约</span>
                    </div>
                    <div class="text-sm text-gray-600 space-y-1">
                        <div>• 容量: 12人</div>
                        <div>• 当前: 空闲</div>
                        <div>• 预约: 15:30-17:00</div>
                        <div>• 预约人: 李主任</div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg p-4 border border-gray-200">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-800">会议室A-103</h4>
                        <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">空闲</span>
                    </div>
                    <div class="text-sm text-gray-600 space-y-1">
                        <div>• 容量: 6人</div>
                        <div>• 当前: 空闲</div>
                        <div>• 下次预约: 明日 09:00</div>
                        <div>• 可立即使用</div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-red-50 to-pink-50 rounded-lg p-4 border border-red-200">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-800">会议室A-104</h4>
                        <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">维护中</span>
                    </div>
                    <div class="text-sm text-gray-600 space-y-1">
                        <div>• 容量: 20人</div>
                        <div>• 状态: 设备维护</div>
                        <div>• 预计恢复: 明日</div>
                        <div>• 暂不可用</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-bolt text-blue-600 mr-2"></i>
                快速操作
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button class="flex flex-col items-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
                    <i class="fas fa-search text-blue-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">空间查询</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                    <i class="fas fa-calendar-plus text-green-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">预约空间</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors">
                    <i class="fas fa-map-marked-alt text-purple-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">导航定位</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors">
                    <i class="fas fa-download text-orange-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">空间报表</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 空间概览与地图功能
        function initSpaceOverview() {
            console.log('初始化空间概览与地图功能');
            
            // 楼层导航按钮事件
            const floorButtons = document.querySelectorAll('button');
            floorButtons.forEach(button => {
                if (button.textContent.includes('栋')) {
                    button.addEventListener('click', function() {
                        const buildingName = this.textContent.trim();
                        console.log('切换楼层:', buildingName);
                        alert(`正在切换到 ${buildingName}`);
                    });
                }
            });
            
            // 实时数据更新
            function updateSpaceData() {
                console.log('更新空间数据');
                // 这里可以添加实时数据更新逻辑
            }
            
            // 启动定时更新
            setInterval(updateSpaceData, 60000); // 每分钟更新一次
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initSpaceOverview();
            console.log('空间概览与地图页面加载完成');
        });
    </script>
</body>
</html>
