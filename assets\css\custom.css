/* MOM系统自定义样式 */

/* 导航项样式 */
.nav-item {
    color: #6b7280;
    text-decoration: none;
}

.nav-item:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.nav-item.active {
    background-color: #1e40af;
    color: white;
}

.nav-item.active:hover {
    background-color: #1d4ed8;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.card-header {
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    margin: 0;
}

/* 状态指示器 */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-success {
    background-color: #d1fae5;
    color: #065f46;
}

.status-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.status-danger {
    background-color: #fee2e2;
    color: #991b1b;
}

.status-info {
    background-color: #dbeafe;
    color: #1e40af;
}

/* 数据表格样式 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.data-table th {
    background-color: #f9fafb;
    padding: 0.75rem 1rem;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
}

.data-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f3f4f6;
    color: #6b7280;
}

.data-table tbody tr:hover {
    background-color: #f9fafb;
}

/* KPI卡片样式 */
.kpi-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.kpi-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.kpi-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

.kpi-trend {
    display: flex;
    align-items: center;
    margin-top: 0.5rem;
    font-size: 0.75rem;
}

/* 图表容器 */
.chart-container {
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
}

/* 进度条样式 */
.progress-bar {
    width: 100%;
    height: 0.5rem;
    background-color: #e5e7eb;
    border-radius: 9999px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: #10b981;
    transition: width 0.3s ease;
}

/* 设备状态样式 */
.equipment-status {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.equipment-status-dot {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
}

.equipment-running {
    background-color: #10b981;
}

.equipment-idle {
    background-color: #f59e0b;
}

.equipment-maintenance {
    background-color: #ef4444;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-item span {
        display: none;
    }
    
    .nav-item {
        justify-content: center;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 加载状态 */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6b7280;
}

.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #1e40af;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 三级菜单样式 */
.sub-menu-group {
    margin-bottom: 4px;
}

.sub-items {
    border-left: 2px solid #e5e7eb;
    margin-left: 12px;
    padding-left: 8px;
    transition: all 0.3s ease;
}

.sub-items.hidden {
    max-height: 0;
    opacity: 0;
    overflow: hidden;
}

.sub-items:not(.hidden) {
    max-height: 500px;
    opacity: 1;
}

.sub-item-link {
    position: relative;
    color: #6b7280;
    font-weight: 500;
    display: block;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.sub-item-link:hover {
    color: #2563eb;
    background-color: #eff6ff;
    text-decoration: none;
}

.sub-item-link.active {
    color: #2563eb;
    background-color: #eff6ff;
    font-weight: 600;
}

.sub-item-link.active::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background-color: #2563eb;
    border-radius: 2px;
}

/* 二级菜单项样式优化 */
.sub-nav-item {
    color: #374151;
    font-weight: 500;
    text-decoration: none;
}

.sub-nav-item:hover {
    background-color: #f9fafb;
    color: #1f2937;
    text-decoration: none;
}

.sub-nav-item.active {
    background-color: #eff6ff;
    color: #2563eb;
}

/* 菜单展开图标动画 */
.sub-nav-item i[id^="chevron-"] {
    transition: transform 0.2s ease;
}
