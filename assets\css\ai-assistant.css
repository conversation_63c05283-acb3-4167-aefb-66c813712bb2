/**
 * 数字工厂一体化平台 - AI助手样式
 * 版本: v1.0.0
 */

/* AI助手按钮样式 */
#ai-assistant-btn {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 9999;
}

#ai-assistant-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.4);
}

#ai-assistant-btn:active {
    transform: scale(1.05);
}

/* AI对话框样式 */
#ai-assistant-dialog {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(229, 231, 235, 0.8);
    z-index: 9998;
    width: 400px;
    transition: height 0.3s ease-in-out;
    min-height: 300px;
    max-height: calc(100vh - 140px);
}

/* 响应式调整 */
@media (max-width: 640px) {
    #ai-assistant-dialog {
        width: calc(100vw - 40px);
        right: 20px;
        left: 20px;
        bottom: 80px;
        max-height: calc(100vh - 120px);
    }
    
    #ai-assistant-btn {
        width: 50px;
        height: 50px;
        bottom: 20px;
        right: 20px;
    }
}

/* 动画效果 */
.animate-fade-in {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 加载动画 */
.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 时间线样式优化 */
.timeline-item {
    transition: all 0.2s ease;
    position: relative;
    min-height: 80px;
}

.timeline-item:hover {
    background-color: rgba(59, 130, 246, 0.05);
    border-radius: 8px;
}

/* 时间线左侧容器 */
.timeline-left {
    width: 32px;
    min-height: 100%;
    position: relative;
}

/* 时间线图标 */
.timeline-icon {
    position: relative;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 时间线连接线 */
.timeline-connector {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    background: linear-gradient(to bottom, #d1d5db 0%, #9ca3af 50%, #d1d5db 100%);
    z-index: 1;
    border-radius: 1px;
}

/* 时间线内容区域 */
.timeline-content {
    position: relative;
    padding-bottom: 24px;
}

/* 最后一个时间线项目的特殊处理 */
.timeline-item:last-child .timeline-content {
    padding-bottom: 8px;
}

/* 响应式调整 */
@media (max-width: 640px) {
    .timeline-item {
        min-height: 70px;
    }

    .timeline-left {
        width: 28px;
    }

    .timeline-icon {
        width: 28px;
        height: 28px;
    }

    .timeline-connector {
        top: 28px;
    }

    .timeline-content {
        margin-left: 12px;
        padding-bottom: 20px;
    }
}

/* 状态指示器 */
.status-indicator {
    position: relative;
    overflow: hidden;
}

.status-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.status-indicator:hover::before {
    left: 100%;
}

/* 输入框样式增强 */
#ai-input:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 搜索按钮样式 */
#ai-search-btn {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    transition: all 0.2s ease;
}

#ai-search-btn:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
}

/* 关闭按钮样式 */
#ai-close-btn {
    transition: all 0.2s ease;
    border-radius: 4px;
    padding: 4px;
}

#ai-close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

/* 滚动条样式 */
#ai-results::-webkit-scrollbar {
    width: 6px;
}

#ai-results::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

#ai-results::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

#ai-results::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 时间线连接线样式 */
.timeline-connector {
    background: linear-gradient(to bottom, #e5e7eb 0%, #d1d5db 50%, #e5e7eb 100%);
}

/* 文档类型标签样式 */
.doc-type-tag {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.doc-type-tag:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    border-color: #cbd5e1;
}

/* 状态徽章样式 */
.status-badge {
    font-weight: 500;
    letter-spacing: 0.025em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 悬停提示样式 */
.tooltip {
    position: relative;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
    z-index: 10000;
}

.tooltip:hover::after {
    opacity: 1;
}

/* 数据展示样式 */
.data-display {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-left: 3px solid #3b82f6;
    font-family: 'Courier New', monospace;
}

/* 交互反馈 */
.interactive-item {
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 6px;
}

.interactive-item:hover {
    background-color: rgba(59, 130, 246, 0.05);
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.interactive-item:active {
    transform: translateX(1px);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* 空状态样式 */
.empty-state {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 2px dashed #e2e8f0;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.empty-state:hover {
    border-color: #cbd5e1;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

/* 加载状态样式 */
.loading-state {
    background: linear-gradient(135deg, #fef7ff 0%, #faf5ff 100%);
    border: 1px solid #e9d5ff;
    border-radius: 12px;
}

/* 错误状态样式 */
.error-state {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    border: 1px solid #fecaca;
    border-radius: 12px;
}

/* 成功状态样式 */
.success-state {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border: 1px solid #bbf7d0;
    border-radius: 12px;
}

/* 高亮效果 */
.highlight {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border: 1px solid #93c5fd;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}
