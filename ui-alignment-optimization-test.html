<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI对齐优化测试 - 数字工厂一体化平台</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">数字工厂一体化平台 UI对齐优化测试</h1>
            <p class="text-gray-600">验证首页模块卡片【进入模块】按钮高度对齐优化效果</p>
        </div>

        <!-- 优化说明 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">🎨 UI对齐优化内容</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">优化前问题</h4>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li>❌ 各模块卡片简介文字行数不一致</li>
                            <li>❌ 【进入模块】按钮垂直位置不对齐</li>
                            <li>❌ 影响整体视觉效果和用户体验</li>
                            <li>❌ 卡片高度参差不齐</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">优化后效果</h4>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li>✅ 统一简介文字区域高度为2.5rem</li>
                            <li>✅ 所有【进入模块】按钮完全对齐</li>
                            <li>✅ 使用flexbox布局确保按钮位于底部</li>
                            <li>✅ 保持响应式设计和企业级UI风格</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技术实现 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">⚙️ 技术实现</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">Flexbox布局优化</h4>
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <p class="text-sm text-blue-800 mb-2"><strong>关键CSS类</strong>：</p>
                            <ul class="text-sm text-blue-700 space-y-1">
                                <li>• <code>flex flex-col h-full</code> - 卡片容器</li>
                                <li>• <code>flex-grow</code> - 简介文字区域</li>
                                <li>• <code>mt-auto</code> - 按钮自动推到底部</li>
                                <li>• <code>min-height: 2.5rem</code> - 统一文字高度</li>
                            </ul>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">响应式设计保持</h4>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <p class="text-sm text-green-800 mb-2"><strong>适配特点</strong>：</p>
                            <ul class="text-sm text-green-700 space-y-1">
                                <li>• 桌面端：5列网格布局</li>
                                <li>• 平板端：3列网格布局</li>
                                <li>• 移动端：1列网格布局</li>
                                <li>• 所有尺寸下按钮都完美对齐</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试步骤 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">📋 测试步骤</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                        <div>
                            <h4 class="font-medium text-gray-800">打开平台主页</h4>
                            <p class="text-sm text-gray-600">访问 <a href="http://localhost:8081/" target="_blank" class="text-blue-600 hover:underline">http://localhost:8081/</a></p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                        <div>
                            <h4 class="font-medium text-gray-800">检查业务中心模块</h4>
                            <p class="text-sm text-gray-600">验证7个业务功能卡片的【进入模块】按钮是否完全对齐</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                        <div>
                            <h4 class="font-medium text-gray-800">检查运营中心模块</h4>
                            <p class="text-sm text-gray-600">验证2个运营功能卡片的【进入模块】按钮是否完全对齐</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">4</div>
                        <div>
                            <h4 class="font-medium text-gray-800">检查基础平台模块</h4>
                            <p class="text-sm text-gray-600">验证5个基础功能卡片的【进入模块】/【进入平台】按钮是否完全对齐</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">5</div>
                        <div>
                            <h4 class="font-medium text-gray-800">响应式测试</h4>
                            <p class="text-sm text-gray-600">调整浏览器窗口大小，验证在不同屏幕尺寸下按钮对齐效果</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 验证要点 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">🔍 验证要点</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">业务中心模块</h4>
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <p class="text-sm text-blue-800 mb-2"><strong>7个功能卡片</strong>：</p>
                            <ul class="text-sm text-blue-700 space-y-1">
                                <li>• 计划管理</li>
                                <li>• 生产管理</li>
                                <li>• 仓储管理</li>
                                <li>• 厂内物流</li>
                                <li>• 质量管理</li>
                                <li>• 设备管理</li>
                                <li>• 能源管理</li>
                            </ul>
                        </div>
                        <div class="mt-4">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" class="form-checkbox text-primary">
                                <span class="text-sm text-gray-700">所有按钮完全对齐</span>
                            </label>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">运营中心模块</h4>
                        <div class="bg-red-50 p-4 rounded-lg">
                            <p class="text-sm text-red-800 mb-2"><strong>2个功能卡片</strong>：</p>
                            <ul class="text-sm text-red-700 space-y-1">
                                <li>• 数据看板</li>
                                <li>• 数字孪生</li>
                            </ul>
                        </div>
                        <div class="mt-4">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" class="form-checkbox text-primary">
                                <span class="text-sm text-gray-700">所有按钮完全对齐</span>
                            </label>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">基础平台模块</h4>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-800 mb-2"><strong>5个功能卡片</strong>：</p>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>• IOT平台</li>
                                <li>• 低代码平台</li>
                                <li>• 主数据平台</li>
                                <li>• 数据集成平台</li>
                                <li>• 慧图云平台</li>
                            </ul>
                        </div>
                        <div class="mt-4">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" class="form-checkbox text-primary">
                                <span class="text-sm text-gray-700">所有按钮完全对齐</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SAP/OA系统验证 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">🏢 SAP/OA系统验证</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">SAP系统卡片</h4>
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <p class="text-sm text-blue-800 mb-2"><strong>验证项目</strong>：</p>
                            <ul class="text-sm text-blue-700 space-y-1">
                                <li>• 简介文字高度统一</li>
                                <li>• 【进入系统】按钮对齐</li>
                                <li>• 与其他卡片高度一致</li>
                            </ul>
                        </div>
                        <div class="mt-4">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" class="form-checkbox text-primary">
                                <span class="text-sm text-gray-700">SAP卡片按钮对齐正确</span>
                            </label>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">OA系统卡片</h4>
                        <div class="bg-emerald-50 p-4 rounded-lg">
                            <p class="text-sm text-emerald-800 mb-2"><strong>验证项目</strong>：</p>
                            <ul class="text-sm text-emerald-700 space-y-1">
                                <li>• 简介文字高度统一</li>
                                <li>• 【进入系统】按钮对齐</li>
                                <li>• 与SAP卡片高度一致</li>
                            </ul>
                        </div>
                        <div class="mt-4">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" class="form-checkbox text-primary">
                                <span class="text-sm text-gray-700">OA卡片按钮对齐正确</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速测试按钮 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">🚀 快速测试</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="window.open('http://localhost:8081/', '_blank')" 
                            class="p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-home mb-2"></i>
                        <div class="font-medium">打开平台主页</div>
                        <div class="text-xs opacity-80">验证UI对齐效果</div>
                    </button>
                    <button onclick="testAlignment()" 
                            class="p-4 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                        <i class="fas fa-align-center mb-2"></i>
                        <div class="font-medium">检查按钮对齐</div>
                        <div class="text-xs opacity-80">自动验证对齐效果</div>
                    </button>
                    <button onclick="testResponsive()" 
                            class="p-4 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                        <i class="fas fa-mobile-alt mb-2"></i>
                        <div class="font-medium">响应式测试</div>
                        <div class="text-xs opacity-80">测试不同屏幕尺寸</div>
                    </button>
                </div>
            </div>
        </div>

        <!-- 验证结果总结 -->
        <div class="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6">
            <div class="flex items-start">
                <i class="fas fa-clipboard-check text-green-600 text-2xl mr-4 mt-1"></i>
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">UI对齐优化验证清单</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h4 class="font-medium text-gray-800 mb-2">基础验证</h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>□ 业务中心模块按钮完全对齐</li>
                                <li>□ 运营中心模块按钮完全对齐</li>
                                <li>□ 基础平台模块按钮完全对齐</li>
                                <li>□ SAP/OA系统按钮完全对齐</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-800 mb-2">响应式验证</h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>□ 桌面端对齐效果正常</li>
                                <li>□ 平板端对齐效果正常</li>
                                <li>□ 移动端对齐效果正常</li>
                                <li>□ 窗口大小变化时保持对齐</li>
                            </ul>
                        </div>
                    </div>
                    <div class="mt-4 p-3 bg-white rounded-lg border border-green-200">
                        <p class="text-sm text-green-800">
                            <i class="fas fa-lightbulb mr-2"></i>
                            <strong>测试提示</strong>：请仔细观察每个模块中所有卡片的【进入模块】按钮是否在同一水平线上，确保UI对齐优化效果达到预期。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 测试按钮对齐
        function testAlignment() {
            alert('请手动检查：\n1. 每个模块内所有卡片的按钮是否在同一水平线上\n2. 简介文字区域高度是否统一\n3. 卡片整体高度是否一致\n\n如果对齐正确，请在验证清单中勾选对应项目。');
        }

        // 测试响应式
        function testResponsive() {
            alert('请手动测试：\n1. 调整浏览器窗口宽度\n2. 观察不同屏幕尺寸下的按钮对齐效果\n3. 确认在桌面端、平板端、移动端都保持良好对齐\n\n如果响应式效果正常，请在验证清单中勾选对应项目。');
        }

        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('UI对齐优化测试页面已加载');
            console.log('请按照测试步骤验证按钮对齐效果');
        });
    </script>
</body>
</html>
