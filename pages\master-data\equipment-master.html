<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备主数据管理 - 主数据平台 - 数字工厂一体化平台</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">设备主数据管理</h1>
            <p class="text-gray-600">统一管理设备档案、技术参数、维护记录、备件信息等核心设备数据</p>
        </div>

        <!-- 设备统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-green-600">156</div>
                        <div class="text-sm text-gray-600">设备总数</div>
                        <div class="text-xs text-gray-500">在线管理</div>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-cogs text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-blue-600">8</div>
                        <div class="text-sm text-gray-600">设备类型</div>
                        <div class="text-xs text-gray-500">分类管理</div>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-layer-group text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-orange-600">96%</div>
                        <div class="text-sm text-gray-600">设备在线率</div>
                        <div class="text-xs text-gray-500">运行状态</div>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-wifi text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-purple-600">1,256</div>
                        <div class="text-sm text-gray-600">备件库存</div>
                        <div class="text-xs text-gray-500">备件种类</div>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-tools text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备管理功能选项卡 -->
        <div class="mb-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8">
                    <button onclick="showTab('equipment')" class="tab-button border-b-2 border-green-500 text-green-600 py-2 px-1 text-sm font-medium" id="equipment-tab">
                        设备档案管理
                    </button>
                    <button onclick="showTab('parameters')" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 text-sm font-medium" id="parameters-tab">
                        技术参数管理
                    </button>
                    <button onclick="showTab('maintenance')" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 text-sm font-medium" id="maintenance-tab">
                        维护记录管理
                    </button>
                    <button onclick="showTab('spareparts')" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 text-sm font-medium" id="spareparts-tab">
                        备件信息管理
                    </button>
                </nav>
            </div>
        </div>

        <!-- 设备档案管理 -->
        <div id="equipment-content" class="tab-content">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-800">设备档案管理</h3>
                        <div class="flex space-x-2">
                            <button class="bg-primary text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700" onclick="addEquipment()">
                                <i class="fas fa-plus mr-2"></i>新增设备
                            </button>
                            <button class="bg-success text-white px-4 py-2 rounded-md text-sm hover:bg-green-700" onclick="importEquipment()">
                                <i class="fas fa-upload mr-2"></i>批量导入
                            </button>
                            <button class="bg-secondary text-white px-4 py-2 rounded-md text-sm hover:bg-gray-700" onclick="exportEquipment()">
                                <i class="fas fa-download mr-2"></i>导出数据
                            </button>
                        </div>
                    </div>
                    
                    <!-- 搜索和筛选 -->
                    <div class="flex flex-wrap gap-4 mt-4">
                        <select class="border border-gray-300 rounded-md px-3 py-2 text-sm" id="typeFilter">
                            <option>全部类型</option>
                            <option>生产设备</option>
                            <option>检测设备</option>
                            <option>物流设备</option>
                            <option>辅助设备</option>
                        </select>
                        <select class="border border-gray-300 rounded-md px-3 py-2 text-sm" id="statusFilter">
                            <option>全部状态</option>
                            <option>运行中</option>
                            <option>停机</option>
                            <option>维护中</option>
                            <option>故障</option>
                        </select>
                        <input type="text" placeholder="搜索设备编号、名称..." class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1 min-w-64" id="searchInput">
                        <button class="bg-primary text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700" onclick="searchEquipment()">
                            <i class="fas fa-search mr-1"></i>搜索
                        </button>
                    </div>
                </div>

                <!-- 设备数据表格 -->
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备编号</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备名称</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备类型</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">安装位置</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">制造商</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">运行状态</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后维护</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200" id="equipmentTableBody">
                            <!-- 设备数据将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
                    <div class="text-sm text-gray-700" id="equipmentRecordInfo">
                        显示记录信息
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-primary text-white rounded text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技术参数管理 -->
        <div id="parameters-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">技术参数管理</h3>
                <div class="mb-4">
                    <select class="border border-gray-300 rounded-md px-3 py-2 text-sm mr-4">
                        <option>选择设备</option>
                        <option>SMT贴片机-001</option>
                        <option>波峰焊机-002</option>
                        <option>AOI检测设备-003</option>
                    </select>
                    <button class="bg-primary text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700">
                        <i class="fas fa-eye mr-2"></i>查看参数
                    </button>
                </div>
                
                <!-- 技术参数示例 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-3">基本参数</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">额定功率</span>
                                <span class="text-gray-900">15KW</span>
                            </div>
                            <div class="flex justify-between p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">工作电压</span>
                                <span class="text-gray-900">380V</span>
                            </div>
                            <div class="flex justify-between p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">工作频率</span>
                                <span class="text-gray-900">50Hz</span>
                            </div>
                            <div class="flex justify-between p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">重量</span>
                                <span class="text-gray-900">2500kg</span>
                            </div>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-3">性能参数</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">贴装精度</span>
                                <span class="text-gray-900">±0.05mm</span>
                            </div>
                            <div class="flex justify-between p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">贴装速度</span>
                                <span class="text-gray-900">25000CPH</span>
                            </div>
                            <div class="flex justify-between p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">PCB尺寸</span>
                                <span class="text-gray-900">50×50~330×250mm</span>
                            </div>
                            <div class="flex justify-between p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">元件范围</span>
                                <span class="text-gray-900">0201~74×74mm</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 维护记录管理 -->
        <div id="maintenance-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">维护记录管理</h3>
                <div class="space-y-4">
                    <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-blue-800">定期保养</div>
                                <div class="text-xs text-gray-600">SMT贴片机-001 月度保养</div>
                                <div class="text-xs text-gray-500">维护时间: 2025-01-15 14:00-16:00</div>
                            </div>
                            <div class="text-right">
                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">已完成</span>
                                <div class="text-xs text-gray-500 mt-1">维护人员: 张工</div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg border-l-4 border-green-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-green-800">故障维修</div>
                                <div class="text-xs text-gray-600">波峰焊机-002 温度传感器更换</div>
                                <div class="text-xs text-gray-500">维护时间: 2025-01-12 09:00-11:30</div>
                            </div>
                            <div class="text-right">
                                <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">已完成</span>
                                <div class="text-xs text-gray-500 mt-1">维护人员: 李工</div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-orange-50 p-4 rounded-lg border-l-4 border-orange-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-orange-800">预防性维护</div>
                                <div class="text-xs text-gray-600">AOI检测设备-003 光源清洁</div>
                                <div class="text-xs text-gray-500">计划时间: 2025-01-20 13:00-15:00</div>
                            </div>
                            <div class="text-right">
                                <span class="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">计划中</span>
                                <div class="text-xs text-gray-500 mt-1">负责人员: 王工</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 备件信息管理 -->
        <div id="spareparts-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">备件信息管理</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- 传感器类 -->
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-gray-800">传感器类</h4>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">常用</span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">备件种类:</span>
                                <span class="text-gray-900">45种</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">库存数量:</span>
                                <span class="text-gray-900">256个</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">安全库存:</span>
                                <span class="text-green-600">充足</span>
                            </div>
                        </div>
                        <div class="mt-3 flex space-x-2">
                            <button class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded hover:bg-blue-200">查看详情</button>
                            <button class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded hover:bg-gray-200">管理</button>
                        </div>
                    </div>

                    <!-- 电机类 -->
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-gray-800">电机类</h4>
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">重要</span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">备件种类:</span>
                                <span class="text-gray-900">28种</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">库存数量:</span>
                                <span class="text-gray-900">89个</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">安全库存:</span>
                                <span class="text-orange-600">偏低</span>
                            </div>
                        </div>
                        <div class="mt-3 flex space-x-2">
                            <button class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded hover:bg-green-200">查看详情</button>
                            <button class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded hover:bg-gray-200">管理</button>
                        </div>
                    </div>

                    <!-- 气动元件 -->
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-gray-800">气动元件</h4>
                            <span class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">辅助</span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">备件种类:</span>
                                <span class="text-gray-900">67种</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">库存数量:</span>
                                <span class="text-gray-900">345个</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">安全库存:</span>
                                <span class="text-green-600">充足</span>
                            </div>
                        </div>
                        <div class="mt-3 flex space-x-2">
                            <button class="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded hover:bg-purple-200">查看详情</button>
                            <button class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded hover:bg-gray-200">管理</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 设备数据模型
        const equipmentData = [
            {
                id: 'EQP001',
                code: 'SMT-001',
                name: 'SMT贴片机',
                type: '生产设备',
                location: '产线A-工位1',
                manufacturer: '松下',
                status: 'running',
                statusName: '运行中',
                lastMaintenance: '2025-01-15'
            },
            {
                id: 'EQP002',
                code: 'WAVE-002',
                name: '波峰焊机',
                type: '生产设备',
                location: '产线A-工位3',
                manufacturer: '德国ERSA',
                status: 'running',
                statusName: '运行中',
                lastMaintenance: '2025-01-12'
            },
            {
                id: 'EQP003',
                code: 'AOI-003',
                name: 'AOI检测设备',
                type: '检测设备',
                location: '产线A-工位5',
                manufacturer: '奥普特',
                status: 'maintenance',
                statusName: '维护中',
                lastMaintenance: '2025-01-10'
            },
            {
                id: 'EQP004',
                code: 'AGV-001',
                name: 'AGV自动导引车',
                type: '物流设备',
                location: 'A区通道',
                manufacturer: '新松机器人',
                status: 'running',
                statusName: '运行中',
                lastMaintenance: '2025-01-08'
            }
        ];

        // 显示选项卡
        function showTab(tabName) {
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // 重置所有选项卡样式
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('border-green-500', 'text-green-600');
                button.classList.add('border-transparent', 'text-gray-500');
            });
            
            // 显示选中的内容
            document.getElementById(tabName + '-content').classList.remove('hidden');
            
            // 设置选中的选项卡样式
            const activeTab = document.getElementById(tabName + '-tab');
            activeTab.classList.remove('border-transparent', 'text-gray-500');
            activeTab.classList.add('border-green-500', 'text-green-600');

            // 根据选项卡类型渲染相应数据
            if (tabName === 'equipment') {
                renderEquipmentTable();
            }
        }

        // 渲染设备数据表格
        function renderEquipmentTable() {
            const tbody = document.getElementById('equipmentTableBody');
            tbody.innerHTML = '';

            equipmentData.forEach(equipment => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 transition-colors';
                
                const statusClass = equipment.status === 'running' ? 'bg-green-100 text-green-800' : 
                                  equipment.status === 'maintenance' ? 'bg-orange-100 text-orange-800' : 
                                  'bg-red-100 text-red-800';
                
                row.innerHTML = `
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-blue-600">${equipment.code}</div>
                        <div class="text-xs text-gray-500">ID: ${equipment.id}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${equipment.name}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <span class="text-sm text-gray-900">${equipment.type}</span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <span class="text-sm text-gray-900">${equipment.location}</span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <span class="text-sm text-gray-900">${equipment.manufacturer}</span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${statusClass}">
                            ${equipment.statusName}
                        </span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <span class="text-sm text-gray-900">${equipment.lastMaintenance}</span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-2">
                            <button onclick="editEquipment('${equipment.id}')" class="text-blue-600 hover:text-blue-900">编辑</button>
                            <button onclick="viewEquipment('${equipment.id}')" class="text-green-600 hover:text-green-900">查看</button>
                            <button onclick="deleteEquipment('${equipment.id}')" class="text-red-600 hover:text-red-900">删除</button>
                        </div>
                    </td>
                `;
                
                tbody.appendChild(row);
            });

            // 更新记录信息
            document.getElementById('equipmentRecordInfo').textContent = `显示 1-${equipmentData.length} 条，共 ${equipmentData.length} 条记录`;
        }

        // 操作函数
        function addEquipment() {
            alert('新增设备功能');
        }

        function importEquipment() {
            alert('批量导入设备功能');
        }

        function exportEquipment() {
            alert('导出设备数据功能');
        }

        function searchEquipment() {
            alert('搜索设备功能');
        }

        function editEquipment(id) {
            alert(`编辑设备: ${id}`);
        }

        function viewEquipment(id) {
            alert(`查看设备详情: ${id}`);
        }

        function deleteEquipment(id) {
            if (confirm(`确认删除设备: ${id}？`)) {
                alert(`已删除设备: ${id}`);
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            showTab('equipment');
        });
    </script>
</body>
</html>
