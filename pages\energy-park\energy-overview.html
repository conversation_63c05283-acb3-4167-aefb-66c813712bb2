<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>能源监控概览 - 慧新全智厂园一体平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #3b82f6;
            --primary-dark: #1e40af;
            --secondary: #64748b;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-tachometer-alt text-primary mr-3"></i>
                能源监控概览
            </h1>
            <p class="text-gray-600 mt-2">实时监控园区能源消耗，掌握能耗动态</p>
        </div>

        <!-- 能源概览卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">今日用电量</h3>
                        <p class="text-3xl font-bold text-yellow-600 mt-2">2,456</p>
                        <p class="text-sm text-gray-500">kWh</p>
                    </div>
                    <div class="bg-yellow-100 p-3 rounded-full">
                        <i class="fas fa-plug text-yellow-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>较昨日:</span>
                        <span class="text-green-600 font-medium">-5.2%</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>峰值时段:</span>
                        <span class="text-orange-600 font-medium">14:00-16:00</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">节能率</h3>
                        <p class="text-3xl font-bold text-green-600 mt-2">15.2%</p>
                        <p class="text-sm text-gray-500">较上月提升</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-leaf text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>目标:</span>
                        <span class="text-blue-600 font-medium">12%</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>完成度:</span>
                        <span class="text-green-600 font-medium">126.7%</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">光伏发电</h3>
                        <p class="text-3xl font-bold text-blue-600 mt-2">1,234</p>
                        <p class="text-sm text-gray-500">kWh</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-solar-panel text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>自用率:</span>
                        <span class="text-blue-600 font-medium">85.3%</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>上网电量:</span>
                        <span class="text-purple-600 font-medium">181 kWh</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">能耗告警</h3>
                        <p class="text-3xl font-bold text-red-600 mt-2">2</p>
                        <p class="text-sm text-gray-500">待处理</p>
                    </div>
                    <div class="bg-red-100 p-3 rounded-full">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>高级告警:</span>
                        <span class="text-red-600 font-medium">1个</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>一般告警:</span>
                        <span class="text-yellow-600 font-medium">1个</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时能耗监控 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-chart-line text-blue-600 mr-2"></i>
                    实时功率监控
                </h3>
                <div class="h-64 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg flex items-center justify-center">
                    <div class="text-center text-gray-500">
                        <i class="fas fa-chart-area text-4xl mb-4"></i>
                        <p>实时功率曲线图</p>
                        <p class="text-sm">当前功率: 1,850 kW</p>
                        <p class="text-sm text-green-600">负荷率: 74%</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-chart-pie text-purple-600 mr-2"></i>
                    能耗分布
                </h3>
                <div class="space-y-4">
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">生产设备</span>
                            <span class="text-lg font-bold text-blue-600">45%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-blue-500 h-3 rounded-full" style="width: 45%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">1,105 kWh</div>
                    </div>
                    <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">空调系统</span>
                            <span class="text-lg font-bold text-green-600">28%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-green-500 h-3 rounded-full" style="width: 28%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">688 kWh</div>
                    </div>
                    <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">照明系统</span>
                            <span class="text-lg font-bold text-yellow-600">15%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-yellow-500 h-3 rounded-full" style="width: 15%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">368 kWh</div>
                    </div>
                    <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">其他设备</span>
                            <span class="text-lg font-bold text-purple-600">12%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-purple-500 h-3 rounded-full" style="width: 12%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">295 kWh</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 能耗趋势分析 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-chart-area text-green-600 mr-2"></i>
                能耗趋势分析
            </h3>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="lg:col-span-2">
                    <div class="h-64 bg-gradient-to-r from-green-50 to-teal-50 rounded-lg flex items-center justify-center">
                        <div class="text-center text-gray-500">
                            <i class="fas fa-chart-line text-4xl mb-4"></i>
                            <p>7天能耗趋势图</p>
                            <p class="text-sm">显示近一周用电量变化</p>
                        </div>
                    </div>
                </div>
                <div class="space-y-4">
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">本周统计</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• 总用电量: 17,192 kWh</div>
                            <div>• 日均用电: 2,456 kWh</div>
                            <div>• 较上周: -3.8%</div>
                            <div class="text-green-600">• 节能效果显著</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">峰谷分析</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• 峰时用电: 35%</div>
                            <div>• 平时用电: 40%</div>
                            <div>• 谷时用电: 25%</div>
                            <div class="text-blue-600">• 谷电利用率良好</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 告警事件处理 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-exclamation-triangle text-red-600 mr-2"></i>
                告警事件处理
            </h3>
            <div class="space-y-4">
                <div class="border border-red-200 bg-red-50 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">高级告警</span>
                            <h4 class="font-semibold text-gray-800">生产车间A - 功率异常</h4>
                        </div>
                        <span class="text-sm text-gray-500">14:25</span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                        <div>
                            <span class="text-sm text-gray-600">当前功率:</span>
                            <p class="font-medium text-red-600">1,250 kW</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">正常范围:</span>
                            <p class="font-medium">800-1,000 kW</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">超标程度:</span>
                            <p class="font-medium text-red-600">+25%</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            <span>可能原因: 设备故障或负荷异常</span>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                                <i class="fas fa-tools mr-1"></i>立即处理
                            </button>
                            <button class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                <i class="fas fa-eye mr-1"></i>查看详情
                            </button>
                        </div>
                    </div>
                </div>

                <div class="border border-yellow-200 bg-yellow-50 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">一般告警</span>
                            <h4 class="font-semibold text-gray-800">办公楼B - 空调能耗偏高</h4>
                        </div>
                        <span class="text-sm text-gray-500">13:45</span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                        <div>
                            <span class="text-sm text-gray-600">当前能耗:</span>
                            <p class="font-medium text-yellow-600">180 kW</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">正常范围:</span>
                            <p class="font-medium">120-150 kW</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">超标程度:</span>
                            <p class="font-medium text-yellow-600">+20%</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            <span>建议: 调整空调温度设定或检查系统</span>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 transition-colors">
                                <i class="fas fa-cog mr-1"></i>调整设置
                            </button>
                            <button class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                <i class="fas fa-eye mr-1"></i>查看详情
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-bolt text-blue-600 mr-2"></i>
                快速操作
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button class="flex flex-col items-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
                    <i class="fas fa-eye text-blue-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">实时监控</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                    <i class="fas fa-cog text-green-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">参数设置</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors">
                    <i class="fas fa-bell text-purple-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">告警配置</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors">
                    <i class="fas fa-download text-orange-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">能耗报表</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 能源监控概览功能
        function initEnergyOverview() {
            console.log('初始化能源监控概览功能');
            
            // 告警处理按钮事件
            const alertButtons = document.querySelectorAll('button');
            alertButtons.forEach(button => {
                const text = button.textContent.trim();
                if (text.includes('立即处理')) {
                    button.addEventListener('click', function() {
                        const alertTitle = this.closest('.rounded-lg').querySelector('h4').textContent;
                        console.log('处理告警:', alertTitle);
                        alert(`正在处理告警: ${alertTitle}`);
                    });
                } else if (text.includes('调整设置')) {
                    button.addEventListener('click', function() {
                        const alertTitle = this.closest('.rounded-lg').querySelector('h4').textContent;
                        console.log('调整设置:', alertTitle);
                        alert(`正在调整设置: ${alertTitle}`);
                    });
                } else if (text.includes('查看详情')) {
                    button.addEventListener('click', function() {
                        const alertTitle = this.closest('.rounded-lg').querySelector('h4').textContent;
                        console.log('查看详情:', alertTitle);
                        alert(`查看详情: ${alertTitle}`);
                    });
                }
            });
        }

        // 实时数据更新
        function updateEnergyData() {
            console.log('更新能源监控数据');
            // 这里可以添加实时数据更新逻辑
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initEnergyOverview();
            updateEnergyData();
            setInterval(updateEnergyData, 30000); // 每30秒更新一次数据
            
            console.log('能源监控概览页面加载完成');
        });
    </script>
</body>
</html>
