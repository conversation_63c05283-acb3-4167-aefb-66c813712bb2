<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>运营中心概览 - 慧新全智厂园一体平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-desktop text-primary mr-3"></i>
                IOC运营中心概览
            </h1>
            <p class="text-gray-600 mt-2">智能运营中心，全面监控园区运行状态</p>
        </div>

        <!-- 核心运营指标 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">系统在线率</h3>
                        <p class="text-3xl font-bold text-green-600 mt-2">99.8%</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>在线系统:</span>
                        <span class="text-green-600 font-medium">127/128</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>连续运行:</span>
                        <span class="text-green-600 font-medium">365天</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">数据处理量</h3>
                        <p class="text-3xl font-bold text-blue-600 mt-2">2.5M</p>
                        <p class="text-sm text-gray-500">条/小时</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-database text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>较昨日:</span>
                        <span class="text-green-600 font-medium">+8.5%</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>峰值:</span>
                        <span class="text-blue-600 font-medium">3.2M条/小时</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">告警事件</h3>
                        <p class="text-3xl font-bold text-yellow-600 mt-2">12</p>
                        <p class="text-sm text-gray-500">今日</p>
                    </div>
                    <div class="bg-yellow-100 p-3 rounded-full">
                        <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>已处理:</span>
                        <span class="text-green-600 font-medium">10件</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>处理中:</span>
                        <span class="text-yellow-600 font-medium">2件</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">运营效率</h3>
                        <p class="text-3xl font-bold text-purple-600 mt-2">95.2%</p>
                    </div>
                    <div class="bg-purple-100 p-3 rounded-full">
                        <i class="fas fa-chart-line text-purple-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>目标:</span>
                        <span class="text-blue-600 font-medium">95%</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>较上月:</span>
                        <span class="text-green-600 font-medium">+2.1%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统运行状态 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-server text-blue-600 mr-2"></i>
                系统运行状态
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-200">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-800">生产管理系统</h4>
                        <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">正常</span>
                    </div>
                    <div class="text-sm text-gray-600 space-y-1">
                        <div>• CPU使用率: 65%</div>
                        <div>• 内存使用率: 72%</div>
                        <div>• 响应时间: 120ms</div>
                        <div>• 在线用户: 156人</div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-800">质量管理系统</h4>
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">正常</span>
                    </div>
                    <div class="text-sm text-gray-600 space-y-1">
                        <div>• CPU使用率: 58%</div>
                        <div>• 内存使用率: 68%</div>
                        <div>• 响应时间: 95ms</div>
                        <div>• 在线用户: 89人</div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-4 border border-yellow-200">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-800">设备管理系统</h4>
                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">警告</span>
                    </div>
                    <div class="text-sm text-gray-600 space-y-1">
                        <div>• CPU使用率: 85%</div>
                        <div>• 内存使用率: 88%</div>
                        <div>• 响应时间: 280ms</div>
                        <div>• 在线用户: 234人</div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-200">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-800">仓储管理系统</h4>
                        <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">正常</span>
                    </div>
                    <div class="text-sm text-gray-600 space-y-1">
                        <div>• CPU使用率: 45%</div>
                        <div>• 内存使用率: 52%</div>
                        <div>• 响应时间: 85ms</div>
                        <div>• 在线用户: 67人</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时监控面板 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-chart-area text-green-600 mr-2"></i>
                    实时数据流监控
                </h3>
                <div class="h-64 bg-gradient-to-r from-green-50 to-teal-50 rounded-lg flex items-center justify-center">
                    <div class="text-center text-gray-500">
                        <i class="fas fa-chart-line text-6xl mb-4 text-green-400"></i>
                        <p class="text-lg font-medium">实时数据流图表</p>
                        <p class="text-sm">数据处理量、系统负载、网络流量</p>
                        <div class="mt-4 flex justify-center space-x-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600">2.5M</div>
                                <div class="text-xs text-gray-500">数据/小时</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600">68%</div>
                                <div class="text-xs text-gray-500">系统负载</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-purple-600">1.2GB</div>
                                <div class="text-xs text-gray-500">网络流量</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-bell text-yellow-600 mr-2"></i>
                    告警事件处理
                </h3>
                <div class="space-y-4">
                    <div class="bg-gradient-to-r from-red-50 to-pink-50 rounded-lg p-4 border border-red-200">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-semibold text-gray-800">高优先级告警</h4>
                            <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">紧急</span>
                        </div>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• 设备管理系统CPU使用率过高</div>
                            <div>• 影响范围: 生产线3-5</div>
                            <div>• 发生时间: 14:25</div>
                            <div class="text-red-600">• 需要立即处理</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-4 border border-yellow-200">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-semibold text-gray-800">中优先级告警</h4>
                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">注意</span>
                        </div>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• 网络延迟异常增高</div>
                            <div>• 影响范围: 办公区域</div>
                            <div>• 发生时间: 13:45</div>
                            <div class="text-yellow-600">• 建议关注处理</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-200">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-semibold text-gray-800">系统状态</h4>
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">正常</span>
                        </div>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• 所有核心系统运行正常</div>
                            <div>• 数据备份完成</div>
                            <div>• 安全扫描通过</div>
                            <div class="text-green-600">• 系统稳定运行</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 运营统计分析 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-chart-pie text-purple-600 mr-2"></i>
                运营统计分析
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
                    <h4 class="font-semibold text-gray-800 mb-3">系统使用率</h4>
                    <div class="space-y-3">
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span>生产管理</span>
                                <span>85%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span>质量管理</span>
                                <span>72%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 72%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span>设备管理</span>
                                <span>95%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-purple-500 h-2 rounded-full" style="width: 95%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-200">
                    <h4 class="font-semibold text-gray-800 mb-3">性能指标</h4>
                    <div class="text-sm text-gray-600 space-y-2">
                        <div class="flex justify-between">
                            <span>平均响应时间:</span>
                            <span class="font-medium text-green-600">145ms</span>
                        </div>
                        <div class="flex justify-between">
                            <span>系统可用性:</span>
                            <span class="font-medium text-green-600">99.8%</span>
                        </div>
                        <div class="flex justify-between">
                            <span>并发用户数:</span>
                            <span class="font-medium">546人</span>
                        </div>
                        <div class="flex justify-between">
                            <span>数据同步率:</span>
                            <span class="font-medium text-blue-600">99.9%</span>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-200">
                    <h4 class="font-semibold text-gray-800 mb-3">运营趋势</h4>
                    <div class="text-sm text-gray-600 space-y-2">
                        <div class="flex justify-between">
                            <span>用户活跃度:</span>
                            <span class="font-medium text-green-600">+15%</span>
                        </div>
                        <div class="flex justify-between">
                            <span>系统负载:</span>
                            <span class="font-medium text-yellow-600">+8%</span>
                        </div>
                        <div class="flex justify-between">
                            <span>处理效率:</span>
                            <span class="font-medium text-green-600">+12%</span>
                        </div>
                        <div class="flex justify-between">
                            <span>故障率:</span>
                            <span class="font-medium text-green-600">-25%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-bolt text-blue-600 mr-2"></i>
                快速操作
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button class="flex flex-col items-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                    <i class="fas fa-tv text-green-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">监控大屏</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
                    <i class="fas fa-chart-bar text-blue-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">数据分析</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors">
                    <i class="fas fa-exclamation-triangle text-red-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">应急指挥</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors">
                    <i class="fas fa-cogs text-purple-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">系统运维</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // IOC运营中心概览功能
        function initOperationsOverview() {
            console.log('初始化IOC运营中心概览功能');
            
            // 快速操作按钮事件
            const quickButtons = document.querySelectorAll('button');
            quickButtons.forEach(button => {
                const text = button.textContent.trim();
                if (text.includes('监控大屏')) {
                    button.addEventListener('click', function() {
                        console.log('打开监控大屏');
                        alert('正在打开实时监控大屏...');
                    });
                } else if (text.includes('数据分析')) {
                    button.addEventListener('click', function() {
                        console.log('打开数据分析');
                        alert('正在打开数据分析报表...');
                    });
                } else if (text.includes('应急指挥')) {
                    button.addEventListener('click', function() {
                        console.log('打开应急指挥');
                        alert('正在打开应急指挥调度...');
                    });
                } else if (text.includes('系统运维')) {
                    button.addEventListener('click', function() {
                        console.log('打开系统运维');
                        alert('正在打开系统运维管理...');
                    });
                }
            });
            
            // 实时数据更新
            function updateOperationsData() {
                console.log('更新运营中心数据');
                // 这里可以添加实时数据更新逻辑
            }
            
            // 启动定时更新
            setInterval(updateOperationsData, 30000); // 每30秒更新一次
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initOperationsOverview();
            console.log('IOC运营中心概览页面加载完成');
        });
    </script>
</body>
</html>
