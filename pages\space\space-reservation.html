<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空间预约管理 - 慧新全智厂园一体平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-calendar-check text-primary mr-3"></i>
                空间预约管理
            </h1>
            <p class="text-gray-600 mt-2">智能空间预约系统，提升空间利用效率</p>
        </div>

        <!-- 预约统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">今日预约</h3>
                        <p class="text-3xl font-bold text-blue-600 mt-2">45</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-calendar-day text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>进行中:</span>
                        <span class="text-green-600 font-medium">18个</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>待开始:</span>
                        <span class="text-blue-600 font-medium">27个</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">预约率</h3>
                        <p class="text-3xl font-bold text-green-600 mt-2">78%</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-percentage text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>会议室:</span>
                        <span class="text-green-600 font-medium">85%</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>培训室:</span>
                        <span class="text-blue-600 font-medium">65%</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">取消率</h3>
                        <p class="text-3xl font-bold text-yellow-600 mt-2">8%</p>
                    </div>
                    <div class="bg-yellow-100 p-3 rounded-full">
                        <i class="fas fa-times-circle text-yellow-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>本周取消:</span>
                        <span class="text-yellow-600 font-medium">12个</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>目标:</span>
                        <span class="text-green-600 font-medium">≤5%</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">满意度</h3>
                        <p class="text-3xl font-bold text-purple-600 mt-2">4.6</p>
                    </div>
                    <div class="bg-purple-100 p-3 rounded-full">
                        <i class="fas fa-star text-purple-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>评价数:</span>
                        <span class="text-purple-600 font-medium">156条</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>好评率:</span>
                        <span class="text-green-600 font-medium">92%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时预约状态 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-clock text-green-600 mr-2"></i>
                实时预约状态
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-200">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-800">会议室A-201</h4>
                        <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">使用中</span>
                    </div>
                    <div class="text-sm text-gray-600 space-y-1">
                        <div>• 预约人: 张经理</div>
                        <div>• 时间: 14:00-16:00</div>
                        <div>• 参会: 8人</div>
                        <div>• 主题: 项目评审</div>
                    </div>
                    <div class="mt-3 flex space-x-2">
                        <button class="flex-1 px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700">
                            查看详情
                        </button>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-800">培训室B-301</h4>
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">已预约</span>
                    </div>
                    <div class="text-sm text-gray-600 space-y-1">
                        <div>• 预约人: 李主任</div>
                        <div>• 时间: 15:30-17:30</div>
                        <div>• 参会: 25人</div>
                        <div>• 主题: 安全培训</div>
                    </div>
                    <div class="mt-3 flex space-x-2">
                        <button class="flex-1 px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">
                            修改预约
                        </button>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg p-4 border border-gray-200">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-800">会议室C-102</h4>
                        <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">空闲</span>
                    </div>
                    <div class="text-sm text-gray-600 space-y-1">
                        <div>• 容量: 12人</div>
                        <div>• 设备: 投影仪</div>
                        <div>• 下次预约: 明日 09:00</div>
                        <div>• 状态: 可立即预约</div>
                    </div>
                    <div class="mt-3 flex space-x-2">
                        <button class="flex-1 px-3 py-1 bg-gray-600 text-white text-xs rounded hover:bg-gray-700">
                            立即预约
                        </button>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-red-50 to-pink-50 rounded-lg p-4 border border-red-200">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-800">多功能厅D-401</h4>
                        <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">维护中</span>
                    </div>
                    <div class="text-sm text-gray-600 space-y-1">
                        <div>• 容量: 50人</div>
                        <div>• 状态: 设备维护</div>
                        <div>• 预计恢复: 明日</div>
                        <div>• 暂不可预约</div>
                    </div>
                    <div class="mt-3 flex space-x-2">
                        <button class="flex-1 px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700">
                            维护详情
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 预约日历视图 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <div class="lg:col-span-2 bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-calendar text-blue-600 mr-2"></i>
                    预约日历
                </h3>
                <div class="h-96 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg flex items-center justify-center">
                    <div class="text-center text-gray-500">
                        <i class="fas fa-calendar-alt text-6xl mb-4 text-blue-400"></i>
                        <p class="text-lg font-medium">智能预约日历</p>
                        <p class="text-sm">可视化预约时间管理</p>
                        <div class="mt-4 flex justify-center space-x-4">
                            <button class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                                <i class="fas fa-plus mr-1"></i>新建预约
                            </button>
                            <button class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                                <i class="fas fa-search mr-1"></i>查找空闲
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-filter text-purple-600 mr-2"></i>
                    筛选条件
                </h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">空间类型</label>
                        <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <option>全部类型</option>
                            <option>会议室</option>
                            <option>培训室</option>
                            <option>多功能厅</option>
                            <option>活动室</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">容量要求</label>
                        <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <option>不限</option>
                            <option>1-5人</option>
                            <option>6-15人</option>
                            <option>16-30人</option>
                            <option>30人以上</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">设备需求</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600">
                                <span class="ml-2 text-sm text-gray-700">投影仪</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600">
                                <span class="ml-2 text-sm text-gray-700">音响设备</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600">
                                <span class="ml-2 text-sm text-gray-700">视频会议</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600">
                                <span class="ml-2 text-sm text-gray-700">白板</span>
                            </label>
                        </div>
                    </div>
                    <button class="w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors">
                        <i class="fas fa-search mr-1"></i>搜索空间
                    </button>
                </div>
            </div>
        </div>

        <!-- 预约申请处理 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-clipboard-check text-orange-600 mr-2"></i>
                预约申请处理
            </h3>
            <div class="space-y-4">
                <div class="border border-blue-200 bg-blue-50 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">待审批</span>
                            <h4 class="font-semibold text-gray-800">王总监 - 董事会会议</h4>
                        </div>
                        <span class="text-sm text-gray-500">申请时间: 2025-01-17 10:30</span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-3">
                        <div>
                            <span class="text-sm text-gray-600">申请空间:</span>
                            <p class="font-medium">多功能厅D-401</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">使用时间:</span>
                            <p class="font-medium">2025-01-18 14:00-17:00</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">参会人数:</span>
                            <p class="font-medium">35人</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">特殊需求:</span>
                            <p class="font-medium">视频会议设备</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            <span>会议主题: 年度战略规划讨论</span>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                                <i class="fas fa-check mr-1"></i>批准
                            </button>
                            <button class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                                <i class="fas fa-times mr-1"></i>拒绝
                            </button>
                            <button class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                <i class="fas fa-edit mr-1"></i>建议修改
                            </button>
                        </div>
                    </div>
                </div>

                <div class="border border-yellow-200 bg-yellow-50 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">冲突</span>
                            <h4 class="font-semibold text-gray-800">陈经理 - 客户洽谈</h4>
                        </div>
                        <span class="text-sm text-gray-500">申请时间: 2025-01-17 11:15</span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-3">
                        <div>
                            <span class="text-sm text-gray-600">申请空间:</span>
                            <p class="font-medium">会议室A-201</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">使用时间:</span>
                            <p class="font-medium">2025-01-17 15:00-17:00</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">参会人数:</span>
                            <p class="font-medium">6人</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">冲突原因:</span>
                            <p class="font-medium text-red-600">时间重叠</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            <span>建议: 推荐会议室C-102 (15:00-17:00可用)</span>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                <i class="fas fa-exchange-alt mr-1"></i>推荐替代
                            </button>
                            <button class="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 transition-colors">
                                <i class="fas fa-phone mr-1"></i>协调沟通
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-bolt text-blue-600 mr-2"></i>
                快速操作
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button class="flex flex-col items-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                    <i class="fas fa-plus text-green-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">新建预约</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
                    <i class="fas fa-search text-blue-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">查找空间</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors">
                    <i class="fas fa-calendar-alt text-purple-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">预约日历</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors">
                    <i class="fas fa-download text-orange-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">预约报表</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 空间预约管理功能
        function initSpaceReservation() {
            console.log('初始化空间预约管理功能');
            
            // 预约审批按钮事件
            const approvalButtons = document.querySelectorAll('button');
            approvalButtons.forEach(button => {
                const text = button.textContent.trim();
                if (text.includes('批准')) {
                    button.addEventListener('click', function() {
                        const applicantName = this.closest('.rounded-lg').querySelector('h4').textContent;
                        console.log('批准预约申请:', applicantName);
                        alert(`已批准 ${applicantName} 的预约申请`);
                    });
                } else if (text.includes('拒绝')) {
                    button.addEventListener('click', function() {
                        const applicantName = this.closest('.rounded-lg').querySelector('h4').textContent;
                        console.log('拒绝预约申请:', applicantName);
                        alert(`已拒绝 ${applicantName} 的预约申请`);
                    });
                } else if (text.includes('立即预约')) {
                    button.addEventListener('click', function() {
                        const roomName = this.closest('.rounded-lg').querySelector('h4').textContent;
                        console.log('立即预约:', roomName);
                        alert(`正在为您预约 ${roomName}...`);
                    });
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initSpaceReservation();
            console.log('空间预约管理页面加载完成');
        });
    </script>
</body>
</html>
