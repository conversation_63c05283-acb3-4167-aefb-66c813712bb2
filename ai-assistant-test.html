<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手功能测试 - 数字工厂一体化平台</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- AI助手样式 -->
    <link rel="stylesheet" href="assets/css/ai-assistant.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">AI助手功能测试</h1>
            <p class="text-gray-600">测试智能业务单据追踪AI助手的完整功能</p>
        </div>

        <!-- 功能说明 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">🤖 AI助手功能说明</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">使用方法</h4>
                        <ol class="text-sm text-gray-600 space-y-2">
                            <li>1. 点击右下角蓝色AI助手按钮</li>
                            <li>2. 在弹出的对话框中输入单据编号</li>
                            <li>3. 点击搜索按钮或按回车键</li>
                            <li>4. 查看完整的业务流程时间线</li>
                            <li>5. 点击任意节点跳转到对应模块</li>
                        </ol>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">测试用例</h4>
                        <div class="space-y-2 text-sm">
                            <div class="bg-blue-50 p-3 rounded-lg">
                                <div class="font-medium text-blue-800">需求计划追踪</div>
                                <div class="text-blue-600 font-mono">DM202501001</div>
                                <div class="text-xs text-blue-500">完整的9步业务流程</div>
                            </div>
                            <div class="bg-green-50 p-3 rounded-lg">
                                <div class="font-medium text-green-800">生产工单追踪</div>
                                <div class="text-green-600 font-mono">WO202501001</div>
                                <div class="text-xs text-green-500">生产过程质量检验</div>
                            </div>
                            <div class="bg-purple-50 p-3 rounded-lg">
                                <div class="font-medium text-purple-800">采购订单追踪</div>
                                <div class="text-purple-600 font-mono">PO202501001</div>
                                <div class="text-xs text-purple-500">采购到入库流程</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 支持的单据类型 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">📋 支持的单据类型</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="space-y-3">
                        <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                                <span class="text-white text-xs font-bold">DM</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-blue-800">需求计划</div>
                                <div class="text-xs text-blue-600">DM202501001</div>
                            </div>
                        </div>
                        <div class="flex items-center p-3 bg-green-50 rounded-lg">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                                <span class="text-white text-xs font-bold">MPS</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-green-800">主生产计划</div>
                                <div class="text-xs text-green-600">MPS202501001</div>
                            </div>
                        </div>
                        <div class="flex items-center p-3 bg-purple-50 rounded-lg">
                            <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center mr-3">
                                <span class="text-white text-xs font-bold">WO</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-purple-800">生产工单</div>
                                <div class="text-xs text-purple-600">WO202501001</div>
                            </div>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-center p-3 bg-orange-50 rounded-lg">
                            <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-3">
                                <span class="text-white text-xs font-bold">MR</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-orange-800">物料需求</div>
                                <div class="text-xs text-orange-600">MR202501001</div>
                            </div>
                        </div>
                        <div class="flex items-center p-3 bg-red-50 rounded-lg">
                            <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center mr-3">
                                <span class="text-white text-xs font-bold">PO</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-red-800">采购订单</div>
                                <div class="text-xs text-red-600">PO202501001</div>
                            </div>
                        </div>
                        <div class="flex items-center p-3 bg-teal-50 rounded-lg">
                            <div class="w-8 h-8 bg-teal-500 rounded-full flex items-center justify-center mr-3">
                                <span class="text-white text-xs font-bold">IN</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-teal-800">收货入库</div>
                                <div class="text-xs text-teal-600">IN202501001</div>
                            </div>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-center p-3 bg-indigo-50 rounded-lg">
                            <div class="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center mr-3">
                                <span class="text-white text-xs font-bold">FIN</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-indigo-800">成品入库</div>
                                <div class="text-xs text-indigo-600">FIN202501001</div>
                            </div>
                        </div>
                        <div class="flex items-center p-3 bg-pink-50 rounded-lg">
                            <div class="w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center mr-3">
                                <span class="text-white text-xs font-bold">SO</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-pink-800">销售订单</div>
                                <div class="text-xs text-pink-600">SO202501001</div>
                            </div>
                        </div>
                        <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                            <div class="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center mr-3">
                                <span class="text-white text-xs font-bold">OUT</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-800">成品出库</div>
                                <div class="text-xs text-gray-600">OUT202501001</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速测试按钮 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">🚀 快速测试</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="testAIAssistant('DM202501001')" 
                            class="p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-search mb-2"></i>
                        <div class="font-medium">测试需求计划</div>
                        <div class="text-xs opacity-80">DM202501001</div>
                    </button>
                    <button onclick="testAIAssistant('WO202501001')" 
                            class="p-4 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                        <i class="fas fa-cogs mb-2"></i>
                        <div class="font-medium">测试生产工单</div>
                        <div class="text-xs opacity-80">WO202501001</div>
                    </button>
                    <button onclick="testAIAssistant('SO202501001')" 
                            class="p-4 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                        <i class="fas fa-shopping-cart mb-2"></i>
                        <div class="font-medium">测试销售订单</div>
                        <div class="text-xs opacity-80">SO202501001</div>
                    </button>
                </div>
            </div>
        </div>

        <!-- 功能特性 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">✨ 功能特性</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-robot text-blue-600 text-xl"></i>
                        </div>
                        <h4 class="font-semibold text-gray-800 mb-2">智能识别</h4>
                        <p class="text-sm text-gray-600">自动识别单据类型，智能匹配业务流程</p>
                    </div>
                    <div class="text-center">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-route text-green-600 text-xl"></i>
                        </div>
                        <h4 class="font-semibold text-gray-800 mb-2">流程追踪</h4>
                        <p class="text-sm text-gray-600">完整展示从需求到交付的业务链路</p>
                    </div>
                    <div class="text-center">
                        <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-clock text-purple-600 text-xl"></i>
                        </div>
                        <h4 class="font-semibold text-gray-800 mb-2">实时状态</h4>
                        <p class="text-sm text-gray-600">动态显示业务节点执行状态</p>
                    </div>
                    <div class="text-center">
                        <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-mouse-pointer text-orange-600 text-xl"></i>
                        </div>
                        <h4 class="font-semibold text-gray-800 mb-2">一键导航</h4>
                        <p class="text-sm text-gray-600">点击节点直接跳转到对应模块</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 提示信息 -->
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div class="flex items-start">
                <i class="fas fa-info-circle text-blue-600 text-xl mr-4 mt-1"></i>
                <div>
                    <h3 class="text-lg font-semibold text-blue-800 mb-2">使用提示</h3>
                    <ul class="text-blue-700 space-y-1">
                        <li>• 右下角的蓝色圆形按钮就是AI助手入口</li>
                        <li>• 支持键盘快捷键：ESC关闭对话框，Enter搜索</li>
                        <li>• 点击时间线上的任意节点可以跳转到对应业务模块</li>
                        <li>• 支持响应式设计，在手机和平板上也能正常使用</li>
                        <li>• 当前使用模拟数据演示，实际部署时可接入真实业务数据</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 快速测试函数
        function testAIAssistant(docNumber) {
            // 等待AI助手加载完成
            setTimeout(() => {
                if (window.digitalFactoryAI) {
                    // 打开AI助手对话框
                    digitalFactoryAI.openDialog();
                    
                    // 填入测试单据编号
                    setTimeout(() => {
                        const input = document.getElementById('ai-input');
                        if (input) {
                            input.value = docNumber;
                            input.focus();
                        }
                    }, 200);
                } else {
                    alert('AI助手正在加载中，请稍后再试...');
                }
            }, 100);
        }

        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AI助手测试页面已加载');
            console.log('请点击右下角的AI助手按钮开始测试');
        });
    </script>
    
    <!-- AI助手脚本 -->
    <script src="assets/js/ai-assistant.js"></script>
</body>
</html>
