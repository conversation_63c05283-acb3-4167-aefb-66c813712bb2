<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访客预约审批 - 慧新全智厂园一体平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #3b82f6;
            --primary-dark: #1e40af;
            --secondary: #64748b;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-user-check text-primary mr-3"></i>
                访客预约审批
            </h1>
            <p class="text-gray-600 mt-2">管理访客预约申请，确保园区访问安全有序</p>
        </div>

        <!-- 访客预约统计 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">待审批预约</h3>
                        <p class="text-3xl font-bold text-orange-600 mt-2">12</p>
                    </div>
                    <div class="bg-orange-100 p-3 rounded-full">
                        <i class="fas fa-clock text-orange-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>紧急审批:</span>
                        <span class="text-red-600 font-medium">3个</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>普通审批:</span>
                        <span class="text-blue-600 font-medium">9个</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">今日到访</h3>
                        <p class="text-3xl font-bold text-green-600 mt-2">8</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-user-friends text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>已到达:</span>
                        <span class="text-green-600 font-medium">6人</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>预计到达:</span>
                        <span class="text-blue-600 font-medium">2人</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">本月通过</h3>
                        <p class="text-3xl font-bold text-blue-600 mt-2">156</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-check-circle text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>通过率:</span>
                        <span class="text-green-600 font-medium">94.5%</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>拒绝:</span>
                        <span class="text-red-600 font-medium">9个</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">平均审批时间</h3>
                        <p class="text-3xl font-bold text-purple-600 mt-2">2.5小时</p>
                    </div>
                    <div class="bg-purple-100 p-3 rounded-full">
                        <i class="fas fa-stopwatch text-purple-600 text-xl"></i>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>最快:</span>
                        <span class="text-green-600 font-medium">30分钟</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span>目标:</span>
                        <span class="text-blue-600 font-medium">≤4小时</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 待审批预约列表 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-list-alt text-orange-600 mr-2"></i>
                待审批预约列表
            </h3>
            <div class="space-y-4">
                <div class="border border-red-200 bg-red-50 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">紧急</span>
                            <h4 class="font-semibold text-gray-800">张明 - 华为技术有限公司</h4>
                        </div>
                        <span class="text-sm text-gray-500">申请时间: 2025-01-17 09:30</span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                        <div>
                            <span class="text-sm text-gray-600">访问目的:</span>
                            <p class="font-medium">技术交流与合作洽谈</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">被访人:</span>
                            <p class="font-medium">李工程师 - 技术部</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">预计访问时间:</span>
                            <p class="font-medium">2025-01-17 14:00-17:00</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            <span>联系电话: 138****8888</span>
                            <span class="ml-4">身份证: 320***********1234</span>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                                <i class="fas fa-check mr-1"></i>批准
                            </button>
                            <button class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                                <i class="fas fa-times mr-1"></i>拒绝
                            </button>
                            <button class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                <i class="fas fa-eye mr-1"></i>详情
                            </button>
                        </div>
                    </div>
                </div>

                <div class="border border-blue-200 bg-blue-50 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">普通</span>
                            <h4 class="font-semibold text-gray-800">王丽 - 中科院自动化所</h4>
                        </div>
                        <span class="text-sm text-gray-500">申请时间: 2025-01-17 10:15</span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                        <div>
                            <span class="text-sm text-gray-600">访问目的:</span>
                            <p class="font-medium">学术交流与参观</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">被访人:</span>
                            <p class="font-medium">陈主任 - 研发中心</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">预计访问时间:</span>
                            <p class="font-medium">2025-01-18 09:00-12:00</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            <span>联系电话: 139****9999</span>
                            <span class="ml-4">身份证: 110***********5678</span>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                                <i class="fas fa-check mr-1"></i>批准
                            </button>
                            <button class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                                <i class="fas fa-times mr-1"></i>拒绝
                            </button>
                            <button class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                <i class="fas fa-eye mr-1"></i>详情
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 今日访客到访情况 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-calendar-day text-green-600 mr-2"></i>
                    今日访客到访
                </h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div>
                            <h4 class="font-semibold text-gray-800">刘强 - 阿里巴巴</h4>
                            <p class="text-sm text-gray-600">商务洽谈 | 被访人: 张总监</p>
                            <p class="text-xs text-gray-500">09:30 已到达</p>
                        </div>
                        <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">已到达</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div>
                            <h4 class="font-semibold text-gray-800">赵敏 - 腾讯科技</h4>
                            <p class="text-sm text-gray-600">技术交流 | 被访人: 李工程师</p>
                            <p class="text-xs text-gray-500">14:00 预计到达</p>
                        </div>
                        <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">预约中</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div>
                            <h4 class="font-semibold text-gray-800">孙伟 - 百度公司</h4>
                            <p class="text-sm text-gray-600">产品演示 | 被访人: 王经理</p>
                            <p class="text-xs text-gray-500">15:30 预计到达</p>
                        </div>
                        <span class="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded-full">待确认</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-chart-pie text-purple-600 mr-2"></i>
                    访客统计分析
                </h3>
                <div class="space-y-4">
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">商务访客</span>
                            <span class="text-lg font-bold text-blue-600">45%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: 45%"></div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">技术交流</span>
                            <span class="text-lg font-bold text-green-600">35%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: 35%"></div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">参观学习</span>
                            <span class="text-lg font-bold text-purple-600">20%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-purple-500 h-2 rounded-full" style="width: 20%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-bolt text-blue-600 mr-2"></i>
                快速操作
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button class="flex flex-col items-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                    <i class="fas fa-user-plus text-green-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">新增预约</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
                    <i class="fas fa-check-double text-blue-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">批量审批</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors">
                    <i class="fas fa-qrcode text-purple-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">访客签到</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors">
                    <i class="fas fa-download text-orange-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-gray-800">导出报表</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 访客预约审批功能
        function initVisitorReservationManagement() {
            console.log('初始化访客预约审批功能');
            
            // 审批按钮事件
            const approvalButtons = document.querySelectorAll('button');
            approvalButtons.forEach(button => {
                const text = button.textContent.trim();
                if (text.includes('批准')) {
                    button.addEventListener('click', function() {
                        const visitorName = this.closest('.rounded-lg').querySelector('h4').textContent;
                        console.log('批准访客预约:', visitorName);
                        alert(`已批准 ${visitorName} 的访客预约申请`);
                    });
                } else if (text.includes('拒绝')) {
                    button.addEventListener('click', function() {
                        const visitorName = this.closest('.rounded-lg').querySelector('h4').textContent;
                        console.log('拒绝访客预约:', visitorName);
                        alert(`已拒绝 ${visitorName} 的访客预约申请`);
                    });
                } else if (text.includes('详情')) {
                    button.addEventListener('click', function() {
                        const visitorName = this.closest('.rounded-lg').querySelector('h4').textContent;
                        console.log('查看访客详情:', visitorName);
                        alert(`查看 ${visitorName} 的详细信息...`);
                    });
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initVisitorReservationManagement();
            console.log('访客预约审批页面加载完成');
        });
    </script>
</body>
</html>
