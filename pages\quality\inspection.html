<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>质量检验 - 数字工厂一体化平台</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">质量检验</h1>
            <p class="text-gray-600">管理产品质量检验流程，确保产品质量符合标准</p>
        </div>

        <!-- 操作按钮 -->
        <div class="flex flex-wrap gap-4 mb-6">
            <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                <i class="fas fa-plus mr-2"></i>
                新建检验单
            </button>
            <button class="bg-success text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
                <i class="fas fa-download mr-2"></i>
                导出检验报告
            </button>
            <button class="bg-secondary text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center">
                <i class="fas fa-filter mr-2"></i>
                高级筛选
            </button>
        </div>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="text-2xl font-bold text-blue-600">18</div>
                <div class="text-sm text-gray-600">待检验</div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="text-2xl font-bold text-green-600">142</div>
                <div class="text-sm text-gray-600">检验合格</div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="text-2xl font-bold text-red-600">5</div>
                <div class="text-sm text-gray-600">检验不合格</div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="text-2xl font-bold text-yellow-600">3</div>
                <div class="text-sm text-gray-600">复检中</div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="text-2xl font-bold text-purple-600">96.7%</div>
                <div class="text-sm text-gray-600">合格率</div>
            </div>
        </div>

        <!-- 检验单列表 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">检验单列表</h3>
                <div class="flex flex-wrap gap-4">
                    <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option>全部状态</option>
                        <option>待检验</option>
                        <option>检验中</option>
                        <option>检验合格</option>
                        <option>检验不合格</option>
                        <option>复检中</option>
                    </select>
                    <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option>全部产品</option>
                        <option>智能手机外壳</option>
                        <option>平板电脑屏幕</option>
                        <option>笔记本键盘</option>
                    </select>
                    <input type="text" placeholder="搜索检验单号或产品..." class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1 min-w-64">
                </div>
            </div>

            <!-- 表格 -->
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检验单号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">批次号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检验数量</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检验员</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检验时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">QC-2024-001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">智能手机外壳</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">BATCH-001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">100</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李检验员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-06-28 09:30</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">检验合格</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-900" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-purple-600 hover:text-purple-900" title="打印报告">
                                        <i class="fas fa-print"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">QC-2024-002</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">平板电脑屏幕</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">BATCH-002</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">50</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王检验员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-06-28 14:15</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">检验中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-900" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-orange-600 hover:text-orange-900" title="完成检验">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">QC-2024-003</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">笔记本键盘</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">BATCH-003</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">80</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张检验员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-06-27 16:45</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">检验不合格</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-900" title="不合格处理">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </button>
                                    <button class="text-purple-600 hover:text-purple-900" title="打印报告">
                                        <i class="fas fa-print"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    显示 1-3 条，共 15 条记录
                </div>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">上一页</button>
                    <button class="px-3 py-1 bg-primary text-white rounded text-sm">1</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">2</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">3</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">下一页</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
