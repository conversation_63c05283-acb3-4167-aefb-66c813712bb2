<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>巡检任务执行 - 数字工厂平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../../assets/css/custom.css">
</head>
<body class="bg-gray-50">
    <div class="p-6 space-y-6">
        <!-- 页面操作栏 -->
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-xl font-bold text-gray-800">巡检任务执行</h1>
                <p class="text-gray-600">管理巡检任务的分配、执行和结果记录</p>
            </div>
            <div class="flex space-x-3">
                <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>创建任务
                </button>
                <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-qrcode mr-2"></i>扫码巡检
                </button>
                <button class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                    <i class="fas fa-chart-bar mr-2"></i>执行统计
                </button>
            </div>
        </div>
        
        <!-- 任务概览统计 -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div class="card text-center">
                <div class="text-2xl font-bold text-blue-600 mb-1">28</div>
                <div class="text-sm text-gray-600">待执行任务</div>
                <div class="text-xs text-blue-600 mt-1">今日计划</div>
            </div>
            <div class="card text-center">
                <div class="text-2xl font-bold text-yellow-600 mb-1">15</div>
                <div class="text-sm text-gray-600">执行中任务</div>
                <div class="text-xs text-yellow-600 mt-1">平均进度45%</div>
            </div>
            <div class="card text-center">
                <div class="text-2xl font-bold text-green-600 mb-1">42</div>
                <div class="text-sm text-gray-600">今日完成</div>
                <div class="text-xs text-green-600 mt-1">
                    <i class="fas fa-arrow-up mr-1"></i>+8 较昨日
                </div>
            </div>
            <div class="card text-center">
                <div class="text-2xl font-bold text-red-600 mb-1">3</div>
                <div class="text-sm text-gray-600">异常发现</div>
                <div class="text-xs text-red-600 mt-1">需要处理</div>
            </div>
            <div class="card text-center">
                <div class="text-2xl font-bold text-purple-600 mb-1">96.8%</div>
                <div class="text-sm text-gray-600">按时完成率</div>
                <div class="text-xs text-green-600 mt-1">
                    <i class="fas fa-arrow-up mr-1"></i>+2.3% 较上月
                </div>
            </div>
        </div>
        
        <!-- 筛选条件区域 -->
        <div class="card">
            <div class="p-4">
                <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                    <div class="flex-1">
                        <input type="text" placeholder="搜索任务编号、巡检员..." 
                               class="w-full border border-gray-300 rounded-lg px-4 py-2">
                    </div>
                    <select class="border border-gray-300 rounded-lg px-3 py-2">
                        <option>全部状态</option>
                        <option>待执行</option>
                        <option>执行中</option>
                        <option>已完成</option>
                        <option>异常</option>
                    </select>
                    <select class="border border-gray-300 rounded-lg px-3 py-2">
                        <option>全部路线</option>
                        <option>注塑车间路线</option>
                        <option>装配车间路线</option>
                        <option>包装车间路线</option>
                        <option>公用设施路线</option>
                    </select>
                    <select class="border border-gray-300 rounded-lg px-3 py-2">
                        <option>全部巡检员</option>
                        <option>张巡检员</option>
                        <option>李巡检员</option>
                        <option>王巡检员</option>
                        <option>陈巡检员</option>
                    </select>
                    <select class="border border-gray-300 rounded-lg px-3 py-2">
                        <option>全部班次</option>
                        <option>早班</option>
                        <option>中班</option>
                        <option>晚班</option>
                    </select>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 巡检任务列表 -->
        <div class="card">
            <div class="card-header">
                <div class="flex justify-between items-center">
                    <h3 class="card-title">巡检任务列表</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 bg-blue-600 text-white rounded text-sm">
                            <i class="fas fa-list"></i>
                        </button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">
                            <i class="fas fa-th"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>任务编号</th>
                            <th>巡检路线</th>
                            <th>巡检员</th>
                            <th>班次</th>
                            <th>状态</th>
                            <th>进度</th>
                            <th>开始时间</th>
                            <th>预计完成</th>
                            <th>异常数量</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="bg-blue-50">
                            <td class="font-medium">IT-2024-001</td>
                            <td>注塑车间路线A</td>
                            <td>张巡检员</td>
                            <td>早班</td>
                            <td><span class="status-indicator status-warning">待执行</span></td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="progress-bar w-16">
                                        <div class="progress-fill bg-gray-400" style="width: 0%"></div>
                                    </div>
                                    <span class="text-sm">0%</span>
                                </div>
                            </td>
                            <td>-</td>
                            <td>2024-06-28 10:00</td>
                            <td>0</td>
                            <td>
                                <div class="flex space-x-1">
                                    <button class="text-blue-600 hover:text-blue-800" title="开始巡检">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-800" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-purple-600 hover:text-purple-800" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        
                        <tr class="bg-yellow-50">
                            <td class="font-medium">IT-2024-002</td>
                            <td>装配车间路线B</td>
                            <td>李巡检员</td>
                            <td>早班</td>
                            <td><span class="status-indicator status-info">执行中</span></td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="progress-bar w-16">
                                        <div class="progress-fill" style="width: 65%"></div>
                                    </div>
                                    <span class="text-sm">65%</span>
                                </div>
                            </td>
                            <td>2024-06-28 08:30</td>
                            <td>2024-06-28 10:30</td>
                            <td>1</td>
                            <td>
                                <div class="flex space-x-1">
                                    <button class="text-yellow-600 hover:text-yellow-800" title="更新进度">
                                        <i class="fas fa-tasks"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-800" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-blue-600 hover:text-blue-800" title="联系巡检员">
                                        <i class="fas fa-phone"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        
                        <tr class="bg-yellow-50">
                            <td class="font-medium">IT-2024-003</td>
                            <td>包装车间路线C</td>
                            <td>王巡检员</td>
                            <td>早班</td>
                            <td><span class="status-indicator status-info">执行中</span></td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="progress-bar w-16">
                                        <div class="progress-fill" style="width: 30%"></div>
                                    </div>
                                    <span class="text-sm">30%</span>
                                </div>
                            </td>
                            <td>2024-06-28 09:15</td>
                            <td>2024-06-28 11:15</td>
                            <td>0</td>
                            <td>
                                <div class="flex space-x-1">
                                    <button class="text-yellow-600 hover:text-yellow-800" title="更新进度">
                                        <i class="fas fa-tasks"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-800" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-blue-600 hover:text-blue-800" title="联系巡检员">
                                        <i class="fas fa-phone"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        
                        <tr class="bg-green-50">
                            <td class="font-medium">IT-2024-004</td>
                            <td>公用设施路线D</td>
                            <td>陈巡检员</td>
                            <td>早班</td>
                            <td><span class="status-indicator status-success">已完成</span></td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="progress-bar w-16">
                                        <div class="progress-fill bg-green-500" style="width: 100%"></div>
                                    </div>
                                    <span class="text-sm">100%</span>
                                </div>
                            </td>
                            <td>2024-06-28 07:00</td>
                            <td>2024-06-28 08:45</td>
                            <td>0</td>
                            <td>
                                <div class="flex space-x-1">
                                    <button class="text-blue-600 hover:text-blue-800" title="查看报告">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-800" title="评价">
                                        <i class="fas fa-star"></i>
                                    </button>
                                    <button class="text-gray-600 hover:text-gray-800" title="归档">
                                        <i class="fas fa-archive"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        
                        <tr class="bg-red-50">
                            <td class="font-medium">IT-2024-005</td>
                            <td>注塑车间路线B</td>
                            <td>张巡检员</td>
                            <td>中班</td>
                            <td><span class="status-indicator status-danger">异常</span></td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="progress-bar w-16">
                                        <div class="progress-fill bg-red-500" style="width: 80%"></div>
                                    </div>
                                    <span class="text-sm">80%</span>
                                </div>
                            </td>
                            <td>2024-06-28 14:00</td>
                            <td>2024-06-28 16:00</td>
                            <td class="text-red-600 font-medium">2</td>
                            <td>
                                <div class="flex space-x-1">
                                    <button class="text-red-600 hover:text-red-800" title="处理异常">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </button>
                                    <button class="text-blue-600 hover:text-blue-800" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-800" title="联系主管">
                                        <i class="fas fa-phone"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="flex justify-between items-center mt-4 pt-4 border-t border-gray-200">
                <div class="text-sm text-gray-600">
                    显示 1-5 条，共 88 条巡检任务
                </div>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">上一页</button>
                    <button class="px-3 py-1 bg-blue-600 text-white rounded text-sm">1</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">2</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">3</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">下一页</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
