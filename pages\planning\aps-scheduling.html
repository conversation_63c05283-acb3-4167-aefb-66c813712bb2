<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日排程(APS) - 计划管理 - 数字工厂一体化平台</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">日排程(APS)系统</h1>
            <p class="text-gray-600">基于Process.md 2.1.18流程：滚动N天排程制定(4个控制点)→生产部门确认→下发MES系统指导生产</p>
        </div>

        <!-- APS流程状态指示器 -->
        <div class="mb-6">
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">APS排程制定流程</h3>
                    <span class="text-sm text-gray-600">滚动7天排程计划</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-6">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                            <span class="ml-2 text-sm font-medium text-gray-900">工单汇总</span>
                        </div>
                        <div class="flex-1 h-1 bg-gray-200 mx-2">
                            <div class="h-1 bg-primary" style="width: 100%"></div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium">2</div>
                            <span class="ml-2 text-sm font-medium text-gray-900">产能匹配</span>
                        </div>
                        <div class="flex-1 h-1 bg-gray-200 mx-2">
                            <div class="h-1 bg-primary" style="width: 80%"></div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">3</div>
                            <span class="ml-2 text-sm text-gray-900">排程优化</span>
                        </div>
                        <div class="flex-1 h-1 bg-gray-200 mx-2">
                            <div class="h-1 bg-blue-500" style="width: 60%"></div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">4</div>
                            <span class="ml-2 text-sm text-gray-600">生产确认</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 顶部操作区域 -->
        <div class="flex flex-wrap gap-4 mb-6">
            <button id="generateScheduleBtn" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                <i class="fas fa-calendar-plus mr-2"></i>
                生成排程
            </button>
            <button id="optimizeScheduleBtn" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors flex items-center">
                <i class="fas fa-brain mr-2"></i>
                智能优化
            </button>
            <button id="confirmScheduleBtn" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center">
                <i class="fas fa-user-check mr-2"></i>
                生产确认
            </button>
            <button id="releaseMESBtn" class="bg-success text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
                <i class="fas fa-paper-plane mr-2"></i>
                下发MES
            </button>
            <button id="adjustScheduleBtn" class="bg-warning text-white px-4 py-2 rounded-lg hover:bg-yellow-600 transition-colors flex items-center">
                <i class="fas fa-edit mr-2"></i>
                调整排程
            </button>
            <button class="bg-secondary text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center">
                <i class="fas fa-download mr-2"></i>
                导出排程
            </button>
        </div>

        <!-- APS排程统计卡片 - 基于Process.md定义的排程指标 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-blue-600">89</div>
                        <div class="text-sm text-gray-600">待排程工单</div>
                        <div class="text-xs text-gray-500">已下达工单</div>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clipboard-list text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-indigo-600">7</div>
                        <div class="text-sm text-gray-600">排程天数</div>
                        <div class="text-xs text-gray-500">滚动计划</div>
                    </div>
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-calendar-week text-indigo-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-green-600">92%</div>
                        <div class="text-sm text-gray-600">产能利用率</div>
                        <div class="text-xs text-gray-500">设备+人员</div>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-pie text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-orange-600">15</div>
                        <div class="text-sm text-gray-600">今日计划</div>
                        <div class="text-xs text-gray-500">生产任务</div>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-calendar-day text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-red-600">3</div>
                        <div class="text-sm text-gray-600">延期风险</div>
                        <div class="text-xs text-gray-500">需要关注</div>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold text-purple-600">78%</div>
                        <div class="text-sm text-gray-600">执行进度</div>
                        <div class="text-xs text-gray-500">当前周期</div>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 排程控制面板 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">排程控制面板</h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">排程开始日期</label>
                    <input type="date" value="2025-01-16" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">排程天数</label>
                    <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option value="7">7天</option>
                        <option value="14">14天</option>
                        <option value="30">30天</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">产线选择</label>
                    <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option>全部产线</option>
                        <option>逆变器产线1</option>
                        <option>逆变器产线2</option>
                        <option>控制器产线</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">排程策略</label>
                    <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option>优先级优先</option>
                        <option>交期优先</option>
                        <option>产能均衡</option>
                        <option>成本最优</option>
                    </select>
                </div>
            </div>
            <div class="mt-4 flex space-x-3">
                <button class="bg-primary text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors">
                    <i class="fas fa-play mr-2"></i>
                    生成排程
                </button>
                <button class="bg-success text-white px-6 py-2 rounded-md hover:bg-green-700 transition-colors">
                    <i class="fas fa-paper-plane mr-2"></i>
                    下发MES
                </button>
                <button class="bg-secondary text-white px-6 py-2 rounded-md hover:bg-gray-700 transition-colors">
                    <i class="fas fa-eye mr-2"></i>
                    甘特图视图
                </button>
            </div>
        </div>

        <!-- 排程计划表 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">7天滚动排程计划</h3>
                <div class="flex flex-wrap gap-4">
                    <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option>全部状态</option>
                        <option>计划中</option>
                        <option>已下发</option>
                        <option>执行中</option>
                        <option>已完成</option>
                        <option>延期</option>
                    </select>
                    <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option>今天</option>
                        <option>明天</option>
                        <option>本周</option>
                        <option>下周</option>
                    </select>
                    <input type="text" placeholder="搜索工单号、产品名称..." class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1 min-w-64">
                    <button class="bg-primary text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700">
                        <i class="fas fa-search mr-1"></i>搜索
                    </button>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="rounded">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">排程编号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工单号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产线</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">计划数量</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">排程日期</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">开始时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">结束时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">优先级</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-blue-600 cursor-pointer hover:underline">APS202501001</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-blue-600 cursor-pointer hover:underline">WO202501001</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">5KW逆变器</div>
                                <div class="text-xs text-gray-500">批次: 30台</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">逆变器产线1</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">30台</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">2025-01-16</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">08:00</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">16:00</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                    高
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    执行中
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-orange-600 hover:text-orange-900" title="调整排程">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-900" title="查看进度">
                                        <i class="fas fa-chart-line"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-blue-600 cursor-pointer hover:underline">APS202501002</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-blue-600 cursor-pointer hover:underline">WO202501001</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">5KW逆变器</div>
                                <div class="text-xs text-gray-500">批次: 35台</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">逆变器产线1</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">35台</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">2025-01-17</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">08:00</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">17:00</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                    高
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    已下发
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-orange-600 hover:text-orange-900" title="调整排程">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-gray-400" title="未开始" disabled>
                                        <i class="fas fa-chart-line"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-blue-600 cursor-pointer hover:underline">APS202501003</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-blue-600 cursor-pointer hover:underline">WO202501003</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">高级控制器</div>
                                <div class="text-xs text-gray-500">批次: 10台</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">控制器产线</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">10台</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">2025-01-17</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">14:00</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">18:00</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    中
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    计划中
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-orange-600 hover:text-orange-900" title="调整排程">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-900" title="下发MES">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    显示 1-10 条，共 42 条记录
                </div>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">上一页</button>
                    <button class="px-3 py-1 bg-primary text-white rounded text-sm">1</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">2</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">3</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 基于Process.md 2.1.18的APS排程数据模型
        const scheduleData = [
            {
                id: 'APS202501001',
                workOrderId: 'WO202501001',
                productName: '5KW逆变器',
                productCode: 'INV-5KW-001',
                batchSize: 30,
                quantity: 30,
                productionLine: '逆变器产线1',
                scheduleDate: '2025-01-16',
                startTime: '08:00',
                endTime: '16:00',
                priority: 'high',
                status: 'executing',
                progress: 65,
                responsible: '张主管',
                estimatedDuration: 8,
                actualDuration: 5.2,
                capacityUtilization: 85,
                createTime: '2025-01-15 18:00'
            },
            {
                id: 'APS202501002',
                workOrderId: 'WO202501001',
                productName: '5KW逆变器',
                productCode: 'INV-5KW-001',
                batchSize: 35,
                quantity: 35,
                productionLine: '逆变器产线1',
                scheduleDate: '2025-01-17',
                startTime: '08:00',
                endTime: '17:00',
                priority: 'high',
                status: 'released',
                progress: 0,
                responsible: '张主管',
                estimatedDuration: 9,
                actualDuration: 0,
                capacityUtilization: 90,
                createTime: '2025-01-15 18:00'
            },
            {
                id: 'APS202501003',
                workOrderId: 'WO202501003',
                productName: '高级控制器',
                productCode: 'CTRL-ADV-003',
                batchSize: 10,
                quantity: 10,
                productionLine: '控制器产线',
                scheduleDate: '2025-01-17',
                startTime: '14:00',
                endTime: '18:00',
                priority: 'medium',
                status: 'planning',
                progress: 0,
                responsible: '王主管',
                estimatedDuration: 4,
                actualDuration: 0,
                capacityUtilization: 75,
                createTime: '2025-01-15 18:00'
            },
            {
                id: 'APS202501004',
                workOrderId: 'WO202501002',
                productName: '储能逆变器',
                productCode: 'ESS-10KW-002',
                batchSize: 25,
                quantity: 25,
                productionLine: '逆变器产线2',
                scheduleDate: '2025-01-18',
                startTime: '08:00',
                endTime: '16:00',
                priority: 'urgent',
                status: 'planning',
                progress: 0,
                responsible: '李主管',
                estimatedDuration: 8,
                actualDuration: 0,
                capacityUtilization: 95,
                createTime: '2025-01-15 18:00'
            },
            {
                id: 'APS202501005',
                workOrderId: 'WO202501004',
                productName: '3KW逆变器',
                productCode: 'INV-3KW-004',
                batchSize: 5,
                quantity: 5,
                productionLine: '逆变器产线1',
                scheduleDate: '2025-01-19',
                startTime: '09:00',
                endTime: '12:00',
                priority: 'urgent',
                status: 'completed',
                progress: 100,
                responsible: '赵主管',
                estimatedDuration: 3,
                actualDuration: 2.8,
                capacityUtilization: 60,
                createTime: '2025-01-14 16:00'
            }
        ];

        // 状态映射
        const statusMap = {
            planning: { text: '计划中', class: 'bg-yellow-100 text-yellow-800', icon: 'fas fa-clock' },
            released: { text: '已下发', class: 'bg-green-100 text-green-800', icon: 'fas fa-paper-plane' },
            executing: { text: '执行中', class: 'bg-blue-100 text-blue-800', icon: 'fas fa-play' },
            completed: { text: '已完成', class: 'bg-gray-100 text-gray-800', icon: 'fas fa-check-circle' },
            delayed: { text: '延期', class: 'bg-red-100 text-red-800', icon: 'fas fa-exclamation-triangle' },
            paused: { text: '暂停', class: 'bg-orange-100 text-orange-800', icon: 'fas fa-pause' }
        };

        // 优先级映射
        const priorityMap = {
            urgent: { text: '紧急', class: 'bg-red-100 text-red-800', icon: 'fas fa-exclamation-triangle' },
            high: { text: '高', class: 'bg-orange-100 text-orange-800', icon: 'fas fa-arrow-up' },
            medium: { text: '中', class: 'bg-yellow-100 text-yellow-800', icon: 'fas fa-minus' },
            low: { text: '低', class: 'bg-gray-100 text-gray-800', icon: 'fas fa-arrow-down' }
        };

        let filteredData = [...scheduleData];

        // 渲染排程表格
        function renderScheduleTable(dataToRender = filteredData) {
            const tbody = document.querySelector('tbody');
            if (!tbody) return;

            tbody.innerHTML = '';

            dataToRender.forEach(schedule => {
                const status = statusMap[schedule.status];
                const priority = priorityMap[schedule.priority];
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 transition-colors';

                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <input type="checkbox" class="rounded schedule-checkbox" data-id="${schedule.id}">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-blue-600 cursor-pointer hover:underline" onclick="viewScheduleDetail('${schedule.id}')">
                            ${schedule.id}
                        </div>
                        <div class="text-xs text-gray-500">${schedule.createTime}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-blue-600 cursor-pointer hover:underline" onclick="viewWorkOrderDetail('${schedule.workOrderId}')">
                            ${schedule.workOrderId}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${schedule.productName}</div>
                        <div class="text-xs text-gray-500">批次: ${schedule.batchSize}台</div>
                        <div class="text-xs text-gray-500">${schedule.productCode}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${schedule.productionLine}</div>
                        <div class="text-xs text-gray-500">${schedule.responsible}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-center">
                        <span class="text-sm font-medium text-gray-900">${schedule.quantity}台</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="text-sm text-gray-900">${schedule.scheduleDate}</span>
                        ${isDateToday(schedule.scheduleDate) ? '<div class="text-xs text-blue-600"><i class="fas fa-star mr-1"></i>今日</div>' : ''}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="text-sm text-gray-900">${schedule.startTime}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="text-sm text-gray-900">${schedule.endTime}</span>
                        <div class="text-xs text-gray-500">${schedule.estimatedDuration}h预计</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${priority.class}">
                            <i class="${priority.icon} mr-1"></i>
                            ${priority.text}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${status.class}">
                            <i class="${status.icon} mr-1"></i>
                            ${status.text}
                        </span>
                        ${schedule.progress > 0 ? `
                            <div class="mt-1 flex items-center">
                                <div class="w-12 bg-gray-200 rounded-full h-1 mr-1">
                                    <div class="bg-blue-600 h-1 rounded-full" style="width: ${schedule.progress}%"></div>
                                </div>
                                <span class="text-xs text-gray-600">${schedule.progress}%</span>
                            </div>
                        ` : ''}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-1">
                            <button onclick="viewScheduleDetail('${schedule.id}')" class="text-blue-600 hover:text-blue-900 p-1" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            ${schedule.status === 'planning' ? `
                                <button onclick="releaseToMES('${schedule.id}')" class="text-green-600 hover:text-green-900 p-1" title="下发MES">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            ` : ''}
                            ${schedule.status !== 'completed' ? `
                                <button onclick="adjustSchedule('${schedule.id}')" class="text-orange-600 hover:text-orange-900 p-1" title="调整排程">
                                    <i class="fas fa-edit"></i>
                                </button>
                            ` : ''}
                            ${schedule.status === 'executing' ? `
                                <button onclick="viewProgress('${schedule.id}')" class="text-purple-600 hover:text-purple-900 p-1" title="查看进度">
                                    <i class="fas fa-chart-line"></i>
                                </button>
                            ` : ''}
                            <button onclick="viewGanttChart('${schedule.id}')" class="text-indigo-600 hover:text-indigo-900 p-1" title="甘特图">
                                <i class="fas fa-chart-gantt"></i>
                            </button>
                        </div>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // 判断是否为今天
        function isDateToday(dateStr) {
            const today = new Date().toISOString().split('T')[0];
            return dateStr === today;
        }

        // APS操作函数 - 基于Process.md的关键控制点
        function viewScheduleDetail(scheduleId) {
            const schedule = scheduleData.find(s => s.id === scheduleId);
            if (schedule) {
                alert(`排程详情：\n编号：${schedule.id}\n工单：${schedule.workOrderId}\n产品：${schedule.productName}\n产线：${schedule.productionLine}\n数量：${schedule.quantity}台\n排程日期：${schedule.scheduleDate}\n时间：${schedule.startTime} - ${schedule.endTime}\n预计工时：${schedule.estimatedDuration}小时\n产能利用率：${schedule.capacityUtilization}%\n负责人：${schedule.responsible}`);
            }
        }

        function releaseToMES(scheduleId) {
            if (confirm('确认下发到MES系统？下发后将指导车间生产执行。')) {
                const schedule = scheduleData.find(s => s.id === scheduleId);
                if (schedule) {
                    schedule.status = 'released';
                    renderScheduleTable();
                    alert('排程已下发到MES系统！车间将按计划执行生产。');
                }
            }
        }

        function adjustSchedule(scheduleId) {
            if (confirm('确认调整排程？调整可能影响后续排程计划。')) {
                const schedule = scheduleData.find(s => s.id === scheduleId);
                if (schedule) {
                    // 模拟调整
                    const newTime = prompt('请输入新的开始时间 (HH:MM):', schedule.startTime);
                    if (newTime) {
                        schedule.startTime = newTime;
                        renderScheduleTable();
                        alert('排程时间已调整！');
                    }
                }
            }
        }

        function viewProgress(scheduleId) {
            const schedule = scheduleData.find(s => s.id === scheduleId);
            if (schedule) {
                alert(`生产进度：\n排程编号：${schedule.id}\n当前进度：${schedule.progress}%\n实际用时：${schedule.actualDuration}小时\n预计用时：${schedule.estimatedDuration}小时\n产能利用率：${schedule.capacityUtilization}%\n状态：${statusMap[schedule.status].text}`);
            }
        }

        function viewGanttChart(scheduleId) {
            alert('甘特图功能：\n- 可视化排程时间线\n- 显示产线负荷情况\n- 识别排程冲突\n- 优化排程安排');
        }

        // 生成排程
        function generateSchedule() {
            if (confirm('确认生成新的排程计划？将基于当前工单和产能情况重新排程。')) {
                // 模拟排程生成过程
                const progressDiv = document.createElement('div');
                progressDiv.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                progressDiv.innerHTML = `
                    <div class="bg-white p-6 rounded-lg shadow-lg">
                        <h3 class="text-lg font-semibold mb-4">正在生成排程...</h3>
                        <div class="w-64 bg-gray-200 rounded-full h-2 mb-4">
                            <div id="scheduleProgress" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                        <div id="scheduleStep" class="text-sm text-gray-600">准备开始...</div>
                    </div>
                `;
                document.body.appendChild(progressDiv);

                const steps = [
                    { text: '汇总待排程工单...', progress: 25 },
                    { text: '匹配产能资源...', progress: 50 },
                    { text: '优化排程算法...', progress: 75 },
                    { text: '生成排程计划...', progress: 100 }
                ];

                let currentStep = 0;
                const interval = setInterval(() => {
                    if (currentStep < steps.length) {
                        const step = steps[currentStep];
                        document.getElementById('scheduleProgress').style.width = step.progress + '%';
                        document.getElementById('scheduleStep').textContent = step.text;
                        currentStep++;
                    } else {
                        clearInterval(interval);
                        document.body.removeChild(progressDiv);
                        renderScheduleTable();
                        alert('排程生成完成！已优化产能利用率和交期匹配。');
                    }
                }, 1000);
            }
        }

        // 事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            renderScheduleTable();

            // 生成排程
            document.getElementById('generateScheduleBtn').addEventListener('click', generateSchedule);

            // 智能优化
            document.getElementById('optimizeScheduleBtn').addEventListener('click', function() {
                alert('智能优化功能：\n- AI算法优化排程\n- 最小化换线时间\n- 平衡产线负荷\n- 优化交期匹配');
            });

            // 生产确认
            document.getElementById('confirmScheduleBtn').addEventListener('click', function() {
                const checkedBoxes = document.querySelectorAll('.schedule-checkbox:checked');
                if (checkedBoxes.length === 0) {
                    alert('请选择要确认的排程计划！');
                    return;
                }

                if (confirm(`确认选中的 ${checkedBoxes.length} 个排程计划？确认后将锁定排程。`)) {
                    checkedBoxes.forEach(checkbox => {
                        const scheduleId = checkbox.dataset.id;
                        const schedule = scheduleData.find(s => s.id === scheduleId);
                        if (schedule && schedule.status === 'planning') {
                            schedule.status = 'released';
                        }
                    });
                    renderScheduleTable();
                    alert('排程计划已确认！');
                }
            });

            // 下发MES
            document.getElementById('releaseMESBtn').addEventListener('click', function() {
                const checkedBoxes = document.querySelectorAll('.schedule-checkbox:checked');
                if (checkedBoxes.length === 0) {
                    alert('请选择要下发的排程计划！');
                    return;
                }

                if (confirm(`确认下发选中的 ${checkedBoxes.length} 个排程到MES系统？`)) {
                    checkedBoxes.forEach(checkbox => {
                        const scheduleId = checkbox.dataset.id;
                        const schedule = scheduleData.find(s => s.id === scheduleId);
                        if (schedule && schedule.status === 'planning') {
                            schedule.status = 'released';
                        }
                    });
                    renderScheduleTable();
                    alert('排程已下发到MES系统！');
                }
            });

            // 调整排程
            document.getElementById('adjustScheduleBtn').addEventListener('click', function() {
                alert('排程调整功能：\n- 拖拽调整时间\n- 产线重新分配\n- 优先级调整\n- 批量时间调整');
            });

            // 全选功能
            const selectAllCheckbox = document.querySelector('thead input[type="checkbox"]');
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    const checkboxes = document.querySelectorAll('.schedule-checkbox');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                });
            }
        });
    </script>
</body>
</html>
