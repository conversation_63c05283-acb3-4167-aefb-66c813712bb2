# 数字工厂一体化平台 - 智能业务单据追踪AI助手

## 🤖 功能概述

智能业务单据追踪AI助手是数字工厂一体化平台v1.0.0的核心创新功能，实现了"一单到底"的业务流程追踪能力。通过AI助手，用户可以快速追踪任意业务单据在整个制造流程中的完整生命周期。

## ✨ 主要特性

### 🎯 核心功能
- **智能单据识别**: 支持多种单据类型的自动识别和追踪
- **完整流程追踪**: 展示从需求计划到最终交付的完整业务链路
- **实时状态更新**: 动态显示每个业务节点的当前状态
- **一键导航**: 点击任意节点直接跳转到对应业务模块

### 🎨 用户体验
- **悬浮式设计**: 右下角悬浮按钮，不干扰主要业务操作
- **响应式布局**: 完美适配桌面端、平板和移动设备
- **直观时间线**: 垂直时间线清晰展示业务流程进展
- **状态可视化**: 颜色编码和图标快速识别单据状态

## 📋 支持的单据类型

### 业务单据编号规则
| 前缀 | 单据类型 | 示例编号 | 说明 |
|------|----------|----------|------|
| DM | 需求计划 | DM202501001 | 客户需求或销售预测 |
| MPS | 主生产计划 | MPS202501001 | 主生产计划安排 |
| WO | 生产工单 | WO202501001 | 生产制造工单 |
| MR | 物料需求 | MR202501001 | 物料需求计划 |
| PO | 采购订单 | PO202501001 | 采购申请订单 |
| IN | 收货入库 | IN202501001 | 原料收货入库 |
| FIN | 成品入库 | FIN202501001 | 成品完工入库 |
| SO | 销售订单 | SO202501001 | 客户销售订单 |
| OUT | 成品出库 | OUT202501001 | 成品发货出库 |

## 🚀 使用指南

### 启动AI助手
1. 在任意平台页面右下角找到蓝色圆形AI助手按钮
2. 点击按钮打开智能单据追踪对话框
3. 在输入框中输入业务单据编号
4. 点击搜索按钮或按回车键开始追踪

### 操作示例
```
输入示例: DM202501001
功能: 追踪需求计划DM202501001的完整业务流程
```

### 时间线解读
- **绿色圆圈**: 已完成的业务节点
- **橙色圆圈**: 正在进行的业务节点
- **灰色圆圈**: 待开始的业务节点
- **红色圆圈**: 已取消或异常的业务节点

## 📊 业务流程演示

### 完整业务链路示例
以需求编号 `DM202501001` 为例，展示变频器生产的完整业务流程：

```
1. 需求计划 (DM202501001)
   ↓
2. 主生产计划 (MPS202501001)
   ↓
3. 生产工单 (WO202501001)
   ↓
4. 物料需求 (MR202501001)
   ↓
5. 采购订单 (PO202501001)
   ↓
6. 收货入库 (IN202501001)
   ↓
7. 成品入库 (FIN202501001)
   ↓
8. 销售订单 (SO202501001)
   ↓
9. 成品出库 (OUT202501001)
```

### 时间线详细信息
每个时间节点包含以下信息：
- **业务时间**: 精确到分钟的业务发生时间
- **单据类型**: 中文业务名称
- **单据编号**: 具体的业务单据编号
- **执行状态**: 当前业务节点的执行状态
- **负责人员**: 具体的业务执行人员
- **关键数据**: 数量、金额等关键业务信息

## 🛠️ 技术实现

### 前端技术栈
- **HTML5**: 现代Web标准
- **Tailwind CSS**: 响应式设计框架
- **JavaScript ES6+**: 现代JavaScript特性
- **FontAwesome**: 图标库

### 核心特性
- **纯前端实现**: 无需后端支持，部署简单
- **模拟数据驱动**: 基于真实业务场景的模拟数据
- **响应式设计**: 适配多种设备屏幕
- **模块化架构**: 易于维护和扩展

### 文件结构
```
assets/
├── css/
│   └── ai-assistant.css          # AI助手样式文件
├── js/
│   ├── ai-assistant.js           # AI助手核心逻辑
│   └── ai-assistant-integration.js # 通用集成脚本
```

## 🎯 业务价值

### 管理效益
- **流程透明化**: 完整展示业务流程执行情况
- **问题快速定位**: 快速识别业务流程中的瓶颈和问题
- **决策支持**: 为管理决策提供实时业务数据
- **效率提升**: 减少人工查询和跟踪的工作量

### 用户价值
- **操作便捷**: 一键查询，无需在多个系统间切换
- **信息集中**: 所有相关业务信息集中展示
- **实时更新**: 业务状态实时同步更新
- **移动友好**: 支持移动设备随时随地查询

## 📱 响应式设计

### 桌面端 (>1024px)
- 对话框尺寸: 400px × 600px
- 位置: 右下角向上偏移显示
- 完整功能展示

### 平板端 (768px - 1024px)
- 对话框自适应屏幕宽度
- 保持核心功能完整性
- 优化触摸操作体验

### 移动端 (<768px)
- 对话框占据大部分屏幕空间
- AI按钮适当缩小
- 优化单手操作体验

## 🔮 扩展功能

### 当前版本 (v1.0.0)
- ✅ 基础单据追踪功能
- ✅ 时间线可视化展示
- ✅ 响应式设计
- ✅ 模拟数据演示

### 未来版本规划
- 🔄 集成真实业务数据
- 🔄 支持更多单据类型
- 🔄 添加业务流程分析
- 🔄 实现智能预警功能
- 🔄 支持语音交互
- 🔄 添加数据导出功能

## 🚀 快速集成

### 在现有页面中集成AI助手

#### 方法一：直接引入（推荐）
```html
<!-- 在页面head中添加样式 -->
<link rel="stylesheet" href="../assets/css/ai-assistant.css">

<!-- 在页面底部添加脚本 -->
<script src="../assets/js/ai-assistant.js"></script>
```

#### 方法二：使用集成脚本
```html
<!-- 只需引入集成脚本，自动处理路径 -->
<script src="../assets/js/ai-assistant-integration.js"></script>
```

### 自定义配置
```javascript
// 页面加载完成后自定义AI助手
document.addEventListener('DOMContentLoaded', function() {
    if (window.digitalFactoryAI) {
        // 可以在这里添加自定义配置
        console.log('AI助手已加载');
    }
});
```

## 📞 技术支持

### 常见问题
1. **AI助手按钮不显示**: 检查CSS文件是否正确加载
2. **点击无响应**: 检查JavaScript文件是否正确加载
3. **样式异常**: 确认Tailwind CSS已正确引入
4. **数据不显示**: 检查浏览器控制台是否有错误信息

### 调试方法
```javascript
// 在浏览器控制台中检查AI助手状态
console.log('AI助手实例:', window.digitalFactoryAI);
console.log('AI助手是否已初始化:', !!window.digitalFactoryAI);
```

---

**数字工厂一体化平台AI助手** - 让业务流程追踪更智能、更高效！ 🤖✨
