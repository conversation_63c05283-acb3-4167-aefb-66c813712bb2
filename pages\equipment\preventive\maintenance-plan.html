<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>维护计划制定 - 数字工厂平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../../assets/css/custom.css">
</head>
<body class="bg-gray-50">
    <div class="p-6 space-y-6">
        <!-- 页面操作栏 -->
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-xl font-bold text-gray-800">维护计划制定</h1>
                <p class="text-gray-600">制定和管理设备预防性维护计划，优化维护策略</p>
            </div>
            <div class="flex space-x-3">
                <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>新建计划
                </button>
                <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-copy mr-2"></i>复制计划
                </button>
                <button class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                    <i class="fas fa-magic mr-2"></i>智能推荐
                </button>
                <button class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                    <i class="fas fa-calendar mr-2"></i>计划日历
                </button>
            </div>
        </div>
        
        <!-- 计划概览统计 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="card text-center">
                <div class="text-2xl font-bold text-blue-600 mb-1">28</div>
                <div class="text-sm text-gray-600">活跃计划</div>
                <div class="text-xs text-green-600 mt-1">
                    <i class="fas fa-check mr-1"></i>执行率95%
                </div>
            </div>
            <div class="card text-center">
                <div class="text-2xl font-bold text-yellow-600 mb-1">5</div>
                <div class="text-sm text-gray-600">待审核计划</div>
                <div class="text-xs text-yellow-600 mt-1">需要批准</div>
            </div>
            <div class="card text-center">
                <div class="text-2xl font-bold text-green-600 mb-1">156</div>
                <div class="text-sm text-gray-600">设备覆盖数</div>
                <div class="text-xs text-green-600 mt-1">覆盖率100%</div>
            </div>
            <div class="card text-center">
                <div class="text-2xl font-bold text-purple-600 mb-1">¥45.6K</div>
                <div class="text-sm text-gray-600">月度预算</div>
                <div class="text-xs text-green-600 mt-1">
                    <i class="fas fa-arrow-down mr-1"></i>-8.5% 较上月
                </div>
            </div>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 计划模板库 -->
            <div class="lg:col-span-1">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">计划模板库</h3>
                    </div>
                    <div class="p-4 space-y-3">
                        <div class="p-3 bg-blue-50 border border-blue-200 rounded-lg cursor-pointer hover:bg-blue-100" onclick="selectTemplate('injection')">
                            <div class="flex justify-between items-start mb-2">
                                <div class="font-medium text-blue-800">注塑机维护模板</div>
                                <i class="fas fa-star text-yellow-500"></i>
                            </div>
                            <div class="text-sm text-gray-600 mb-2">
                                适用设备：注塑机系列
                            </div>
                            <div class="text-xs text-gray-600 mb-2">
                                维护项目：15项 | 周期：7天
                            </div>
                            <div class="text-xs text-blue-600">
                                使用次数：45次 | 评分：4.8分
                            </div>
                        </div>
                        
                        <div class="p-3 bg-green-50 border border-green-200 rounded-lg cursor-pointer hover:bg-green-100" onclick="selectTemplate('assembly')">
                            <div class="flex justify-between items-start mb-2">
                                <div class="font-medium text-green-800">装配机维护模板</div>
                                <i class="fas fa-star text-yellow-500"></i>
                            </div>
                            <div class="text-sm text-gray-600 mb-2">
                                适用设备：装配机系列
                            </div>
                            <div class="text-xs text-gray-600 mb-2">
                                维护项目：12项 | 周期：14天
                            </div>
                            <div class="text-xs text-green-600">
                                使用次数：32次 | 评分：4.6分
                            </div>
                        </div>
                        
                        <div class="p-3 bg-purple-50 border border-purple-200 rounded-lg cursor-pointer hover:bg-purple-100" onclick="selectTemplate('packaging')">
                            <div class="flex justify-between items-start mb-2">
                                <div class="font-medium text-purple-800">包装机维护模板</div>
                                <i class="fas fa-star text-yellow-500"></i>
                            </div>
                            <div class="text-sm text-gray-600 mb-2">
                                适用设备：包装机系列
                            </div>
                            <div class="text-xs text-gray-600 mb-2">
                                维护项目：10项 | 周期：21天
                            </div>
                            <div class="text-xs text-purple-600">
                                使用次数：28次 | 评分：4.5分
                            </div>
                        </div>
                        
                        <div class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg cursor-pointer hover:bg-yellow-100" onclick="selectTemplate('testing')">
                            <div class="flex justify-between items-start mb-2">
                                <div class="font-medium text-yellow-800">检测设备维护模板</div>
                                <i class="fas fa-star text-yellow-500"></i>
                            </div>
                            <div class="text-sm text-gray-600 mb-2">
                                适用设备：检测设备系列
                            </div>
                            <div class="text-xs text-gray-600 mb-2">
                                维护项目：8项 | 周期：30天
                            </div>
                            <div class="text-xs text-yellow-600">
                                使用次数：18次 | 评分：4.7分
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm">
                                <i class="fas fa-plus mr-2"></i>创建新模板
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 计划列表和详情 -->
            <div class="lg:col-span-2 space-y-6">
                <!-- 维护计划列表 -->
                <div class="card">
                    <div class="card-header">
                        <div class="flex justify-between items-center">
                            <h3 class="card-title">维护计划列表</h3>
                            <div class="flex space-x-3">
                                <select class="border border-gray-300 rounded px-3 py-1 text-sm">
                                    <option>全部状态</option>
                                    <option>活跃</option>
                                    <option>暂停</option>
                                    <option>草稿</option>
                                </select>
                                <select class="border border-gray-300 rounded px-3 py-1 text-sm">
                                    <option>全部设备</option>
                                    <option>注塑设备</option>
                                    <option>装配设备</option>
                                    <option>包装设备</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>计划编号</th>
                                    <th>计划名称</th>
                                    <th>设备名称</th>
                                    <th>维护类型</th>
                                    <th>维护周期</th>
                                    <th>下次执行</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr onclick="selectPlan('PM001')" class="cursor-pointer hover:bg-blue-50">
                                    <td class="font-medium">PM-001</td>
                                    <td>注塑机A1日常保养</td>
                                    <td>注塑机A1</td>
                                    <td>日常保养</td>
                                    <td>7天</td>
                                    <td>2024-07-02</td>
                                    <td><span class="status-indicator status-success">活跃</span></td>
                                    <td>
                                        <div class="flex space-x-1">
                                            <button class="text-blue-600 hover:text-blue-800" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-green-600 hover:text-green-800" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-purple-600 hover:text-purple-800" title="生成工单">
                                                <i class="fas fa-plus-circle"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr onclick="selectPlan('PM002')" class="cursor-pointer hover:bg-blue-50">
                                    <td class="font-medium">PM-002</td>
                                    <td>装配机B1定期检查</td>
                                    <td>装配机B1</td>
                                    <td>定期检查</td>
                                    <td>14天</td>
                                    <td>2024-07-05</td>
                                    <td><span class="status-indicator status-success">活跃</span></td>
                                    <td>
                                        <div class="flex space-x-1">
                                            <button class="text-blue-600 hover:text-blue-800" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-green-600 hover:text-green-800" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-purple-600 hover:text-purple-800" title="生成工单">
                                                <i class="fas fa-plus-circle"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr onclick="selectPlan('PM003')" class="cursor-pointer hover:bg-blue-50 bg-yellow-50">
                                    <td class="font-medium">PM-003</td>
                                    <td>包装机C1部件更换</td>
                                    <td>包装机C1</td>
                                    <td>部件更换</td>
                                    <td>30天</td>
                                    <td>2024-06-30</td>
                                    <td><span class="status-indicator status-warning">到期</span></td>
                                    <td>
                                        <div class="flex space-x-1">
                                            <button class="text-blue-600 hover:text-blue-800" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-green-600 hover:text-green-800" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-orange-600 hover:text-orange-800" title="立即执行">
                                                <i class="fas fa-play"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr onclick="selectPlan('PM004')" class="cursor-pointer hover:bg-blue-50">
                                    <td class="font-medium">PM-004</td>
                                    <td>检测设备D1校准维护</td>
                                    <td>检测设备D1</td>
                                    <td>校准维护</td>
                                    <td>90天</td>
                                    <td>2024-08-15</td>
                                    <td><span class="status-indicator status-success">活跃</span></td>
                                    <td>
                                        <div class="flex space-x-1">
                                            <button class="text-blue-600 hover:text-blue-800" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-green-600 hover:text-green-800" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-purple-600 hover:text-purple-800" title="生成工单">
                                                <i class="fas fa-plus-circle"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 计划详情 -->
                <div class="card" id="plan-detail" style="display: none;">
                    <div class="card-header">
                        <h3 class="card-title">计划详情 - 注塑机A1日常保养</h3>
                    </div>
                    <div class="p-4">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- 基本信息 -->
                            <div>
                                <h4 class="text-lg font-semibold mb-4">基本信息</h4>
                                <div class="space-y-3 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">计划编号：</span>
                                        <span class="font-medium">PM-001</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">计划名称：</span>
                                        <span class="font-medium">注塑机A1日常保养</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">设备名称：</span>
                                        <span class="font-medium">注塑机A1</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">维护类型：</span>
                                        <span class="font-medium">日常保养</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">维护周期：</span>
                                        <span class="font-medium">7天</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">预计时长：</span>
                                        <span class="font-medium">2小时</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">负责人：</span>
                                        <span class="font-medium">张师傅</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 维护项目 -->
                            <div>
                                <h4 class="text-lg font-semibold mb-4">维护项目清单</h4>
                                <div class="space-y-2 text-sm">
                                    <div class="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                                        <i class="fas fa-check-circle text-green-600"></i>
                                        <span>检查液压油位和质量</span>
                                    </div>
                                    <div class="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                                        <i class="fas fa-check-circle text-green-600"></i>
                                        <span>清洁注塑机表面和内部</span>
                                    </div>
                                    <div class="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                                        <i class="fas fa-check-circle text-green-600"></i>
                                        <span>检查温度控制系统</span>
                                    </div>
                                    <div class="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                                        <i class="fas fa-check-circle text-green-600"></i>
                                        <span>润滑导轨和传动部件</span>
                                    </div>
                                    <div class="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                                        <i class="fas fa-check-circle text-green-600"></i>
                                        <span>检查安全装置功能</span>
                                    </div>
                                    <div class="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                                        <i class="fas fa-check-circle text-green-600"></i>
                                        <span>记录运行参数</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 执行历史 -->
                        <div class="mt-6">
                            <h4 class="text-lg font-semibold mb-4">最近执行记录</h4>
                            <div class="overflow-x-auto">
                                <table class="w-full text-sm">
                                    <thead>
                                        <tr class="border-b border-gray-200">
                                            <th class="text-left py-2">执行日期</th>
                                            <th class="text-left py-2">执行人</th>
                                            <th class="text-left py-2">用时</th>
                                            <th class="text-left py-2">结果</th>
                                            <th class="text-left py-2">备注</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="border-b border-gray-100">
                                            <td class="py-2">2024-06-25</td>
                                            <td class="py-2">张师傅</td>
                                            <td class="py-2">1.8小时</td>
                                            <td class="py-2"><span class="text-green-600">正常</span></td>
                                            <td class="py-2">设备运行良好</td>
                                        </tr>
                                        <tr class="border-b border-gray-100">
                                            <td class="py-2">2024-06-18</td>
                                            <td class="py-2">李师傅</td>
                                            <td class="py-2">2.1小时</td>
                                            <td class="py-2"><span class="text-green-600">正常</span></td>
                                            <td class="py-2">更换了液压油</td>
                                        </tr>
                                        <tr class="border-b border-gray-100">
                                            <td class="py-2">2024-06-11</td>
                                            <td class="py-2">张师傅</td>
                                            <td class="py-2">1.9小时</td>
                                            <td class="py-2"><span class="text-yellow-600">异常</span></td>
                                            <td class="py-2">发现温度传感器松动</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 选择模板
        function selectTemplate(templateId) {
            console.log('选择模板:', templateId);
            // 这里可以添加模板选择的逻辑
        }
        
        // 选择计划
        function selectPlan(planId) {
            console.log('选择计划:', planId);
            document.getElementById('plan-detail').style.display = 'block';
        }
    </script>
</body>
</html>
