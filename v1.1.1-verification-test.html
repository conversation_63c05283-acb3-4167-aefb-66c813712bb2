<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>v1.1.1版本验证测试 - 数字工厂一体化平台</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-light': '#3b82f6',
                        'secondary': '#6b7280',
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'danger': '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">数字工厂一体化平台 v1.1.1 版本验证测试</h1>
            <p class="text-gray-600">验证业务中心重命名和基础平台功能扩展</p>
        </div>

        <!-- 版本信息 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">📋 版本信息</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">基本信息</h4>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li><strong>版本号</strong>: v1.1.1</li>
                            <li><strong>发布日期</strong>: 2025年1月17日</li>
                            <li><strong>版本状态</strong>: 功能扩展版本</li>
                            <li><strong>基于版本</strong>: v1.1.0</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">主要更新</h4>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li>🏢 业务中心重命名</li>
                            <li>🔗 基础平台功能扩展</li>
                            <li>🌐 外部系统集成</li>
                            <li>🎨 设计一致性保持</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试任务1：业务中心重命名 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">🏢 任务1：业务中心重命名验证</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">标题修改验证</h4>
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <p class="text-sm text-blue-800 mb-2"><strong>修改前</strong>：</p>
                            <p class="text-sm text-blue-700">【业务平台】</p>
                            <p class="text-sm text-blue-800 mb-2 mt-3"><strong>修改后</strong>：</p>
                            <p class="text-sm text-blue-700">【业务中心】</p>
                        </div>
                        <div class="mt-4">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" class="form-checkbox text-primary">
                                <span class="text-sm text-gray-700">首页模块标题显示为"业务中心"</span>
                            </label>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">功能保持验证</h4>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <p class="text-sm text-green-800 mb-2"><strong>保持不变</strong>：</p>
                            <ul class="text-sm text-green-700 space-y-1">
                                <li>• 模块图标（火箭图标）</li>
                                <li>• 卡片样式和布局</li>
                                <li>• 所有业务功能</li>
                                <li>• 点击跳转行为</li>
                            </ul>
                        </div>
                        <div class="mt-4">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" class="form-checkbox text-primary">
                                <span class="text-sm text-gray-700">图标、样式和功能完全保持不变</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试任务2：基础平台功能扩展 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">🔗 任务2：基础平台功能扩展验证</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">数据集成平台验证</h4>
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <p class="text-sm text-blue-800 mb-2"><strong>功能配置</strong>：</p>
                            <ul class="text-sm text-blue-700 space-y-1">
                                <li>• 名称：数据集成平台</li>
                                <li>• 描述：数据同步、接口管理、数据治理</li>
                                <li>• 图标：fas fa-exchange-alt</li>
                                <li>• 颜色：蓝色系渐变</li>
                                <li>• 链接：http://*************:8081/login.html</li>
                            </ul>
                        </div>
                        <div class="mt-4">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" class="form-checkbox text-primary">
                                <span class="text-sm text-gray-700">数据集成平台卡片正确显示</span>
                            </label>
                            <label class="flex items-center space-x-2 mt-2">
                                <input type="checkbox" class="form-checkbox text-primary">
                                <span class="text-sm text-gray-700">外部链接跳转正常工作</span>
                            </label>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">慧图云平台验证</h4>
                        <div class="bg-cyan-50 p-4 rounded-lg">
                            <p class="text-sm text-cyan-800 mb-2"><strong>功能配置</strong>：</p>
                            <ul class="text-sm text-cyan-700 space-y-1">
                                <li>• 名称：慧图云平台</li>
                                <li>• 描述：智能制图、云端协作、图形设计</li>
                                <li>• 图标：fas fa-cloud-upload-alt</li>
                                <li>• 颜色：青色系渐变</li>
                                <li>• 链接：https://imap.iimake.com/index/#/home</li>
                            </ul>
                        </div>
                        <div class="mt-4">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" class="form-checkbox text-primary">
                                <span class="text-sm text-gray-700">慧图云平台卡片正确显示</span>
                            </label>
                            <label class="flex items-center space-x-2 mt-2">
                                <input type="checkbox" class="form-checkbox text-primary">
                                <span class="text-sm text-gray-700">外部链接跳转正常工作</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试任务3：设计一致性验证 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">🎨 任务3：设计一致性验证</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">视觉风格一致性</h4>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <p class="text-sm text-purple-800 mb-2"><strong>检查项目</strong>：</p>
                            <ul class="text-sm text-purple-700 space-y-1">
                                <li>• 卡片尺寸和布局</li>
                                <li>• 圆角和阴影效果</li>
                                <li>• 字体大小和颜色</li>
                                <li>• 间距和对齐</li>
                            </ul>
                        </div>
                        <div class="mt-4">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" class="form-checkbox text-primary">
                                <span class="text-sm text-gray-700">新卡片与现有卡片风格一致</span>
                            </label>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">交互效果一致性</h4>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <p class="text-sm text-green-800 mb-2"><strong>检查项目</strong>：</p>
                            <ul class="text-sm text-green-700 space-y-1">
                                <li>• 悬停效果</li>
                                <li>• 点击反馈</li>
                                <li>• 过渡动画</li>
                                <li>• 按钮样式</li>
                            </ul>
                        </div>
                        <div class="mt-4">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" class="form-checkbox text-primary">
                                <span class="text-sm text-gray-700">交互效果与现有卡片一致</span>
                            </label>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-3">响应式设计</h4>
                        <div class="bg-orange-50 p-4 rounded-lg">
                            <p class="text-sm text-orange-800 mb-2"><strong>检查项目</strong>：</p>
                            <ul class="text-sm text-orange-700 space-y-1">
                                <li>• 桌面端显示</li>
                                <li>• 平板端适配</li>
                                <li>• 移动端布局</li>
                                <li>• 网格自适应</li>
                            </ul>
                        </div>
                        <div class="mt-4">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" class="form-checkbox text-primary">
                                <span class="text-sm text-gray-700">响应式设计正常工作</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试步骤 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">📋 测试步骤</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                        <div>
                            <h4 class="font-medium text-gray-800">打开平台主页</h4>
                            <p class="text-sm text-gray-600">访问 <a href="http://localhost:8081/" target="_blank" class="text-blue-600 hover:underline">http://localhost:8081/</a></p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                        <div>
                            <h4 class="font-medium text-gray-800">验证业务中心标题</h4>
                            <p class="text-sm text-gray-600">检查首页第一个模块的标题是否显示为"业务中心"</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                        <div>
                            <h4 class="font-medium text-gray-800">验证基础平台扩展</h4>
                            <p class="text-sm text-gray-600">检查基础平台模块是否包含5个功能卡片，包括新增的数据集成平台和慧图云平台</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">4</div>
                        <div>
                            <h4 class="font-medium text-gray-800">测试外部链接跳转</h4>
                            <p class="text-sm text-gray-600">点击数据集成平台和慧图云平台卡片，验证是否在新标签页正确打开外部链接</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">5</div>
                        <div>
                            <h4 class="font-medium text-gray-800">验证设计一致性</h4>
                            <p class="text-sm text-gray-600">检查新增卡片的视觉风格、交互效果和响应式设计是否与现有卡片保持一致</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速测试按钮 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">🚀 快速测试</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="window.open('http://localhost:8081/', '_blank')" 
                            class="p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-home mb-2"></i>
                        <div class="font-medium">打开平台主页</div>
                        <div class="text-xs opacity-80">验证业务中心和基础平台</div>
                    </button>
                    <button onclick="window.open('http://*************:8081/login.html', '_blank')" 
                            class="p-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-exchange-alt mb-2"></i>
                        <div class="font-medium">测试数据集成平台</div>
                        <div class="text-xs opacity-80">验证外部链接跳转</div>
                    </button>
                    <button onclick="window.open('https://imap.iimake.com/index/#/home', '_blank')" 
                            class="p-4 bg-cyan-600 text-white rounded-lg hover:bg-cyan-700 transition-colors">
                        <i class="fas fa-cloud-upload-alt mb-2"></i>
                        <div class="font-medium">测试慧图云平台</div>
                        <div class="text-xs opacity-80">验证外部链接跳转</div>
                    </button>
                </div>
            </div>
        </div>

        <!-- 验证结果总结 -->
        <div class="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6">
            <div class="flex items-start">
                <i class="fas fa-clipboard-check text-green-600 text-2xl mr-4 mt-1"></i>
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">v1.1.1版本验证清单</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h4 class="font-medium text-gray-800 mb-2">功能验证</h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>□ 业务中心标题修改正确</li>
                                <li>□ 数据集成平台卡片正确显示</li>
                                <li>□ 慧图云平台卡片正确显示</li>
                                <li>□ 外部链接跳转正常工作</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-800 mb-2">设计验证</h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>□ 视觉风格保持一致</li>
                                <li>□ 交互效果保持一致</li>
                                <li>□ 响应式设计正常</li>
                                <li>□ 企业级UI标准符合</li>
                            </ul>
                        </div>
                    </div>
                    <div class="mt-4 p-3 bg-white rounded-lg border border-green-200">
                        <p class="text-sm text-green-800">
                            <i class="fas fa-lightbulb mr-2"></i>
                            <strong>测试提示</strong>：请按照测试步骤逐项验证，确保所有功能都按照v1.1.1版本要求正确实现。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('v1.1.1版本验证测试页面已加载');
            console.log('请按照测试步骤验证所有功能');
        });
    </script>
</body>
</html>
